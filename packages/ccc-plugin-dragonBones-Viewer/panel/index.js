'use strict';

const Fs = require('fs');
const cp = require('child_process');
const { ipc<PERSON>ender<PERSON> } = require('electron');

var PATH = {
  html: Editor.url('packages://dragonbones-viewer/panel/list.html'),
  style: Editor.url('packages://dragonbones-viewer/panel/list.css'),
};

var createVM = function (elem) {
  return new Vue({
    el: elem,
    data: {
      items: [],
      filteredItems: [],
      searchQuery: "",
      path: "",
      flag: false
    },
    watch: {
      resources() {
        this.searchRes();
      },
    },
    methods: {
      filterItems() {
        const query = this.searchQuery.toLowerCase();
        
        // 如果搜索内容包含 _ske，则去掉后缀只搜索基础名称
        const searchTerm = query.includes('_ske') ? 
            query.substring(0, query.indexOf('_ske')) : 
            query;
        
        this.filteredItems = this.items.filter(item => {
            const urlLower = item.url.toLowerCase();
            const pathLower = item.path.toLowerCase();
            
            // 获取文件名（不包含路径）
            const fileName = pathLower.substring(pathLower.lastIndexOf('/') + 1);
            
            // 检查文件名中是否包含搜索词，但要确保它是作为一个完整的词出现
            // 比如搜索 "pink" 时，不应该匹配到只包含这些字母的其他词
            const parts = fileName.split('_');
            return parts.some(part => part.includes(searchTerm));
        });
      },

      goHub() {
        cp.exec('start https://github.com/krapnikkk/Creator-dragonBones-Viewer');
      },

      view(path) {
        this.path = path;
        if (!this.flag) {
          Editor.Panel.open('dragonbones-viewer.viewer');
        } else {
          Editor.Panel.close('dragonbones-viewer.viewer', () => {
            setTimeout(() => {
              this.view(this.path);
            }, 300);
            
          });
        }
      },

      send() {
        Editor.Ipc.sendToWins('view', this.path);
      },

      searchRes() {
        // 清理搜索框
        this.searchQuery = "";
        this.items = [];
        Editor.assetdb.queryAssets('db://assets/**/*', 'texture', (err, assetInfos) => {
          for (let i = 0; i < assetInfos.length; ++i) {
            let item = assetInfos[i], path = item["path"], url = item["url"];
            if (path.indexOf("_tex.png") > -1) {
              this.items.push({
                path: path,
                url: url
              });
            }
          }
          this.filteredItems = [...this.items];
        });
      },

      formatPath(path) {
        const lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex === -1) return path;
        
        const prefix = path.substring(0, lastSlashIndex + 1);
        const fileName = path.substring(lastSlashIndex + 1);
        
        let highlightedPrefix = this.escapeHtml(prefix);
        let highlightedFileName = this.escapeHtml(fileName);
        
        // 只有在有搜索内容时才进行高亮处理
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            const fileNameLower = fileName.toLowerCase();
            const index = fileNameLower.indexOf(query);
            
            if (index > -1) {
                // 将匹配的搜索词部分高亮为黄色
                const before = fileName.substring(0, index);
                const match = fileName.substring(index, index + query.length);
                const after = fileName.substring(index + query.length);
                
                if (fileName.endsWith('.png')) {
                    return `<span style="color: #b8b8b8">${highlightedPrefix}</span>` +
                           `<span style="color: #00ff9c">${before}</span>` +
                           `<span style="color: #ffeb3b">${match}</span>` +
                           `<span style="color: #00ff9c">${after}</span>`;
                } else {
                    return `<span style="color: #b8b8b8">${highlightedPrefix}</span>` +
                           `<span style="color: #b8b8b8">${before}</span>` +
                           `<span style="color: #ffeb3b">${match}</span>` +
                           `<span style="color: #b8b8b8">${after}</span>`;
                }
            }
        }
        
        // 如果没有搜索内容或没有匹配，使用默认颜色
        if (fileName.endsWith('.png')) {
            return `<span style="color: #b8b8b8">${highlightedPrefix}</span>` +
                   `<span style="color: #00ff9c">${highlightedFileName}</span>`;
        }
        return `<span style="color: #b8b8b8">${highlightedPrefix}${highlightedFileName}</span>`;
      },

      escapeHtml(text) {
        return text
          .replace(/&/g, "&amp;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
          .replace(/"/g, "&quot;")
          .replace(/'/g, "&#039;");
      },
    }
  });
};

// panel/index.js, this filename needs to match the one registered in package.json
Editor.Panel.extend({
  template: Fs.readFileSync(PATH.html, 'utf-8'),
  style: Fs.readFileSync(PATH.style, 'utf-8'),

  $: {
    'warp': '#warp'
  },

  ready() {
    this.vm = createVM(this.$warp);
    // 清理搜索框
    this.vm.searchQuery = "";
    this.vm.searchRes();
    ipcRenderer.on('init', (event, arg) => {
      this.vm.flag = true;
      this.vm.send();
    });
    ipcRenderer.on('close', (event, arg) => {
      this.vm.flag = false;
    })
  },
});