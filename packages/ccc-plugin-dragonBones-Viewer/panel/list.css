@import url('app://bower_components/fontawesome/css/font-awesome.min.css');
:host {
  display: flex;
  background: #2d2d2d;
  color: #e8e8e8;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

#warp {
  margin: 15px;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

section {
  background: #363636;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

section h4 {
  color: #00ff9c;
  margin-bottom: 10px;
  font-size: 16px;
}

section p {
  color: #b8b8b8;
  line-height: 1.6;
  margin-bottom: 5px;
}

.search-box {
  position: relative;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.search-box .search-label {
  color: #e8e8e8;
  margin-right: 10px;
  white-space: nowrap;
}

.search-box input {
  width: 60%;
  padding: 10px 15px 10px 35px;
  border: none;
  border-radius: 6px;
  background: #363636;
  color: #ffeb3b;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.search-box input:focus {
  background: #404040;
  box-shadow: 0 0 0 2px rgba(0,255,156,0.3);
}

.search-box .fa-search {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

section.form {
  flex: 1;
  border: none;
  padding: 0;
  overflow: auto;
}

ul {
  margin: 0;
  padding: 0;
  background: #363636;
  border-radius: 8px;
  overflow: hidden;
}

ul li {
  padding: 12px 15px;
  list-style: none;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #444;
  transition: background-color 0.2s ease;
}

ul li:hover {
  background-color: #404040;
}

ul li:last-child {
  border-bottom: none;
}

ul li > div {
  padding: 4px 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

ul li .path {
  flex: 1;
  color: #b8b8b8;
}

ul li .path .path-prefix {
  color: #b8b8b8;
}

ul li .path .file-name {
  color: #b8b8b8;
}

ul li .path .file-name[title$=".png"],
ul li .path:has(> .file-name:not([title])):has(span:last-child:contains(".png")) .file-name {
  color: #00ff9c;
}

ul li .operating {
  color: #00ff9c;
  font-weight: 500;
  width: 60px;
  text-align: center;
  margin-right: 0;
}

ul li .controller {
  width: 60px;
  text-align: center;
}

ul li .controller i {
  margin: 0;
  padding: 6px 12px;
  cursor: pointer;
  color: #00ff9c;
  background: #404040;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: inline-block;
  width: 60px;
  text-align: center;
}

ul li .controller i:hover {
  background: #505050;
  transform: translateY(-1px);
}

.help, .viewer {
  padding: 12px;
  background: #363636;
  border-radius: 6px;
  margin-top: 15px;
  transition: all 0.2s ease;
  text-align: center;
  color: #00ff9c;
}

.help:hover, .viewer:hover {
  background: #404040;
  transform: translateY(-1px);
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888;
}
