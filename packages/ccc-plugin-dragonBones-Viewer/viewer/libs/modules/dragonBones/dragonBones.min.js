"use strict";
var __extends = this && this.__extends ||
    function() {
        var t = Object.setPrototypeOf || {
                __proto__: []
            }
            instanceof Array &&
            function(t, e) {
                t.__proto__ = e
            } ||
            function(t, e) {
                for (var a in e) if (e.hasOwnProperty(a)) t[a] = e[a]
            };
        return function(e, a) {
            t(e, a);
            function i() {
                this.constructor = e
            }
            e.prototype = a === null ? Object.create(a) : (i.prototype = a.prototype, new i)
        }
    } ();
var dragonBones; (function(t) {})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function e(a) {
            this._clock = new t.WorldClock;
            this._events = [];
            this._objects = [];
            this._eventManager = null;
            this._eventManager = a;
            console.info("DragonBones: " + e.VERSION + "\nWebsite: http://dragonbones.com/\nSource and Demo: https://github.com/DragonBones/")
        }
        e.prototype.advanceTime = function(e) {
            if (this._objects.length > 0) {
                for (var a = 0,
                         i = this._objects; a < i.length; a++) {
                    var r = i[a];
                    r.returnToPool()
                }
                this._objects.length = 0
            }
            this._clock.advanceTime(e);
            if (this._events.length > 0) {
                for (var n = 0; n < this._events.length; ++n) {
                    var s = this._events[n];
                    var o = s.armature;
                    if (o._armatureData !== null) {
                        o.eventDispatcher.dispatchDBEvent(s.type, s);
                        if (s.type === t.EventObject.SOUND_EVENT) {
                            this._eventManager.dispatchDBEvent(s.type, s)
                        }
                    }
                    this.bufferObject(s)
                }
                this._events.length = 0
            }
        };
        e.prototype.bufferEvent = function(t) {
            if (this._events.indexOf(t) < 0) {
                this._events.push(t)
            }
        };
        e.prototype.bufferObject = function(t) {
            if (this._objects.indexOf(t) < 0) {
                this._objects.push(t)
            }
        };
        Object.defineProperty(e.prototype, "clock", {
            get: function() {
                return this._clock
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(e.prototype, "eventManager", {
            get: function() {
                return this._eventManager
            },
            enumerable: true,
            configurable: true
        });
        e.VERSION = "5.6.202";
        e.yDown = true;
        e.debug = false;
        e.debugDraw = false;
        e.webAssembly = false;
        return e
    } ();
    t.DragonBones = e
})(dragonBones || (dragonBones = {}));
if (typeof global === "undefined") {
    var global = window
}
if (!console.warn) {
    console.warn = function() {}
}
if (!console.assert) {
    console.assert = function() {}
}
if (!Date.now) {
    Date.now = function t() {
        return (new Date).getTime()
    }
}
var __extends = function(t, e) {
    function a() {
        this.constructor = t
    }
    for (var i in e) {
        if (e.hasOwnProperty(i)) {
            t[i] = e[i]
        }
    }
    a.prototype = e.prototype,
        t.prototype = new a
};
var dragonBones; (function(t) {
    var e = function() {
        function t() {
            this.hashCode = t._hashCode++;
            this._isInPool = false
        }
        t._returnObject = function(e) {
            var a = String(e.constructor);
            var i = a in t._maxCountMap ? t._maxCountMap[a] : t._defaultMaxCount;
            var r = t._poolsMap[a] = t._poolsMap[a] || [];
            if (r.length < i) {
                if (!e._isInPool) {
                    e._isInPool = true;
                    r.push(e)
                } else {
                    console.warn("The object is already in the pool.")
                }
            } else {}
        };
        t.toString = function() {
            throw new Error
        };
        t.setMaxCount = function(e, a) {
            if (a < 0 || a !== a) {
                a = 0
            }
            if (e !== null) {
                var i = String(e);
                var r = i in t._poolsMap ? t._poolsMap[i] : null;
                if (r !== null && r.length > a) {
                    r.length = a
                }
                t._maxCountMap[i] = a
            } else {
                t._defaultMaxCount = a;
                for (var i in t._poolsMap) {
                    var r = t._poolsMap[i];
                    if (r.length > a) {
                        r.length = a
                    }
                    if (i in t._maxCountMap) {
                        t._maxCountMap[i] = a
                    }
                }
            }
        };
        t.clearPool = function(e) {
            if (e === void 0) {
                e = null
            }
            if (e !== null) {
                var a = String(e);
                var i = a in t._poolsMap ? t._poolsMap[a] : null;
                if (i !== null && i.length > 0) {
                    i.length = 0
                }
            } else {
                for (var r in t._poolsMap) {
                    var i = t._poolsMap[r];
                    i.length = 0
                }
            }
        };
        t.borrowObject = function(e) {
            var a = String(e);
            var i = a in t._poolsMap ? t._poolsMap[a] : null;
            if (i !== null && i.length > 0) {
                var r = i.pop();
                r._isInPool = false;
                return r
            }
            var n = new e;
            n._onClear();
            return n
        };
        t.prototype.returnToPool = function() {
            this._onClear();
            t._returnObject(this)
        };
        t._hashCode = 0;
        t._defaultMaxCount = 3e3;
        t._maxCountMap = {};
        t._poolsMap = {};
        return t
    } ();
    t.BaseObject = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function t(t, e, a, i, r, n) {
            if (t === void 0) {
                t = 1
            }
            if (e === void 0) {
                e = 0
            }
            if (a === void 0) {
                a = 0
            }
            if (i === void 0) {
                i = 1
            }
            if (r === void 0) {
                r = 0
            }
            if (n === void 0) {
                n = 0
            }
            this.a = t;
            this.b = e;
            this.c = a;
            this.d = i;
            this.tx = r;
            this.ty = n
        }
        t.prototype.toString = function() {
            return "[object dragonBones.Matrix] a:" + this.a + " b:" + this.b + " c:" + this.c + " d:" + this.d + " tx:" + this.tx + " ty:" + this.ty
        };
        t.prototype.copyFrom = function(t) {
            this.a = t.a;
            this.b = t.b;
            this.c = t.c;
            this.d = t.d;
            this.tx = t.tx;
            this.ty = t.ty;
            return this
        };
        t.prototype.copyFromArray = function(t, e) {
            if (e === void 0) {
                e = 0
            }
            this.a = t[e];
            this.b = t[e + 1];
            this.c = t[e + 2];
            this.d = t[e + 3];
            this.tx = t[e + 4];
            this.ty = t[e + 5];
            return this
        };
        t.prototype.identity = function() {
            this.a = this.d = 1;
            this.b = this.c = 0;
            this.tx = this.ty = 0;
            return this
        };
        t.prototype.concat = function(t) {
            var e = this.a * t.a;
            var a = 0;
            var i = 0;
            var r = this.d * t.d;
            var n = this.tx * t.a + t.tx;
            var s = this.ty * t.d + t.ty;
            if (this.b !== 0 || this.c !== 0) {
                e += this.b * t.c;
                a += this.b * t.d;
                i += this.c * t.a;
                r += this.c * t.b
            }
            if (t.b !== 0 || t.c !== 0) {
                a += this.a * t.b;
                i += this.d * t.c;
                n += this.ty * t.c;
                s += this.tx * t.b
            }
            this.a = e;
            this.b = a;
            this.c = i;
            this.d = r;
            this.tx = n;
            this.ty = s;
            return this
        };
        t.prototype.invert = function() {
            var t = this.a;
            var e = this.b;
            var a = this.c;
            var i = this.d;
            var r = this.tx;
            var n = this.ty;
            if (e === 0 && a === 0) {
                this.b = this.c = 0;
                if (t === 0 || i === 0) {
                    this.a = this.b = this.tx = this.ty = 0
                } else {
                    t = this.a = 1 / t;
                    i = this.d = 1 / i;
                    this.tx = -t * r;
                    this.ty = -i * n
                }
                return this
            }
            var s = t * i - e * a;
            if (s === 0) {
                this.a = this.d = 1;
                this.b = this.c = 0;
                this.tx = this.ty = 0;
                return this
            }
            s = 1 / s;
            var o = this.a = i * s;
            e = this.b = -e * s;
            a = this.c = -a * s;
            i = this.d = t * s;
            this.tx = -(o * r + a * n);
            this.ty = -(e * r + i * n);
            return this
        };
        t.prototype.transformPoint = function(t, e, a, i) {
            if (i === void 0) {
                i = false
            }
            a.x = this.a * t + this.c * e;
            a.y = this.b * t + this.d * e;
            if (!i) {
                a.x += this.tx;
                a.y += this.ty
            }
        };
        t.prototype.transformRectangle = function(t, e) {
            if (e === void 0) {
                e = false
            }
            var a = this.a;
            var i = this.b;
            var r = this.c;
            var n = this.d;
            var s = e ? 0 : this.tx;
            var o = e ? 0 : this.ty;
            var l = t.x;
            var h = t.y;
            var f = l + t.width;
            var u = h + t.height;
            var _ = a * l + r * h + s;
            var c = i * l + n * h + o;
            var m = a * f + r * h + s;
            var p = i * f + n * h + o;
            var d = a * f + r * u + s;
            var y = i * f + n * u + o;
            var g = a * l + r * u + s;
            var v = i * l + n * u + o;
            var b = 0;
            if (_ > m) {
                b = _;
                _ = m;
                m = b
            }
            if (d > g) {
                b = d;
                d = g;
                g = b
            }
            t.x = Math.floor(_ < d ? _: d);
            t.width = Math.ceil((m > g ? m: g) - t.x);
            if (c > p) {
                b = c;
                c = p;
                p = b
            }
            if (y > v) {
                b = y;
                y = v;
                v = b
            }
            t.y = Math.floor(c < y ? c: y);
            t.height = Math.ceil((p > v ? p: v) - t.y)
        };
        return t
    } ();
    t.Matrix = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function t(t, e, a, i, r, n) {
            if (t === void 0) {
                t = 0
            }
            if (e === void 0) {
                e = 0
            }
            if (a === void 0) {
                a = 0
            }
            if (i === void 0) {
                i = 0
            }
            if (r === void 0) {
                r = 1
            }
            if (n === void 0) {
                n = 1
            }
            this.x = t;
            this.y = e;
            this.skew = a;
            this.rotation = i;
            this.scaleX = r;
            this.scaleY = n
        }
        t.normalizeRadian = function(t) {
            t = (t + Math.PI) % (Math.PI * 2);
            t += t > 0 ? -Math.PI: Math.PI;
            return t
        };
        t.prototype.toString = function() {
            return "[object dragonBones.Transform] x:" + this.x + " y:" + this.y + " skewX:" + this.skew * 180 / Math.PI + " skewY:" + this.rotation * 180 / Math.PI + " scaleX:" + this.scaleX + " scaleY:" + this.scaleY
        };
        t.prototype.copyFrom = function(t) {
            this.x = t.x;
            this.y = t.y;
            this.skew = t.skew;
            this.rotation = t.rotation;
            this.scaleX = t.scaleX;
            this.scaleY = t.scaleY;
            return this
        };
        t.prototype.identity = function() {
            this.x = this.y = 0;
            this.skew = this.rotation = 0;
            this.scaleX = this.scaleY = 1;
            return this
        };
        t.prototype.add = function(t) {
            this.x += t.x;
            this.y += t.y;
            this.skew += t.skew;
            this.rotation += t.rotation;
            this.scaleX *= t.scaleX;
            this.scaleY *= t.scaleY;
            return this
        };
        t.prototype.minus = function(t) {
            this.x -= t.x;
            this.y -= t.y;
            this.skew -= t.skew;
            this.rotation -= t.rotation;
            this.scaleX /= t.scaleX;
            this.scaleY /= t.scaleY;
            return this
        };
        t.prototype.fromMatrix = function(e) {
            var a = this.scaleX,
                i = this.scaleY;
            var r = t.PI_Q;
            this.x = e.tx;
            this.y = e.ty;
            this.rotation = Math.atan(e.b / e.a);
            var n = Math.atan( - e.c / e.d);
            this.scaleX = this.rotation > -r && this.rotation < r ? e.a / Math.cos(this.rotation) : e.b / Math.sin(this.rotation);
            this.scaleY = n > -r && n < r ? e.d / Math.cos(n) : -e.c / Math.sin(n);
            if (a >= 0 && this.scaleX < 0) {
                this.scaleX = -this.scaleX;
                this.rotation = this.rotation - Math.PI
            }
            if (i >= 0 && this.scaleY < 0) {
                this.scaleY = -this.scaleY;
                n = n - Math.PI
            }
            this.skew = n - this.rotation;
            return this
        };
        t.prototype.toMatrix = function(t) {
            if (this.rotation === 0) {
                t.a = 1;
                t.b = 0
            } else {
                t.a = Math.cos(this.rotation);
                t.b = Math.sin(this.rotation)
            }
            if (this.skew === 0) {
                t.c = -t.b;
                t.d = t.a
            } else {
                t.c = -Math.sin(this.skew + this.rotation);
                t.d = Math.cos(this.skew + this.rotation)
            }
            if (this.scaleX !== 1) {
                t.a *= this.scaleX;
                t.b *= this.scaleX
            }
            if (this.scaleY !== 1) {
                t.c *= this.scaleY;
                t.d *= this.scaleY
            }
            t.tx = this.x;
            t.ty = this.y;
            return this
        };
        t.PI = Math.PI;
        t.PI_D = Math.PI * 2;
        t.PI_H = Math.PI / 2;
        t.PI_Q = Math.PI / 4;
        t.RAD_DEG = 180 / Math.PI;
        t.DEG_RAD = Math.PI / 180;
        return t
    } ();
    t.Transform = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function t(t, e, a, i, r, n, s, o) {
            if (t === void 0) {
                t = 1
            }
            if (e === void 0) {
                e = 1
            }
            if (a === void 0) {
                a = 1
            }
            if (i === void 0) {
                i = 1
            }
            if (r === void 0) {
                r = 0
            }
            if (n === void 0) {
                n = 0
            }
            if (s === void 0) {
                s = 0
            }
            if (o === void 0) {
                o = 0
            }
            this.alphaMultiplier = t;
            this.redMultiplier = e;
            this.greenMultiplier = a;
            this.blueMultiplier = i;
            this.alphaOffset = r;
            this.redOffset = n;
            this.greenOffset = s;
            this.blueOffset = o
        }
        t.prototype.copyFrom = function(t) {
            this.alphaMultiplier = t.alphaMultiplier;
            this.redMultiplier = t.redMultiplier;
            this.greenMultiplier = t.greenMultiplier;
            this.blueMultiplier = t.blueMultiplier;
            this.alphaOffset = t.alphaOffset;
            this.redOffset = t.redOffset;
            this.greenOffset = t.greenOffset;
            this.blueOffset = t.blueOffset
        };
        t.prototype.identity = function() {
            this.alphaMultiplier = this.redMultiplier = this.greenMultiplier = this.blueMultiplier = 1;
            this.alphaOffset = this.redOffset = this.greenOffset = this.blueOffset = 0
        };
        return t
    } ();
    t.ColorTransform = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function t(t, e) {
            if (t === void 0) {
                t = 0
            }
            if (e === void 0) {
                e = 0
            }
            this.x = t;
            this.y = e
        }
        t.prototype.copyFrom = function(t) {
            this.x = t.x;
            this.y = t.y
        };
        t.prototype.clear = function() {
            this.x = this.y = 0
        };
        return t
    } ();
    t.Point = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function t(t, e, a, i) {
            if (t === void 0) {
                t = 0
            }
            if (e === void 0) {
                e = 0
            }
            if (a === void 0) {
                a = 0
            }
            if (i === void 0) {
                i = 0
            }
            this.x = t;
            this.y = e;
            this.width = a;
            this.height = i
        }
        t.prototype.copyFrom = function(t) {
            this.x = t.x;
            this.y = t.y;
            this.width = t.width;
            this.height = t.height
        };
        t.prototype.clear = function() {
            this.x = this.y = 0;
            this.width = this.height = 0
        };
        return t
    } ();
    t.Rectangle = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.ints = [];
            e.floats = [];
            e.strings = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.UserData]"
        };
        e.prototype._onClear = function() {
            this.ints.length = 0;
            this.floats.length = 0;
            this.strings.length = 0
        };
        e.prototype.addInt = function(t) {
            this.ints.push(t)
        };
        e.prototype.addFloat = function(t) {
            this.floats.push(t)
        };
        e.prototype.addString = function(t) {
            this.strings.push(t)
        };
        e.prototype.getInt = function(t) {
            if (t === void 0) {
                t = 0
            }
            return t >= 0 && t < this.ints.length ? this.ints[t] : 0
        };
        e.prototype.getFloat = function(t) {
            if (t === void 0) {
                t = 0
            }
            return t >= 0 && t < this.floats.length ? this.floats[t] : 0
        };
        e.prototype.getString = function(t) {
            if (t === void 0) {
                t = 0
            }
            return t >= 0 && t < this.strings.length ? this.strings[t] : ""
        };
        return e
    } (t.BaseObject);
    t.UserData = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.data = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.ActionData]"
        };
        e.prototype._onClear = function() {
            if (this.data !== null) {
                this.data.returnToPool()
            }
            this.type = 0;
            this.name = "";
            this.bone = null;
            this.slot = null;
            this.data = null
        };
        return e
    } (t.BaseObject);
    t.ActionData = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.frameIndices = [];
            e.cachedFrames = [];
            e.armatureNames = [];
            e.armatures = {};
            e.userData = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.DragonBonesData]"
        };
        e.prototype._onClear = function() {
            for (var t in this.armatures) {
                this.armatures[t].returnToPool();
                delete this.armatures[t]
            }
            if (this.userData !== null) {
                this.userData.returnToPool()
            }
            this.autoSearch = false;
            this.frameRate = 0;
            this.version = "";
            this.name = "";
            this.stage = null;
            this.frameIndices.length = 0;
            this.cachedFrames.length = 0;
            this.armatureNames.length = 0;
            this.binary = null;
            this.intArray = null;
            this.floatArray = null;
            this.frameIntArray = null;
            this.frameFloatArray = null;
            this.frameArray = null;
            this.timelineArray = null;
            this.userData = null
        };
        e.prototype.addArmature = function(t) {
            if (t.name in this.armatures) {
                console.warn("Same armature: " + t.name);
                return
            }
            t.parent = this;
            this.armatures[t.name] = t;
            this.armatureNames.push(t.name)
        };
        e.prototype.getArmature = function(t) {
            return t in this.armatures ? this.armatures[t] : null
        };
        e.prototype.dispose = function() {
            console.warn("已废弃");
            this.returnToPool()
        };
        return e
    } (t.BaseObject);
    t.DragonBonesData = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.aabb = new t.Rectangle;
            a.animationNames = [];
            a.sortedBones = [];
            a.sortedSlots = [];
            a.defaultActions = [];
            a.actions = [];
            a.bones = {};
            a.slots = {};
            a.constraints = {};
            a.skins = {};
            a.animations = {};
            a.canvas = null;
            a.userData = null;
            return a
        }
        a.toString = function() {
            return "[class dragonBones.ArmatureData]"
        };
        a.prototype._onClear = function() {
            for (var t = 0,
                     e = this.defaultActions; t < e.length; t++) {
                var a = e[t];
                a.returnToPool()
            }
            for (var i = 0,
                     r = this.actions; i < r.length; i++) {
                var a = r[i];
                a.returnToPool()
            }
            for (var n in this.bones) {
                this.bones[n].returnToPool();
                delete this.bones[n]
            }
            for (var n in this.slots) {
                this.slots[n].returnToPool();
                delete this.slots[n]
            }
            for (var n in this.constraints) {
                this.constraints[n].returnToPool();
                delete this.constraints[n]
            }
            for (var n in this.skins) {
                this.skins[n].returnToPool();
                delete this.skins[n]
            }
            for (var n in this.animations) {
                this.animations[n].returnToPool();
                delete this.animations[n]
            }
            if (this.canvas !== null) {
                this.canvas.returnToPool()
            }
            if (this.userData !== null) {
                this.userData.returnToPool()
            }
            this.type = 0;
            this.frameRate = 0;
            this.cacheFrameRate = 0;
            this.scale = 1;
            this.name = "";
            this.aabb.clear();
            this.animationNames.length = 0;
            this.sortedBones.length = 0;
            this.sortedSlots.length = 0;
            this.defaultActions.length = 0;
            this.actions.length = 0;
            this.defaultSkin = null;
            this.defaultAnimation = null;
            this.canvas = null;
            this.userData = null;
            this.parent = null
        };
        a.prototype.sortBones = function() {
            var t = this.sortedBones.length;
            if (t <= 0) {
                return
            }
            var e = this.sortedBones.concat();
            var a = 0;
            var i = 0;
            this.sortedBones.length = 0;
            while (i < t) {
                var r = e[a++];
                if (a >= t) {
                    a = 0
                }
                if (this.sortedBones.indexOf(r) >= 0) {
                    continue
                }
                var n = false;
                for (var s in this.constraints) {
                    var o = this.constraints[s];
                    if (o.root === r && this.sortedBones.indexOf(o.target) < 0) {
                        n = true;
                        break
                    }
                }
                if (n) {
                    continue
                }
                if (r.parent !== null && this.sortedBones.indexOf(r.parent) < 0) {
                    continue
                }
                this.sortedBones.push(r);
                i++
            }
        };
        a.prototype.cacheFrames = function(t) {
            if (this.cacheFrameRate > 0) {
                return
            }
            this.cacheFrameRate = t;
            for (var e in this.animations) {
                this.animations[e].cacheFrames(this.cacheFrameRate)
            }
        };
        a.prototype.setCacheFrame = function(t, e) {
            var a = this.parent.cachedFrames;
            var i = a.length;
            a.length += 10;
            a[i] = t.a;
            a[i + 1] = t.b;
            a[i + 2] = t.c;
            a[i + 3] = t.d;
            a[i + 4] = t.tx;
            a[i + 5] = t.ty;
            a[i + 6] = e.rotation;
            a[i + 7] = e.skew;
            a[i + 8] = e.scaleX;
            a[i + 9] = e.scaleY;
            return i
        };
        a.prototype.getCacheFrame = function(t, e, a) {
            var i = this.parent.cachedFrames;
            t.a = i[a];
            t.b = i[a + 1];
            t.c = i[a + 2];
            t.d = i[a + 3];
            t.tx = i[a + 4];
            t.ty = i[a + 5];
            e.rotation = i[a + 6];
            e.skew = i[a + 7];
            e.scaleX = i[a + 8];
            e.scaleY = i[a + 9];
            e.x = t.tx;
            e.y = t.ty
        };
        a.prototype.addBone = function(t) {
            if (t.name in this.bones) {
                console.warn("Same bone: " + t.name);
                return
            }
            this.bones[t.name] = t;
            this.sortedBones.push(t)
        };
        a.prototype.addSlot = function(t) {
            if (t.name in this.slots) {
                console.warn("Same slot: " + t.name);
                return
            }
            this.slots[t.name] = t;
            this.sortedSlots.push(t)
        };
        a.prototype.addConstraint = function(t) {
            if (t.name in this.constraints) {
                console.warn("Same constraint: " + t.name);
                return
            }
            this.constraints[t.name] = t
        };
        a.prototype.addSkin = function(t) {
            if (t.name in this.skins) {
                console.warn("Same skin: " + t.name);
                return
            }
            t.parent = this;
            this.skins[t.name] = t;
            if (this.defaultSkin === null) {
                this.defaultSkin = t
            }
            if (t.name === "default") {
                this.defaultSkin = t
            }
        };
        a.prototype.addAnimation = function(t) {
            if (t.name in this.animations) {
                console.warn("Same animation: " + t.name);
                return
            }
            t.parent = this;
            this.animations[t.name] = t;
            this.animationNames.push(t.name);
            if (this.defaultAnimation === null) {
                this.defaultAnimation = t
            }
        };
        a.prototype.addAction = function(t, e) {
            if (e) {
                this.defaultActions.push(t)
            } else {
                this.actions.push(t)
            }
        };
        a.prototype.getBone = function(t) {
            return t in this.bones ? this.bones[t] : null
        };
        a.prototype.getSlot = function(t) {
            return t in this.slots ? this.slots[t] : null
        };
        a.prototype.getConstraint = function(t) {
            return t in this.constraints ? this.constraints[t] : null
        };
        a.prototype.getSkin = function(t) {
            return t in this.skins ? this.skins[t] : null
        };
        a.prototype.getMesh = function(t, e, a) {
            var i = this.getSkin(t);
            if (i === null) {
                return null
            }
            return i.getDisplay(e, a)
        };
        a.prototype.getAnimation = function(t) {
            return t in this.animations ? this.animations[t] : null
        };
        return a
    } (t.BaseObject);
    t.ArmatureData = e;
    var a = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.transform = new t.Transform;
            a.userData = null;
            return a
        }
        a.toString = function() {
            return "[class dragonBones.BoneData]"
        };
        a.prototype._onClear = function() {
            if (this.userData !== null) {
                this.userData.returnToPool()
            }
            this.inheritTranslation = false;
            this.inheritRotation = false;
            this.inheritScale = false;
            this.inheritReflection = false;
            this.type = 0;
            this.length = 0;
            this.name = "";
            this.transform.identity();
            this.userData = null;
            this.parent = null
        };
        return a
    } (t.BaseObject);
    t.BoneData = a;
    var i = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.vertices = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.SurfaceData]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.type = 1;
            this.segmentX = 0;
            this.segmentY = 0;
            this.vertices.length = 0
        };
        return e
    } (a);
    t.SurfaceData = i;
    var r = function(e) {
        __extends(a, e);
        function a() {
            var t = e !== null && e.apply(this, arguments) || this;
            t.color = null;
            t.userData = null;
            return t
        }
        a.createColor = function() {
            return new t.ColorTransform
        };
        a.toString = function() {
            return "[class dragonBones.SlotData]"
        };
        a.prototype._onClear = function() {
            if (this.userData !== null) {
                this.userData.returnToPool()
            }
            this.blendMode = 0;
            this.displayIndex = 0;
            this.zOrder = 0;
            this.name = "";
            this.color = null;
            this.userData = null;
            this.parent = null
        };
        a.DEFAULT_COLOR = new t.ColorTransform;
        return a
    } (t.BaseObject);
    t.SlotData = r
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.CanvasData]"
        };
        e.prototype._onClear = function() {
            this.hasBackground = false;
            this.color = 0;
            this.x = 0;
            this.y = 0;
            this.width = 0;
            this.height = 0
        };
        return e
    } (t.BaseObject);
    t.CanvasData = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.displays = {};
            return e
        }
        e.toString = function() {
            return "[class dragonBones.SkinData]"
        };
        e.prototype._onClear = function() {
            for (var t in this.displays) {
                var e = this.displays[t];
                for (var a = 0,
                         i = e; a < i.length; a++) {
                    var r = i[a];
                    if (r !== null) {
                        r.returnToPool()
                    }
                }
                delete this.displays[t]
            }
            this.name = "";
            this.parent = null
        };
        e.prototype.addDisplay = function(t, e) {
            if (! (t in this.displays)) {
                this.displays[t] = []
            }
            if (e !== null) {
                e.parent = this
            }
            var a = this.displays[t];
            a.push(e)
        };
        e.prototype.getDisplay = function(t, e) {
            var a = this.getDisplays(t);
            if (a !== null) {
                for (var i = 0,
                         r = a; i < r.length; i++) {
                    var n = r[i];
                    if (n !== null && n.name === e) {
                        return n
                    }
                }
            }
            return null
        };
        e.prototype.getDisplays = function(t) {
            if (! (t in this.displays)) {
                return null
            }
            return this.displays[t]
        };
        return e
    } (t.BaseObject);
    t.SkinData = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.prototype._onClear = function() {
            this.order = 0;
            this.name = "";
            this.target = null;
            this.root = null;
            this.bone = null
        };
        return e
    } (t.BaseObject);
    t.ConstraintData = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.IKConstraintData]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.scaleEnabled = false;
            this.bendPositive = false;
            this.weight = 1
        };
        return e
    } (e);
    t.IKConstraintData = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.transform = new t.Transform;
            return a
        }
        a.prototype._onClear = function() {
            this.name = "";
            this.path = "";
            this.transform.identity();
            this.parent = null
        };
        return a
    } (t.BaseObject);
    t.DisplayData = e;
    var a = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.pivot = new t.Point;
            return a
        }
        a.toString = function() {
            return "[class dragonBones.ImageDisplayData]"
        };
        a.prototype._onClear = function() {
            e.prototype._onClear.call(this);
            this.type = 0;
            this.pivot.clear();
            this.texture = null
        };
        return a
    } (e);
    t.ImageDisplayData = a;
    var i = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.actions = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.ArmatureDisplayData]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            for (var e = 0,
                     a = this.actions; e < a.length; e++) {
                var i = a[e];
                i.returnToPool()
            }
            this.type = 1;
            this.inheritAnimation = false;
            this.actions.length = 0;
            this.armature = null
        };
        e.prototype.addAction = function(t) {
            this.actions.push(t)
        };
        return e
    } (e);
    t.ArmatureDisplayData = i;
    var r = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.weight = null;
            e.glue = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.MeshDisplayData]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            if (this.weight !== null) {
                this.weight.returnToPool()
            }
            if (this.glue !== null) {
                this.glue.returnToPool()
            }
            this.type = 2;
            this.inheritDeform = false;
            this.offset = 0;
            this.weight = null;
            this.glue = null;
            this.texture = null
        };
        return e
    } (e);
    t.MeshDisplayData = r;
    var n = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.boundingBox = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.BoundingBoxDisplayData]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            if (this.boundingBox !== null) {
                this.boundingBox.returnToPool()
            }
            this.type = 3;
            this.boundingBox = null
        };
        return e
    } (e);
    t.BoundingBoxDisplayData = n;
    var s = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.bones = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.WeightData]"
        };
        e.prototype._onClear = function() {
            this.count = 0;
            this.offset = 0;
            this.bones.length = 0
        };
        e.prototype.addBone = function(t) {
            this.bones.push(t)
        };
        return e
    } (t.BaseObject);
    t.WeightData = s;
    var o = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.meshes = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.GlueData]"
        };
        e.prototype._onClear = function() {
            this.weights.length = 0;
            this.meshes.length = 0
        };
        e.prototype.addMesh = function(t) {
            this.meshes.push(t)
        };
        return e
    } (t.BaseObject);
    t.GlueData = o
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.prototype._onClear = function() {
            this.color = 0;
            this.width = 0;
            this.height = 0
        };
        return e
    } (t.BaseObject);
    t.BoundingBoxData = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.RectangleBoundingBoxData]"
        };
        e._computeOutCode = function(t, e, a, i, r, n) {
            var s = 0;
            if (t < a) {
                s |= 1
            } else if (t > r) {
                s |= 2
            }
            if (e < i) {
                s |= 4
            } else if (e > n) {
                s |= 8
            }
            return s
        };
        e.rectangleIntersectsSegment = function(t, a, i, r, n, s, o, l, h, f, u) {
            if (h === void 0) {
                h = null
            }
            if (f === void 0) {
                f = null
            }
            if (u === void 0) {
                u = null
            }
            var _ = t > n && t < o && a > s && a < l;
            var c = i > n && i < o && r > s && r < l;
            if (_ && c) {
                return - 1
            }
            var m = 0;
            var p = e._computeOutCode(t, a, n, s, o, l);
            var d = e._computeOutCode(i, r, n, s, o, l);
            while (true) {
                if ((p | d) === 0) {
                    m = 2;
                    break
                } else if ((p & d) !== 0) {
                    break
                }
                var y = 0;
                var g = 0;
                var v = 0;
                var b = p !== 0 ? p: d;
                if ((b & 4) !== 0) {
                    y = t + (i - t) * (s - a) / (r - a);
                    g = s;
                    if (u !== null) {
                        v = -Math.PI * .5
                    }
                } else if ((b & 8) !== 0) {
                    y = t + (i - t) * (l - a) / (r - a);
                    g = l;
                    if (u !== null) {
                        v = Math.PI * .5
                    }
                } else if ((b & 2) !== 0) {
                    g = a + (r - a) * (o - t) / (i - t);
                    y = o;
                    if (u !== null) {
                        v = 0
                    }
                } else if ((b & 1) !== 0) {
                    g = a + (r - a) * (n - t) / (i - t);
                    y = n;
                    if (u !== null) {
                        v = Math.PI
                    }
                }
                if (b === p) {
                    t = y;
                    a = g;
                    p = e._computeOutCode(t, a, n, s, o, l);
                    if (u !== null) {
                        u.x = v
                    }
                } else {
                    i = y;
                    r = g;
                    d = e._computeOutCode(i, r, n, s, o, l);
                    if (u !== null) {
                        u.y = v
                    }
                }
            }
            if (m) {
                if (_) {
                    m = 2;
                    if (h !== null) {
                        h.x = i;
                        h.y = r
                    }
                    if (f !== null) {
                        f.x = i;
                        f.y = i
                    }
                    if (u !== null) {
                        u.x = u.y + Math.PI
                    }
                } else if (c) {
                    m = 1;
                    if (h !== null) {
                        h.x = t;
                        h.y = a
                    }
                    if (f !== null) {
                        f.x = t;
                        f.y = a
                    }
                    if (u !== null) {
                        u.y = u.x + Math.PI
                    }
                } else {
                    m = 3;
                    if (h !== null) {
                        h.x = t;
                        h.y = a
                    }
                    if (f !== null) {
                        f.x = i;
                        f.y = r
                    }
                }
            }
            return m
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.type = 0
        };
        e.prototype.containsPoint = function(t, e) {
            var a = this.width * .5;
            if (t >= -a && t <= a) {
                var i = this.height * .5;
                if (e >= -i && e <= i) {
                    return true
                }
            }
            return false
        };
        e.prototype.intersectsSegment = function(t, a, i, r, n, s, o) {
            if (n === void 0) {
                n = null
            }
            if (s === void 0) {
                s = null
            }
            if (o === void 0) {
                o = null
            }
            var l = this.width * .5;
            var h = this.height * .5;
            var f = e.rectangleIntersectsSegment(t, a, i, r, -l, -h, l, h, n, s, o);
            return f
        };
        return e
    } (e);
    t.RectangleBoundingBoxData = a;
    var i = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.EllipseData]"
        };
        e.ellipseIntersectsSegment = function(t, e, a, i, r, n, s, o, l, h, f) {
            if (l === void 0) {
                l = null
            }
            if (h === void 0) {
                h = null
            }
            if (f === void 0) {
                f = null
            }
            var u = s / o;
            var _ = u * u;
            e *= u;
            i *= u;
            var c = a - t;
            var m = i - e;
            var p = Math.sqrt(c * c + m * m);
            var d = c / p;
            var y = m / p;
            var g = (r - t) * d + (n - e) * y;
            var v = g * g;
            var b = t * t + e * e;
            var D = s * s;
            var T = D - b + v;
            var A = 0;
            if (T >= 0) {
                var x = Math.sqrt(T);
                var P = g - x;
                var O = g + x;
                var S = P < 0 ? -1 : P <= p ? 0 : 1;
                var E = O < 0 ? -1 : O <= p ? 0 : 1;
                var B = S * E;
                if (B < 0) {
                    return - 1
                } else if (B === 0) {
                    if (S === -1) {
                        A = 2;
                        a = t + O * d;
                        i = (e + O * y) / u;
                        if (l !== null) {
                            l.x = a;
                            l.y = i
                        }
                        if (h !== null) {
                            h.x = a;
                            h.y = i
                        }
                        if (f !== null) {
                            f.x = Math.atan2(i / D * _, a / D);
                            f.y = f.x + Math.PI
                        }
                    } else if (E === 1) {
                        A = 1;
                        t = t + P * d;
                        e = (e + P * y) / u;
                        if (l !== null) {
                            l.x = t;
                            l.y = e
                        }
                        if (h !== null) {
                            h.x = t;
                            h.y = e
                        }
                        if (f !== null) {
                            f.x = Math.atan2(e / D * _, t / D);
                            f.y = f.x + Math.PI
                        }
                    } else {
                        A = 3;
                        if (l !== null) {
                            l.x = t + P * d;
                            l.y = (e + P * y) / u;
                            if (f !== null) {
                                f.x = Math.atan2(l.y / D * _, l.x / D)
                            }
                        }
                        if (h !== null) {
                            h.x = t + O * d;
                            h.y = (e + O * y) / u;
                            if (f !== null) {
                                f.y = Math.atan2(h.y / D * _, h.x / D)
                            }
                        }
                    }
                }
            }
            return A
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.type = 1
        };
        e.prototype.containsPoint = function(t, e) {
            var a = this.width * .5;
            if (t >= -a && t <= a) {
                var i = this.height * .5;
                if (e >= -i && e <= i) {
                    e *= a / i;
                    return Math.sqrt(t * t + e * e) <= a
                }
            }
            return false
        };
        e.prototype.intersectsSegment = function(t, a, i, r, n, s, o) {
            if (n === void 0) {
                n = null
            }
            if (s === void 0) {
                s = null
            }
            if (o === void 0) {
                o = null
            }
            var l = e.ellipseIntersectsSegment(t, a, i, r, 0, 0, this.width * .5, this.height * .5, n, s, o);
            return l
        };
        return e
    } (e);
    t.EllipseBoundingBoxData = i;
    var r = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.vertices = [];
            e.weight = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.PolygonBoundingBoxData]"
        };
        e.polygonIntersectsSegment = function(t, e, a, i, r, n, s, o) {
            if (n === void 0) {
                n = null
            }
            if (s === void 0) {
                s = null
            }
            if (o === void 0) {
                o = null
            }
            if (t === a) {
                t = a + 1e-6
            }
            if (e === i) {
                e = i + 1e-6
            }
            var l = r.length;
            var h = t - a;
            var f = e - i;
            var u = t * i - e * a;
            var _ = 0;
            var c = r[l - 2];
            var m = r[l - 1];
            var p = 0;
            var d = 0;
            var y = 0;
            var g = 0;
            var v = 0;
            var b = 0;
            for (var D = 0; D < l; D += 2) {
                var T = r[D];
                var A = r[D + 1];
                if (c === T) {
                    c = T + 1e-4
                }
                if (m === A) {
                    m = A + 1e-4
                }
                var x = c - T;
                var P = m - A;
                var O = c * A - m * T;
                var S = h * P - f * x;
                var E = (u * x - h * O) / S;
                if ((E >= c && E <= T || E >= T && E <= c) && (h === 0 || E >= t && E <= a || E >= a && E <= t)) {
                    var B = (u * P - f * O) / S;
                    if ((B >= m && B <= A || B >= A && B <= m) && (f === 0 || B >= e && B <= i || B >= i && B <= e)) {
                        if (s !== null) {
                            var M = E - t;
                            if (M < 0) {
                                M = -M
                            }
                            if (_ === 0) {
                                p = M;
                                d = M;
                                y = E;
                                g = B;
                                v = E;
                                b = B;
                                if (o !== null) {
                                    o.x = Math.atan2(A - m, T - c) - Math.PI * .5;
                                    o.y = o.x
                                }
                            } else {
                                if (M < p) {
                                    p = M;
                                    y = E;
                                    g = B;
                                    if (o !== null) {
                                        o.x = Math.atan2(A - m, T - c) - Math.PI * .5
                                    }
                                }
                                if (M > d) {
                                    d = M;
                                    v = E;
                                    b = B;
                                    if (o !== null) {
                                        o.y = Math.atan2(A - m, T - c) - Math.PI * .5
                                    }
                                }
                            }
                            _++
                        } else {
                            y = E;
                            g = B;
                            v = E;
                            b = B;
                            _++;
                            if (o !== null) {
                                o.x = Math.atan2(A - m, T - c) - Math.PI * .5;
                                o.y = o.x
                            }
                            break
                        }
                    }
                }
                c = T;
                m = A
            }
            if (_ === 1) {
                if (n !== null) {
                    n.x = y;
                    n.y = g
                }
                if (s !== null) {
                    s.x = y;
                    s.y = g
                }
                if (o !== null) {
                    o.y = o.x + Math.PI
                }
            } else if (_ > 1) {
                _++;
                if (n !== null) {
                    n.x = y;
                    n.y = g
                }
                if (s !== null) {
                    s.x = v;
                    s.y = b
                }
            }
            return _
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            if (this.weight !== null) {
                this.weight.returnToPool()
            }
            this.type = 2;
            this.x = 0;
            this.y = 0;
            this.vertices.length = 0;
            this.weight = null
        };
        e.prototype.containsPoint = function(t, e) {
            var a = false;
            if (t >= this.x && t <= this.width && e >= this.y && e <= this.height) {
                for (var i = 0,
                         r = this.vertices.length,
                         n = r - 2; i < r; i += 2) {
                    var s = this.vertices[n + 1];
                    var o = this.vertices[i + 1];
                    if (o < e && s >= e || s < e && o >= e) {
                        var l = this.vertices[n];
                        var h = this.vertices[i];
                        if ((e - o) * (l - h) / (s - o) + h < t) {
                            a = !a
                        }
                    }
                    n = i
                }
            }
            return a
        };
        e.prototype.intersectsSegment = function(t, i, r, n, s, o, l) {
            if (s === void 0) {
                s = null
            }
            if (o === void 0) {
                o = null
            }
            if (l === void 0) {
                l = null
            }
            var h = 0;
            if (a.rectangleIntersectsSegment(t, i, r, n, this.x, this.y, this.x + this.width, this.y + this.height, null, null, null) !== 0) {
                h = e.polygonIntersectsSegment(t, i, r, n, this.vertices, s, o, l)
            }
            return h
        };
        return e
    } (e);
    t.PolygonBoundingBoxData = r
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.cachedFrames = [];
            e.boneTimelines = {};
            e.surfaceTimelines = {};
            e.slotTimelines = {};
            e.constraintTimelines = {};
            e.animationTimelines = {};
            e.boneCachedFrameIndices = {};
            e.slotCachedFrameIndices = {};
            e.actionTimeline = null;
            e.zOrderTimeline = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.AnimationData]"
        };
        e.prototype._onClear = function() {
            for (var t in this.boneTimelines) {
                for (var e = 0,
                         a = this.boneTimelines[t]; e < a.length; e++) {
                    var i = a[e];
                    i.returnToPool()
                }
                delete this.boneTimelines[t]
            }
            for (var t in this.surfaceTimelines) {
                for (var r = 0,
                         n = this.surfaceTimelines[t]; r < n.length; r++) {
                    var i = n[r];
                    i.returnToPool()
                }
                delete this.surfaceTimelines[t]
            }
            for (var t in this.slotTimelines) {
                for (var s = 0,
                         o = this.slotTimelines[t]; s < o.length; s++) {
                    var i = o[s];
                    i.returnToPool()
                }
                delete this.slotTimelines[t]
            }
            for (var t in this.constraintTimelines) {
                for (var l = 0,
                         h = this.constraintTimelines[t]; l < h.length; l++) {
                    var i = h[l];
                    i.returnToPool()
                }
                delete this.constraintTimelines[t]
            }
            for (var t in this.animationTimelines) {
                for (var f = 0,
                         u = this.animationTimelines[t]; f < u.length; f++) {
                    var i = u[f];
                    i.returnToPool()
                }
                delete this.animationTimelines[t]
            }
            for (var t in this.boneCachedFrameIndices) {
                delete this.boneCachedFrameIndices[t]
            }
            for (var t in this.slotCachedFrameIndices) {
                delete this.slotCachedFrameIndices[t]
            }
            if (this.actionTimeline !== null) {
                this.actionTimeline.returnToPool()
            }
            if (this.zOrderTimeline !== null) {
                this.zOrderTimeline.returnToPool()
            }
            this.frameIntOffset = 0;
            this.frameFloatOffset = 0;
            this.frameOffset = 0;
            this.frameCount = 0;
            this.playTimes = 0;
            this.duration = 0;
            this.scale = 1;
            this.fadeInTime = 0;
            this.cacheFrameRate = 0;
            this.name = "";
            this.cachedFrames.length = 0;
            this.actionTimeline = null;
            this.zOrderTimeline = null;
            this.parent = null
        };
        e.prototype.cacheFrames = function(t) {
            if (this.cacheFrameRate > 0) {
                return
            }
            this.cacheFrameRate = Math.max(Math.ceil(t * this.scale), 1);
            var e = Math.ceil(this.cacheFrameRate * this.duration) + 1;
            this.cachedFrames.length = e;
            for (var a = 0,
                     i = this.cacheFrames.length; a < i; ++a) {
                this.cachedFrames[a] = false
            }
            for (var r = 0,
                     n = this.parent.sortedBones; r < n.length; r++) {
                var s = n[r];
                var o = new Array(e);
                for (var a = 0,
                         i = o.length; a < i; ++a) {
                    o[a] = -1
                }
                this.boneCachedFrameIndices[s.name] = o
            }
            for (var l = 0,
                     h = this.parent.sortedSlots; l < h.length; l++) {
                var f = h[l];
                var o = new Array(e);
                for (var a = 0,
                         i = o.length; a < i; ++a) {
                    o[a] = -1
                }
                this.slotCachedFrameIndices[f.name] = o
            }
        };
        e.prototype.addBoneTimeline = function(t, e) {
            var a = t.name in this.boneTimelines ? this.boneTimelines[t.name] : this.boneTimelines[t.name] = [];
            if (a.indexOf(e) < 0) {
                a.push(e)
            }
        };
        e.prototype.addSurfaceTimeline = function(t, e) {
            var a = t.name in this.surfaceTimelines ? this.surfaceTimelines[t.name] : this.surfaceTimelines[t.name] = [];
            if (a.indexOf(e) < 0) {
                a.push(e)
            }
        };
        e.prototype.addSlotTimeline = function(t, e) {
            var a = t.name in this.slotTimelines ? this.slotTimelines[t.name] : this.slotTimelines[t.name] = [];
            if (a.indexOf(e) < 0) {
                a.push(e)
            }
        };
        e.prototype.addConstraintTimeline = function(t, e) {
            var a = t.name in this.constraintTimelines ? this.constraintTimelines[t.name] : this.constraintTimelines[t.name] = [];
            if (a.indexOf(e) < 0) {
                a.push(e)
            }
        };
        e.prototype.addAnimationTimeline = function(t, e) {
            var a = t in this.animationTimelines ? this.animationTimelines[t] : this.animationTimelines[t] = [];
            if (a.indexOf(e) < 0) {
                a.push(e)
            }
        };
        e.prototype.getBoneTimelines = function(t) {
            return t in this.boneTimelines ? this.boneTimelines[t] : null
        };
        e.prototype.getSurfaceTimelines = function(t) {
            return t in this.surfaceTimelines ? this.surfaceTimelines[t] : null
        };
        e.prototype.getSlotTimelines = function(t) {
            return t in this.slotTimelines ? this.slotTimelines[t] : null
        };
        e.prototype.getConstraintTimelines = function(t) {
            return t in this.constraintTimelines ? this.constraintTimelines[t] : null
        };
        e.prototype.getAnimationTimelines = function(t) {
            return t in this.animationTimelines ? this.animationTimelines[t] : null
        };
        e.prototype.getBoneCachedFrameIndices = function(t) {
            return t in this.boneCachedFrameIndices ? this.boneCachedFrameIndices[t] : null
        };
        e.prototype.getSlotCachedFrameIndices = function(t) {
            return t in this.slotCachedFrameIndices ? this.slotCachedFrameIndices[t] : null
        };
        return e
    } (t.BaseObject);
    t.AnimationData = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.TimelineData]"
        };
        e.prototype._onClear = function() {
            this.type = 10;
            this.offset = 0;
            this.frameIndicesOffset = -1
        };
        return e
    } (t.BaseObject);
    t.TimelineData = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.boneMask = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.AnimationConfig]"
        };
        e.prototype._onClear = function() {
            this.pauseFadeOut = true;
            this.fadeOutMode = 4;
            this.fadeOutTweenType = 1;
            this.fadeOutTime = -1;
            this.actionEnabled = true;
            this.additiveBlending = false;
            this.displayControl = true;
            this.pauseFadeIn = true;
            this.resetToPose = true;
            this.fadeInTweenType = 1;
            this.playTimes = -1;
            this.layer = 0;
            this.position = 0;
            this.duration = -1;
            this.timeScale = -100;
            this.weight = 1;
            this.fadeInTime = -1;
            this.autoFadeOutTime = -1;
            this.name = "";
            this.animation = "";
            this.group = "";
            this.boneMask.length = 0
        };
        e.prototype.clear = function() {
            this._onClear()
        };
        e.prototype.copyFrom = function(t) {
            this.pauseFadeOut = t.pauseFadeOut;
            this.fadeOutMode = t.fadeOutMode;
            this.autoFadeOutTime = t.autoFadeOutTime;
            this.fadeOutTweenType = t.fadeOutTweenType;
            this.actionEnabled = t.actionEnabled;
            this.additiveBlending = t.additiveBlending;
            this.displayControl = t.displayControl;
            this.pauseFadeIn = t.pauseFadeIn;
            this.resetToPose = t.resetToPose;
            this.playTimes = t.playTimes;
            this.layer = t.layer;
            this.position = t.position;
            this.duration = t.duration;
            this.timeScale = t.timeScale;
            this.fadeInTime = t.fadeInTime;
            this.fadeOutTime = t.fadeOutTime;
            this.fadeInTweenType = t.fadeInTweenType;
            this.weight = t.weight;
            this.name = t.name;
            this.animation = t.animation;
            this.group = t.group;
            this.boneMask.length = t.boneMask.length;
            for (var e = 0,
                     a = this.boneMask.length; e < a; ++e) {
                this.boneMask[e] = t.boneMask[e]
            }
        };
        e.prototype.containsBoneMask = function(t) {
            return this.boneMask.length === 0 || this.boneMask.indexOf(t) >= 0
        };
        e.prototype.addBoneMask = function(t, e, a) {
            if (a === void 0) {
                a = true
            }
            var i = t.getBone(e);
            if (i === null) {
                return
            }
            if (this.boneMask.indexOf(e) < 0) {
                this.boneMask.push(e)
            }
            if (a) {
                for (var r = 0,
                         n = t.getBones(); r < n.length; r++) {
                    var s = n[r];
                    if (this.boneMask.indexOf(s.name) < 0 && i.contains(s)) {
                        this.boneMask.push(s.name)
                    }
                }
            }
        };
        e.prototype.removeBoneMask = function(t, e, a) {
            if (a === void 0) {
                a = true
            }
            var i = this.boneMask.indexOf(e);
            if (i >= 0) {
                this.boneMask.splice(i, 1)
            }
            if (a) {
                var r = t.getBone(e);
                if (r !== null) {
                    if (this.boneMask.length > 0) {
                        for (var n = 0,
                                 s = t.getBones(); n < s.length; n++) {
                            var o = s[n];
                            var l = this.boneMask.indexOf(o.name);
                            if (l >= 0 && r.contains(o)) {
                                this.boneMask.splice(l, 1)
                            }
                        }
                    } else {
                        for (var h = 0,
                                 f = t.getBones(); h < f.length; h++) {
                            var o = f[h];
                            if (o === r) {
                                continue
                            }
                            if (!r.contains(o)) {
                                this.boneMask.push(o.name)
                            }
                        }
                    }
                }
            }
        };
        return e
    } (t.BaseObject);
    t.AnimationConfig = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.textures = {};
            return e
        }
        e.prototype._onClear = function() {
            for (var t in this.textures) {
                this.textures[t].returnToPool();
                delete this.textures[t]
            }
            this.autoSearch = false;
            this.width = 0;
            this.height = 0;
            this.scale = 1;
            this.name = "";
            this.imagePath = ""
        };
        e.prototype.copyFrom = function(t) {
            this.autoSearch = t.autoSearch;
            this.scale = t.scale;
            this.width = t.width;
            this.height = t.height;
            this.name = t.name;
            this.imagePath = t.imagePath;
            for (var e in this.textures) {
                this.textures[e].returnToPool();
                delete this.textures[e]
            }
            for (var e in t.textures) {
                var a = this.createTexture();
                a.copyFrom(t.textures[e]);
                this.textures[e] = a
            }
        };
        e.prototype.addTexture = function(t) {
            if (t.name in this.textures) {
                console.warn("Same texture: " + t.name);
                return
            }
            t.parent = this;
            this.textures[t.name] = t
        };
        e.prototype.getTexture = function(t) {
            return t in this.textures ? this.textures[t] : null
        };
        return e
    } (t.BaseObject);
    t.TextureAtlasData = e;
    var a = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.region = new t.Rectangle;
            a.frame = null;
            return a
        }
        a.createRectangle = function() {
            return new t.Rectangle
        };
        a.prototype._onClear = function() {
            this.rotated = false;
            this.name = "";
            this.region.clear();
            this.parent = null;
            this.frame = null
        };
        a.prototype.copyFrom = function(t) {
            this.rotated = t.rotated;
            this.name = t.name;
            this.region.copyFrom(t.region);
            this.parent = t.parent;
            if (this.frame === null && t.frame !== null) {
                this.frame = a.createRectangle()
            } else if (this.frame !== null && t.frame === null) {
                this.frame = null
            }
            if (this.frame !== null && t.frame !== null) {
                this.frame.copyFrom(t.frame)
            }
        };
        return a
    } (t.BaseObject);
    t.TextureData = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var t = e !== null && e.apply(this, arguments) || this;
            t._bones = [];
            t._slots = [];
            t._glueSlots = [];
            t._constraints = [];
            t._actions = [];
            t._animation = null;
            t._proxy = null;
            t._replaceTextureAtlasData = null;
            t._clock = null;
            return t
        }
        a.toString = function() {
            return "[class dragonBones.Armature]"
        };
        a._onSortSlots = function(t, e) {
            return t._zOrder > e._zOrder ? 1 : -1
        };
        a.prototype._onClear = function() {
            if (this._clock !== null) {
                this._clock.remove(this)
            }
            for (var t = 0,
                     e = this._bones; t < e.length; t++) {
                var a = e[t];
                a.returnToPool()
            }
            for (var i = 0,
                     r = this._slots; i < r.length; i++) {
                var n = r[i];
                n.returnToPool()
            }
            for (var s = 0,
                     o = this._constraints; s < o.length; s++) {
                var l = o[s];
                l.returnToPool()
            }
            if (this._animation !== null) {
                this._animation.returnToPool()
            }
            if (this._proxy !== null) {
                this._proxy.dbClear()
            }
            if (this._replaceTextureAtlasData !== null) {
                this._replaceTextureAtlasData.returnToPool()
            }
            this.inheritAnimation = true;
            this.userData = null;
            this._lockUpdate = false;
            this._bonesDirty = false;
            this._slotsDirty = false;
            this._zOrderDirty = false;
            this._flipX = false;
            this._flipY = false;
            this._cacheFrameIndex = -1;
            this._bones.length = 0;
            this._slots.length = 0;
            this._glueSlots.length = 0;
            this._constraints.length = 0;
            this._actions.length = 0;
            this._armatureData = null;
            this._animation = null;
            this._proxy = null;
            this._display = null;
            this._replaceTextureAtlasData = null;
            this._replacedTexture = null;
            this._dragonBones = null;
            this._clock = null;
            this._parent = null
        };
        a.prototype._sortBones = function() {
            var t = this._bones.length;
            if (t <= 0) {
                return
            }
            var e = this._bones.concat();
            var a = 0;
            var i = 0;
            this._bones.length = 0;
            while (i < t) {
                var r = e[a++];
                if (a >= t) {
                    a = 0
                }
                if (this._bones.indexOf(r) >= 0) {
                    continue
                }
                if (r._hasConstraint) {
                    var n = false;
                    for (var s = 0,
                             o = this._constraints; s < o.length; s++) {
                        var l = o[s];
                        if (l._root === r && this._bones.indexOf(l._target) < 0) {
                            n = true;
                            break
                        }
                    }
                    if (n) {
                        continue
                    }
                }
                if (r.parent !== null && this._bones.indexOf(r.parent) < 0) {
                    continue
                }
                this._bones.push(r);
                i++
            }
        };
        a.prototype._sortSlots = function() {
            this._slots.sort(a._onSortSlots)
        };
        a.prototype._sortZOrder = function(t, e) {
            var a = this._armatureData.sortedSlots;
            var i = t === null;
            if (this._zOrderDirty || !i) {
                for (var r = 0,
                         n = a.length; r < n; ++r) {
                    var s = i ? r: t[e + r];
                    if (s < 0 || s >= n) {
                        continue
                    }
                    var o = a[s];
                    var l = this.getSlot(o.name);
                    if (l !== null) {
                        l._setZorder(r)
                    }
                }
                this._slotsDirty = true;
                this._zOrderDirty = !i
            }
        };
        a.prototype._addBoneToBoneList = function(t) {
            if (this._bones.indexOf(t) < 0) {
                this._bonesDirty = true;
                this._bones.push(t)
            }
        };
        a.prototype._removeBoneFromBoneList = function(t) {
            var e = this._bones.indexOf(t);
            if (e >= 0) {
                this._bones.splice(e, 1)
            }
        };
        a.prototype._addSlotToSlotList = function(t) {
            if (this._slots.indexOf(t) < 0) {
                this._slotsDirty = true;
                this._slots.push(t)
            }
        };
        a.prototype._removeSlotFromSlotList = function(t) {
            var e = this._slots.indexOf(t);
            if (e >= 0) {
                this._slots.splice(e, 1)
            }
        };
        a.prototype._bufferAction = function(t, e) {
            if (this._actions.indexOf(t) < 0) {
                if (e) {
                    this._actions.unshift(t)
                } else {
                    this._actions.push(t)
                }
            }
        };
        a.prototype.dispose = function() {
            if (this._armatureData !== null) {
                this._lockUpdate = true;
                this._dragonBones.bufferObject(this)
            }
        };
        a.prototype.init = function(e, a, i, r) {
            if (this._armatureData !== null) {
                return
            }
            this._armatureData = e;
            this._animation = t.BaseObject.borrowObject(t.Animation);
            this._proxy = a;
            this._display = i;
            this._dragonBones = r;
            this._proxy.dbInit(this);
            this._animation.init(this);
            this._animation.animations = this._armatureData.animations
        };
        a.prototype.advanceTime = function(t) {
            if (this._lockUpdate) {
                return
            }
            if (this._armatureData === null) {
                console.warn("The armature has been disposed.");
                return
            } else if (this._armatureData.parent === null) {
                console.warn("The armature data has been disposed.\nPlease make sure dispose armature before call factory.clear().");
                return
            }
            var e = this._cacheFrameIndex;
            this._animation.advanceTime(t);
            if (this._bonesDirty) {
                this._bonesDirty = false;
                this._sortBones()
            }
            if (this._slotsDirty) {
                this._slotsDirty = false;
                this._sortSlots()
            }
            if (this._cacheFrameIndex < 0 || this._cacheFrameIndex !== e) {
                var a = 0,
                    i = 0;
                for (a = 0, i = this._bones.length; a < i; ++a) {
                    this._bones[a].update(this._cacheFrameIndex)
                }
                for (a = 0, i = this._slots.length; a < i; ++a) {
                    this._slots[a].update(this._cacheFrameIndex)
                }
                for (a = 0, i = this._glueSlots.length; a < i; ++a) {
                    this._glueSlots[a]._updateGlueMesh()
                }
            }
            if (this._actions.length > 0) {
                this._lockUpdate = true;
                for (var r = 0,
                         n = this._actions; r < n.length; r++) {
                    var s = n[r];
                    var o = s.actionData;
                    if (o === null) {
                        continue
                    }
                    if (o.type === 0) {
                        if (s.slot !== null) {
                            var l = s.slot.childArmature;
                            if (l !== null) {
                                l.animation.fadeIn(o.name)
                            }
                        } else if (s.bone !== null) {
                            for (var h = 0,
                                     f = this.getSlots(); h < f.length; h++) {
                                var u = f[h];
                                if (u.parent === s.bone) {
                                    var l = u.childArmature;
                                    if (l !== null) {
                                        l.animation.fadeIn(o.name)
                                    }
                                }
                            }
                        } else {
                            this._animation.fadeIn(o.name)
                        }
                    }
                }
                this._actions.length = 0;
                this._lockUpdate = false
            }
            this._proxy.dbUpdate()
        };
        a.prototype.invalidUpdate = function(t, e) {
            if (t === void 0) {
                t = null
            }
            if (e === void 0) {
                e = false
            }
            if (t !== null && t.length > 0) {
                var a = this.getBone(t);
                if (a !== null) {
                    a.invalidUpdate();
                    if (e) {
                        for (var i = 0,
                                 r = this._slots; i < r.length; i++) {
                            var n = r[i];
                            if (n.parent === a) {
                                n.invalidUpdate()
                            }
                        }
                    }
                }
            } else {
                for (var s = 0,
                         o = this._bones; s < o.length; s++) {
                    var a = o[s];
                    a.invalidUpdate()
                }
                if (e) {
                    for (var l = 0,
                             h = this._slots; l < h.length; l++) {
                        var n = h[l];
                        n.invalidUpdate()
                    }
                }
            }
        };
        a.prototype.containsPoint = function(t, e) {
            for (var a = 0,
                     i = this._slots; a < i.length; a++) {
                var r = i[a];
                if (r.containsPoint(t, e)) {
                    return r
                }
            }
            return null
        };
        a.prototype.intersectsSegment = function(t, e, a, i, r, n, s) {
            if (r === void 0) {
                r = null
            }
            if (n === void 0) {
                n = null
            }
            if (s === void 0) {
                s = null
            }
            var o = t === a;
            var l = 0;
            var h = 0;
            var f = 0;
            var u = 0;
            var _ = 0;
            var c = 0;
            var m = 0;
            var p = 0;
            var d = null;
            var y = null;
            for (var g = 0,
                     v = this._slots; g < v.length; g++) {
                var b = v[g];
                var D = b.intersectsSegment(t, e, a, i, r, n, s);
                if (D > 0) {
                    if (r !== null || n !== null) {
                        if (r !== null) {
                            var T = o ? r.y - e: r.x - t;
                            if (T < 0) {
                                T = -T
                            }
                            if (d === null || T < l) {
                                l = T;
                                f = r.x;
                                u = r.y;
                                d = b;
                                if (s) {
                                    m = s.x
                                }
                            }
                        }
                        if (n !== null) {
                            var T = n.x - t;
                            if (T < 0) {
                                T = -T
                            }
                            if (y === null || T > h) {
                                h = T;
                                _ = n.x;
                                c = n.y;
                                y = b;
                                if (s !== null) {
                                    p = s.y
                                }
                            }
                        }
                    } else {
                        d = b;
                        break
                    }
                }
            }
            if (d !== null && r !== null) {
                r.x = f;
                r.y = u;
                if (s !== null) {
                    s.x = m
                }
            }
            if (y !== null && n !== null) {
                n.x = _;
                n.y = c;
                if (s !== null) {
                    s.y = p
                }
            }
            return d
        };
        a.prototype.getBone = function(t) {
            for (var e = 0,
                     a = this._bones; e < a.length; e++) {
                var i = a[e];
                if (i.name === t) {
                    return i
                }
            }
            return null
        };
        a.prototype.getBoneByDisplay = function(t) {
            var e = this.getSlotByDisplay(t);
            return e !== null ? e.parent: null
        };
        a.prototype.getSlot = function(t) {
            for (var e = 0,
                     a = this._slots; e < a.length; e++) {
                var i = a[e];
                if (i.name === t) {
                    return i
                }
            }
            return null
        };
        a.prototype.getSlotByDisplay = function(t) {
            if (t !== null) {
                for (var e = 0,
                         a = this._slots; e < a.length; e++) {
                    var i = a[e];
                    if (i.display === t) {
                        return i
                    }
                }
            }
            return null
        };
        a.prototype.addBone = function(t, e) {
            console.assert(t !== null);
            t._setArmature(this);
            t._setParent(e.length > 0 ? this.getBone(e) : null)
        };
        a.prototype.addSlot = function(t, e) {
            var a = this.getBone(e);
            console.assert(t !== null && a !== null);
            t._setArmature(this);
            t._setParent(a)
        };
        a.prototype.addConstraint = function(t) {
            if (this._constraints.indexOf(t) < 0) {
                this._constraints.push(t)
            }
        };
        a.prototype.removeBone = function(t) {
            console.assert(t !== null && t.armature === this);
            t._setParent(null);
            t._setArmature(null)
        };
        a.prototype.removeSlot = function(t) {
            console.assert(t !== null && t.armature === this);
            t._setParent(null);
            t._setArmature(null)
        };
        a.prototype.getBones = function() {
            return this._bones
        };
        a.prototype.getSlots = function() {
            return this._slots
        };
        Object.defineProperty(a.prototype, "flipX", {
            get: function() {
                return this._flipX
            },
            set: function(t) {
                if (this._flipX === t) {
                    return
                }
                this._flipX = t;
                this.invalidUpdate()
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "flipY", {
            get: function() {
                return this._flipY
            },
            set: function(t) {
                if (this._flipY === t) {
                    return
                }
                this._flipY = t;
                this.invalidUpdate()
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "cacheFrameRate", {
            get: function() {
                return this._armatureData.cacheFrameRate
            },
            set: function(t) {
                if (this._armatureData.cacheFrameRate !== t) {
                    this._armatureData.cacheFrames(t);
                    for (var e = 0,
                             a = this._slots; e < a.length; e++) {
                        var i = a[e];
                        var r = i.childArmature;
                        if (r !== null) {
                            r.cacheFrameRate = t
                        }
                    }
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "name", {
            get: function() {
                return this._armatureData.name
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "armatureData", {
            get: function() {
                return this._armatureData
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animation", {
            get: function() {
                return this._animation
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "proxy", {
            get: function() {
                return this._proxy
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "eventDispatcher", {
            get: function() {
                return this._proxy
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "display", {
            get: function() {
                return this._display
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "replacedTexture", {
            get: function() {
                return this._replacedTexture
            },
            set: function(t) {
                if (this._replacedTexture === t) {
                    return
                }
                if (this._replaceTextureAtlasData !== null) {
                    this._replaceTextureAtlasData.returnToPool();
                    this._replaceTextureAtlasData = null
                }
                this._replacedTexture = t;
                for (var e = 0,
                         a = this._slots; e < a.length; e++) {
                    var i = a[e];
                    i.invalidUpdate();
                    i.update( - 1)
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "clock", {
            get: function() {
                return this._clock
            },
            set: function(t) {
                if (this._clock === t) {
                    return
                }
                if (this._clock !== null) {
                    this._clock.remove(this)
                }
                this._clock = t;
                if (this._clock) {
                    this._clock.add(this)
                }
                for (var e = 0,
                         a = this._slots; e < a.length; e++) {
                    var i = a[e];
                    var r = i.childArmature;
                    if (r !== null) {
                        r.clock = this._clock
                    }
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "parent", {
            get: function() {
                return this._parent
            },
            enumerable: true,
            configurable: true
        });
        a.prototype.replaceTexture = function(t) {
            this.replacedTexture = t
        };
        a.prototype.hasEventListener = function(t) {
            return this._proxy.hasDBEventListener(t)
        };
        a.prototype.addEventListener = function(t, e, a) {
            this._proxy.addDBEventListener(t, e, a)
        };
        a.prototype.removeEventListener = function(t, e, a) {
            this._proxy.removeDBEventListener(t, e, a)
        };
        a.prototype.enableAnimationCache = function(t) {
            this.cacheFrameRate = t
        };
        a.prototype.getDisplay = function() {
            return this._display
        };
        return a
    } (t.BaseObject);
    t.Armature = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.globalTransformMatrix = new t.Matrix;
            a.global = new t.Transform;
            a.offset = new t.Transform;
            return a
        }
        a.prototype._onClear = function() {
            this.globalTransformMatrix.identity();
            this.global.identity();
            this.offset.identity();
            this.origin = null;
            this.userData = null;
            this._globalDirty = false;
            this._armature = null;
            this._parent = null
        };
        a.prototype._setArmature = function(t) {
            this._armature = t
        };
        a.prototype._setParent = function(t) {
            this._parent = t
        };
        a.prototype.updateGlobalTransform = function() {
            if (this._globalDirty) {
                this._globalDirty = false;
                this.global.fromMatrix(this.globalTransformMatrix)
            }
        };
        Object.defineProperty(a.prototype, "armature", {
            get: function() {
                return this._armature
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "parent", {
            get: function() {
                return this._parent
            },
            enumerable: true,
            configurable: true
        });
        a._helpMatrix = new t.Matrix;
        a._helpTransform = new t.Transform;
        a._helpPoint = new t.Point;
        return a
    } (t.BaseObject);
    t.TransformObject = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.animationPose = new t.Transform;
            a._blendState = new t.BlendState;
            return a
        }
        a.toString = function() {
            return "[class dragonBones.Bone]"
        };
        a.prototype._onClear = function() {
            e.prototype._onClear.call(this);
            this.offsetMode = 1;
            this.animationPose.identity();
            this._transformDirty = false;
            this._childrenTransformDirty = false;
            this._localDirty = true;
            this._hasConstraint = false;
            this._visible = true;
            this._cachedFrameIndex = -1;
            this._blendState.clear();
            this._boneData = null;
            this._cachedFrameIndices = null
        };
        a.prototype._updateGlobalTransformMatrix = function(e) {
            var a = this._boneData;
            var i = this._parent;
            var r = this._armature.flipX;
            var n = this._armature.flipY === t.DragonBones.yDown;
            var s = i !== null;
            var o = 0;
            var l = this.global;
            var h = this.globalTransformMatrix;
            if (this.offsetMode === 1) {
                if (this.origin !== null) {
                    l.x = this.origin.x + this.offset.x + this.animationPose.x;
                    l.y = this.origin.y + this.offset.y + this.animationPose.y;
                    l.skew = this.origin.skew + this.offset.skew + this.animationPose.skew;
                    l.rotation = this.origin.rotation + this.offset.rotation + this.animationPose.rotation;
                    l.scaleX = this.origin.scaleX * this.offset.scaleX * this.animationPose.scaleX;
                    l.scaleY = this.origin.scaleY * this.offset.scaleY * this.animationPose.scaleY
                } else {
                    l.copyFrom(this.offset).add(this.animationPose)
                }
            } else if (this.offsetMode === 0) {
                if (this.origin !== null) {
                    l.copyFrom(this.origin).add(this.animationPose)
                } else {
                    l.copyFrom(this.animationPose)
                }
            } else {
                s = false;
                l.copyFrom(this.offset)
            }
            if (s) {
                var f = i._boneData.type === 0 ? i.globalTransformMatrix: i._getGlobalTransformMatrix(l.x, l.y);
                if (a.inheritScale) {
                    if (!a.inheritRotation) {
                        i.updateGlobalTransform();
                        if (r && n) {
                            o = l.rotation - (i.global.rotation + Math.PI)
                        } else if (r) {
                            o = l.rotation + i.global.rotation + Math.PI
                        } else if (n) {
                            o = l.rotation + i.global.rotation
                        } else {
                            o = l.rotation - i.global.rotation
                        }
                        l.rotation = o
                    }
                    l.toMatrix(h);
                    h.concat(f);
                    if (a.inheritTranslation) {
                        l.x = h.tx;
                        l.y = h.ty
                    } else {
                        h.tx = l.x;
                        h.ty = l.y
                    }
                    if (e) {
                        l.fromMatrix(h)
                    } else {
                        this._globalDirty = true
                    }
                } else {
                    if (a.inheritTranslation) {
                        var u = l.x;
                        var _ = l.y;
                        l.x = f.a * u + f.c * _ + f.tx;
                        l.y = f.b * u + f.d * _ + f.ty
                    } else {
                        if (r) {
                            l.x = -l.x
                        }
                        if (n) {
                            l.y = -l.y
                        }
                    }
                    if (a.inheritRotation) {
                        i.updateGlobalTransform();
                        if (i.global.scaleX < 0) {
                            o = l.rotation + i.global.rotation + Math.PI
                        } else {
                            o = l.rotation + i.global.rotation
                        }
                        if (f.a * f.d - f.b * f.c < 0) {
                            o -= l.rotation * 2;
                            if (r !== n || a.inheritReflection) {
                                l.skew += Math.PI
                            }
                        }
                        l.rotation = o
                    } else if (r || n) {
                        if (r && n) {
                            o = l.rotation + Math.PI
                        } else {
                            if (r) {
                                o = Math.PI - l.rotation
                            } else {
                                o = -l.rotation
                            }
                            l.skew += Math.PI
                        }
                        l.rotation = o
                    }
                    l.toMatrix(h)
                }
            } else {
                if (r || n) {
                    if (r) {
                        l.x = -l.x
                    }
                    if (n) {
                        l.y = -l.y
                    }
                    if (r && n) {
                        o = l.rotation + Math.PI
                    } else {
                        if (r) {
                            o = Math.PI - l.rotation
                        } else {
                            o = -l.rotation
                        }
                        l.skew += Math.PI
                    }
                    l.rotation = o
                }
                l.toMatrix(h)
            }
        };
        a.prototype._setArmature = function(t) {
            if (this._armature === t) {
                return
            }
            var e = null;
            var a = null;
            if (this._armature !== null) {
                e = this.getSlots();
                a = this.getBones();
                this._armature._removeBoneFromBoneList(this)
            }
            this._armature = t;
            if (this._armature !== null) {
                this._armature._addBoneToBoneList(this)
            }
            if (e !== null) {
                for (var i = 0,
                         r = e; i < r.length; i++) {
                    var n = r[i];
                    if (n.parent === this) {
                        n._setArmature(this._armature)
                    }
                }
            }
            if (a !== null) {
                for (var s = 0,
                         o = a; s < o.length; s++) {
                    var l = o[s];
                    if (l.parent === this) {
                        l._setArmature(this._armature)
                    }
                }
            }
        };
        a.prototype.init = function(t) {
            if (this._boneData !== null) {
                return
            }
            this._boneData = t;
            this.origin = this._boneData.transform
        };
        a.prototype.update = function(t) {
            this._blendState.dirty = false;
            if (t >= 0 && this._cachedFrameIndices !== null) {
                var e = this._cachedFrameIndices[t];
                if (e >= 0 && this._cachedFrameIndex === e) {
                    this._transformDirty = false
                } else if (e >= 0) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = e
                } else {
                    if (this._hasConstraint) {
                        for (var a = 0,
                                 i = this._armature._constraints; a < i.length; a++) {
                            var r = i[a];
                            if (r._root === this) {
                                r.update()
                            }
                        }
                    }
                    if (this._transformDirty || this._parent !== null && this._parent._childrenTransformDirty) {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1
                    } else if (this._cachedFrameIndex >= 0) {
                        this._transformDirty = false;
                        this._cachedFrameIndices[t] = this._cachedFrameIndex
                    } else {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1
                    }
                }
            } else {
                if (this._hasConstraint) {
                    for (var n = 0,
                             s = this._armature._constraints; n < s.length; n++) {
                        var r = s[n];
                        if (r._root === this) {
                            r.update()
                        }
                    }
                }
                if (this._transformDirty || this._parent !== null && this._parent._childrenTransformDirty) {
                    t = -1;
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1
                }
            }
            if (this._transformDirty) {
                this._transformDirty = false;
                this._childrenTransformDirty = true;
                if (this._cachedFrameIndex < 0) {
                    var o = t >= 0;
                    if (this._localDirty) {
                        this._updateGlobalTransformMatrix(o)
                    }
                    if (o && this._cachedFrameIndices !== null) {
                        this._cachedFrameIndex = this._cachedFrameIndices[t] = this._armature._armatureData.setCacheFrame(this.globalTransformMatrix, this.global)
                    }
                } else {
                    this._armature._armatureData.getCacheFrame(this.globalTransformMatrix, this.global, this._cachedFrameIndex)
                }
            } else if (this._childrenTransformDirty) {
                this._childrenTransformDirty = false
            }
            this._localDirty = true
        };
        a.prototype.updateByConstraint = function() {
            if (this._localDirty) {
                this._localDirty = false;
                if (this._transformDirty || this._parent !== null && this._parent._childrenTransformDirty) {
                    this._updateGlobalTransformMatrix(true)
                }
                this._transformDirty = true
            }
        };
        a.prototype.invalidUpdate = function() {
            this._transformDirty = true
        };
        a.prototype.contains = function(t) {
            if (t === this) {
                return false
            }
            var e = t;
            while (e !== this && e !== null) {
                e = e.parent
            }
            return e === this
        };
        Object.defineProperty(a.prototype, "boneData", {
            get: function() {
                return this._boneData
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "visible", {
            get: function() {
                return this._visible
            },
            set: function(t) {
                if (this._visible === t) {
                    return
                }
                this._visible = t;
                for (var e = 0,
                         a = this._armature.getSlots(); e < a.length; e++) {
                    var i = a[e];
                    if (i._parent === this) {
                        i._updateVisible()
                    }
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "name", {
            get: function() {
                return this._boneData.name
            },
            enumerable: true,
            configurable: true
        });
        a.prototype.getBones = function() {
            var t = new Array;
            for (var e = 0,
                     a = this._armature.getBones(); e < a.length; e++) {
                var i = a[e];
                if (i.parent === this) {
                    t.push(i)
                }
            }
            return t
        };
        a.prototype.getSlots = function() {
            var t = new Array;
            for (var e = 0,
                     a = this._armature.getSlots(); e < a.length; e++) {
                var i = a[e];
                if (i.parent === this) {
                    t.push(i)
                }
            }
            return t
        };
        Object.defineProperty(a.prototype, "slot", {
            get: function() {
                for (var t = 0,
                         e = this._armature.getSlots(); t < e.length; t++) {
                    var a = e[t];
                    if (a.parent === this) {
                        return a
                    }
                }
                return null
            },
            enumerable: true,
            configurable: true
        });
        return a
    } (t.TransformObject);
    t.Bone = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e._vertices = [];
            e._deformVertices = [];
            e._hullCache = [];
            e._matrixCahce = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.Surface]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this._dX = 0;
            this._dY = 0;
            this._k = 0;
            this._kX = 0;
            this._kY = 0;
            this._vertices.length = 0;
            this._deformVertices.length = 0;
            this._matrixCahce.length = 0;
            this._hullCache.length = 0
        };
        e.prototype._getAffineTransform = function(t, e, a, i, r, n, s, o, l, h, f, u, _) {
            var c = s - r;
            var m = o - n;
            var p = l - r;
            var d = h - n;
            f.rotation = Math.atan2(m, c);
            f.skew = Math.atan2(d, p) - Math.PI * .5 - f.rotation;
            if (_) {
                f.rotation += Math.PI
            }
            f.scaleX = Math.sqrt(c * c + m * m) / a;
            f.scaleY = Math.sqrt(p * p + d * d) / i;
            f.toMatrix(u);
            f.x = u.tx = r - (u.a * t + u.c * e);
            f.y = u.ty = n - (u.b * t + u.d * e)
        };
        e.prototype._updateVertices = function() {
            var t = this._boneData.vertices;
            var e = this._vertices;
            var a = this._deformVertices;
            if (this._parent !== null) {
                if (this._parent._boneData.type === 1) {
                    for (var i = 0,
                             r = t.length; i < r; i += 2) {
                        var n = t[i] + a[i];
                        var s = t[i + 1] + a[i];
                        var o = this._parent._getGlobalTransformMatrix(n, s);
                        e[i] = o.a * n + o.c * s + o.tx;
                        e[i + 1] = o.b * n + o.d * s + o.ty
                    }
                } else {
                    var l = this._parent.globalTransformMatrix;
                    for (var i = 0,
                             r = t.length; i < r; i += 2) {
                        var n = t[i] + a[i];
                        var s = t[i + 1] + a[i + 1];
                        e[i] = l.a * n + l.c * s + l.tx;
                        e[i + 1] = l.b * n + l.d * s + l.ty
                    }
                }
            } else {
                for (var i = 0,
                         r = t.length; i < r; i += 2) {
                    e[i] = t[i] + a[i];
                    e[i + 1] = t[i + 1] + a[i + 1]
                }
            }
        };
        e.prototype._updateGlobalTransformMatrix = function(t) {
            t;
            var e = this._boneData.segmentX * 2;
            var a = this._vertices.length - 2;
            var i = 200;
            var r = this._vertices[0];
            var n = this._vertices[1];
            var s = this._vertices[e];
            var o = this._vertices[e + 1];
            var l = this._vertices[a];
            var h = this._vertices[a + 1];
            var f = this._vertices[a - e];
            var u = this._vertices[a - e + 1];
            var _ = r + (l - r) * .5;
            var c = n + (h - n) * .5;
            var m = s + (f - s) * .5;
            var p = o + (u - o) * .5;
            var d = _ + (m - _) * .5;
            var y = c + (p - c) * .5;
            var g = s + (l - s) * .5;
            var v = o + (h - o) * .5;
            var b = f + (l - f) * .5;
            var D = u + (h - u) * .5;
            this._globalDirty = false;
            this._getAffineTransform(0, 0, i, i, d, y, g, v, b, D, this.global, this.globalTransformMatrix, false)
        };
        e.prototype._getGlobalTransformMatrix = function(t, a) {
            var i = 1e3;
            if (t < -i || i < t || a < -i || i < a) {
                return this.globalTransformMatrix
            }
            var r = false;
            var n = 200;
            var s = this._boneData;
            var o = s.segmentX;
            var l = s.segmentY;
            var h = s.segmentX * 2;
            var f = this._dX;
            var u = this._dY;
            var _ = Math.floor((t + n) / f);
            var c = Math.floor((a + n) / u);
            var m = 0;
            var p = _ * f - n;
            var d = c * u - n;
            var y = this._matrixCahce;
            var g = e._helpMatrix;
            if (t < -n) {
                if (a < -n || a > n) {
                    return this.globalTransformMatrix
                }
                r = a > this._kX * (t + n) + d;
                m = ((o * (l + 1) + o * 2 + l + c) * 2 + (r ? 1 : 0)) * 7;
                if (this._matrixCahce[m] > 0) {
                    g.copyFromArray(y, m + 1)
                } else {
                    var v = c * (h + 2);
                    var b = this._hullCache[4];
                    var D = this._hullCache[5];
                    var T = this._hullCache[2] - (l - c) * b;
                    var A = this._hullCache[3] - (l - c) * D;
                    var x = this._vertices;
                    if (r) {
                        this._getAffineTransform( - n, d + u, i - n, u, x[v + h + 2], x[v + h + 3], T + b, A + D, x[v], x[v + 1], e._helpTransform, g, true)
                    } else {
                        this._getAffineTransform( - i, d, i - n, u, T, A, x[v], x[v + 1], T + b, A + D, e._helpTransform, g, false)
                    }
                    y[m] = 1;
                    y[m + 1] = g.a;
                    y[m + 2] = g.b;
                    y[m + 3] = g.c;
                    y[m + 4] = g.d;
                    y[m + 5] = g.tx;
                    y[m + 6] = g.ty
                }
            } else if (t > n) {
                if (a < -n || a > n) {
                    return this.globalTransformMatrix
                }
                r = a > this._kX * (t - i) + d;
                m = ((o * (l + 1) + o + c) * 2 + (r ? 1 : 0)) * 7;
                if (this._matrixCahce[m] > 0) {
                    g.copyFromArray(y, m + 1)
                } else {
                    var v = (c + 1) * (h + 2) - 2;
                    var b = this._hullCache[4];
                    var D = this._hullCache[5];
                    var T = this._hullCache[0] + c * b;
                    var A = this._hullCache[1] + c * D;
                    var x = this._vertices;
                    if (r) {
                        this._getAffineTransform(i, d + u, i - n, u, T + b, A + D, x[v + h + 2], x[v + h + 3], T, A, e._helpTransform, g, true)
                    } else {
                        this._getAffineTransform(n, d, i - n, u, x[v], x[v + 1], T, A, x[v + h + 2], x[v + h + 3], e._helpTransform, g, false)
                    }
                    y[m] = 1;
                    y[m + 1] = g.a;
                    y[m + 2] = g.b;
                    y[m + 3] = g.c;
                    y[m + 4] = g.d;
                    y[m + 5] = g.tx;
                    y[m + 6] = g.ty
                }
            } else if (a < -n) {
                if (t < -n || t > n) {
                    return this.globalTransformMatrix
                }
                r = a > this._kY * (t - p - f) - i;
                m = (o * (l + 1) + _ * 2 + (r ? 1 : 0)) * 7;
                if (this._matrixCahce[m] > 0) {
                    g.copyFromArray(y, m + 1)
                } else {
                    var v = _ * 2;
                    var b = this._hullCache[10];
                    var D = this._hullCache[11];
                    var T = this._hullCache[8] + _ * b;
                    var A = this._hullCache[9] + _ * D;
                    var x = this._vertices;
                    if (r) {
                        this._getAffineTransform(p + f, -n, f, i - n, x[v + 2], x[v + 3], x[v], x[v + 1], T + b, A + D, e._helpTransform, g, true)
                    } else {
                        this._getAffineTransform(p, -i, f, i - n, T, A, T + b, A + D, x[v], x[v + 1], e._helpTransform, g, false)
                    }
                    y[m] = 1;
                    y[m + 1] = g.a;
                    y[m + 2] = g.b;
                    y[m + 3] = g.c;
                    y[m + 4] = g.d;
                    y[m + 5] = g.tx;
                    y[m + 6] = g.ty
                }
            } else if (a > n) {
                if (t < -n || t > n) {
                    return this.globalTransformMatrix
                }
                r = a > this._kY * (t - p - f) + n;
                m = ((o * (l + 1) + o + l + c) * 2 + (r ? 1 : 0)) * 7;
                if (this._matrixCahce[m] > 0) {
                    g.copyFromArray(y, m + 1)
                } else {
                    var v = l * (h + 2) + _ * 2;
                    var b = this._hullCache[10];
                    var D = this._hullCache[11];
                    var T = this._hullCache[6] - (o - _) * b;
                    var A = this._hullCache[7] - (o - _) * D;
                    var x = this._vertices;
                    if (r) {
                        this._getAffineTransform(p + f, i, f, i - n, T + b, A + D, T, A, x[v + 2], x[v + 3], e._helpTransform, g, true)
                    } else {
                        this._getAffineTransform(p, n, f, i - n, x[v], x[v + 1], x[v + 2], x[v + 3], T, A, e._helpTransform, g, false)
                    }
                    y[m] = 1;
                    y[m + 1] = g.a;
                    y[m + 2] = g.b;
                    y[m + 3] = g.c;
                    y[m + 4] = g.d;
                    y[m + 5] = g.tx;
                    y[m + 6] = g.ty
                }
            } else {
                r = a > this._k * (t - p - f) + d;
                m = ((o * c + _) * 2 + (r ? 1 : 0)) * 7;
                if (this._matrixCahce[m] > 0) {
                    g.copyFromArray(y, m + 1)
                } else {
                    var v = _ * 2 + c * (h + 2);
                    var x = this._vertices;
                    if (r) {
                        this._getAffineTransform(p + f, d + u, f, u, x[v + h + 4], x[v + h + 5], x[v + h + 2], x[v + h + 3], x[v + 2], x[v + 3], e._helpTransform, g, true)
                    } else {
                        this._getAffineTransform(p, d, f, u, x[v], x[v + 1], x[v + 2], x[v + 3], x[v + h + 2], x[v + h + 3], e._helpTransform, g, false)
                    }
                    y[m] = 1;
                    y[m + 1] = g.a;
                    y[m + 2] = g.b;
                    y[m + 3] = g.c;
                    y[m + 4] = g.d;
                    y[m + 5] = g.tx;
                    y[m + 6] = g.ty
                }
            }
            return g
        };
        e.prototype.init = function(e) {
            if (this._boneData !== null) {
                return
            }
            t.prototype.init.call(this, e);
            var a = e.segmentX;
            var i = e.segmentY;
            var r = e.vertices.length;
            var n = 1e3;
            var s = 200;
            this._dX = s * 2 / a;
            this._dY = s * 2 / i;
            this._k = -this._dY / this._dX;
            this._kX = -this._dY / (n - s);
            this._kY = -(n - s) / this._dX;
            this._vertices.length = r;
            this._deformVertices.length = r;
            this._matrixCahce.length = (a * i + a * 2 + i * 2) * 2 * 7;
            this._hullCache.length = 10;
            for (var o = 0; o < r; ++o) {
                this._deformVertices[o] = 0
            }
        };
        e.prototype.update = function(t) {
            this._blendState.dirty = false;
            if (t >= 0 && this._cachedFrameIndices !== null) {
                var a = this._cachedFrameIndices[t];
                if (a >= 0 && this._cachedFrameIndex === a) {
                    this._transformDirty = false
                } else if (a >= 0) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = a
                } else {
                    if (this._hasConstraint) {
                        for (var i = 0,
                                 r = this._armature._constraints; i < r.length; i++) {
                            var n = r[i];
                            if (n._root === this) {
                                n.update()
                            }
                        }
                    }
                    if (this._transformDirty || this._parent !== null && this._parent._childrenTransformDirty) {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1
                    } else if (this._cachedFrameIndex >= 0) {
                        this._transformDirty = false;
                        this._cachedFrameIndices[t] = this._cachedFrameIndex
                    } else {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1
                    }
                }
            } else {
                if (this._hasConstraint) {
                    for (var s = 0,
                             o = this._armature._constraints; s < o.length; s++) {
                        var n = o[s];
                        if (n._root === this) {
                            n.update()
                        }
                    }
                }
                if (this._transformDirty || this._parent !== null && this._parent._childrenTransformDirty) {
                    t = -1;
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1
                }
            }
            if (this._transformDirty) {
                this._transformDirty = false;
                this._childrenTransformDirty = true;
                for (var l = 0,
                         h = this._matrixCahce.length; l < h; l += 7) {
                    this._matrixCahce[l] = -1
                }
                this._updateVertices();
                if (this._cachedFrameIndex < 0) {
                    var f = t >= 0;
                    if (this._localDirty) {
                        this._updateGlobalTransformMatrix(f)
                    }
                    if (f && this._cachedFrameIndices !== null) {
                        this._cachedFrameIndex = this._cachedFrameIndices[t] = this._armature._armatureData.setCacheFrame(this.globalTransformMatrix, this.global)
                    }
                } else {
                    this._armature._armatureData.getCacheFrame(this.globalTransformMatrix, this.global, this._cachedFrameIndex)
                }
                var u = 1e3;
                var _ = 200;
                var c = 2 * this.global.x;
                var m = 2 * this.global.y;
                var p = e._helpPoint;
                this.globalTransformMatrix.transformPoint(u, -_, p);
                this._hullCache[0] = p.x;
                this._hullCache[1] = p.y;
                this._hullCache[2] = c - p.x;
                this._hullCache[3] = m - p.y;
                this.globalTransformMatrix.transformPoint(0, this._dY, p, true);
                this._hullCache[4] = p.x;
                this._hullCache[5] = p.y;
                this.globalTransformMatrix.transformPoint(_, u, p);
                this._hullCache[6] = p.x;
                this._hullCache[7] = p.y;
                this._hullCache[8] = c - p.x;
                this._hullCache[9] = m - p.y;
                this.globalTransformMatrix.transformPoint(this._dX, 0, p, true);
                this._hullCache[10] = p.x;
                this._hullCache[11] = p.y
            } else if (this._childrenTransformDirty) {
                this._childrenTransformDirty = false
            }
            this._localDirty = true
        };
        return e
    } (t.Bone);
    t.Surface = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a._localMatrix = new t.Matrix;
            a._colorTransform = new t.ColorTransform;
            a._deformVertices = [];
            a._displayDatas = [];
            a._displayList = [];
            a._meshBones = [];
            a._meshSlots = [];
            a._rawDisplay = null;
            a._meshDisplay = null;
            return a
        }
        a.prototype._onClear = function() {
            e.prototype._onClear.call(this);
            var a = [];
            for (var i = 0,
                     r = this._displayList; i < r.length; i++) {
                var n = r[i];
                if (n !== null && n !== this._rawDisplay && n !== this._meshDisplay && a.indexOf(n) < 0) {
                    a.push(n)
                }
            }
            for (var s = 0,
                     o = a; s < o.length; s++) {
                var n = o[s];
                if (n instanceof t.Armature) {
                    n.dispose()
                } else {
                    this._disposeDisplay(n, true)
                }
            }
            if (this._meshDisplay !== null && this._meshDisplay !== this._rawDisplay) {
                this._disposeDisplay(this._meshDisplay, false)
            }
            if (this._rawDisplay !== null) {
                this._disposeDisplay(this._rawDisplay, false)
            }
            this.displayController = null;
            this._displayDirty = false;
            this._zOrderDirty = false;
            this._blendModeDirty = false;
            this._colorDirty = false;
            this._meshDirty = false;
            this._transformDirty = false;
            this._visible = true;
            this._blendMode = 0;
            this._displayIndex = -1;
            this._animationDisplayIndex = -1;
            this._zOrder = 0;
            this._cachedFrameIndex = -1;
            this._pivotX = 0;
            this._pivotY = 0;
            this._localMatrix.identity();
            this._colorTransform.identity();
            this._deformVertices.length = 0;
            this._displayList.length = 0;
            this._displayDatas.length = 0;
            this._meshBones.length = 0;
            this._meshSlots.length = 0;
            this._slotData = null;
            this._rawDisplayDatas = null;
            this._displayData = null;
            this._textureData = null;
            this._meshData = null;
            this._boundingBoxData = null;
            this._rawDisplay = null;
            this._meshDisplay = null;
            this._display = null;
            this._childArmature = null;
            this._cachedFrameIndices = null
        };
        a.prototype._isMeshBonesUpdate = function() {
            for (var t = 0,
                     e = this._meshBones; t < e.length; t++) {
                var a = e[t];
                if (a !== null && a._childrenTransformDirty) {
                    return true
                }
            }
            return false
        };
        a.prototype._getDefaultRawDisplayData = function(t) {
            var e = this._armature._armatureData.defaultSkin;
            if (e !== null) {
                var a = e.getDisplays(this._slotData.name);
                if (a !== null) {
                    return t < a.length ? a[t] : null
                }
            }
            return null
        };
        a.prototype._updateDisplayData = function() {
            var e = this._displayData;
            var i = this._textureData;
            var r = this._meshData;
            var n = null;
            if (this._displayIndex >= 0) {
                if (this._rawDisplayDatas !== null) {
                    n = this._displayIndex < this._rawDisplayDatas.length ? this._rawDisplayDatas[this._displayIndex] : null
                }
                if (n === null) {
                    n = this._getDefaultRawDisplayData(this._displayIndex)
                }
                if (this._displayIndex < this._displayDatas.length) {
                    this._displayData = this._displayDatas[this._displayIndex]
                }
            } else {
                n = null;
                this._displayData = null
            }
            if (this._displayData !== null) {
                if (this._displayData.type === 0 || this._displayData.type === 2) {
                    if (this._displayData.type === 2) {
                        this._textureData = this._displayData.texture;
                        this._meshData = this._displayData
                    } else if (n !== null && n.type === 2) {
                        this._textureData = this._displayData.texture;
                        this._meshData = n
                    } else {
                        this._textureData = this._displayData.texture;
                        this._meshData = null
                    }
                } else {
                    this._textureData = null;
                    this._meshData = null
                }
            } else {
                this._textureData = null;
                this._meshData = null
            }
            if (this._displayData !== null && this._displayData.type === 3) {
                this._boundingBoxData = this._displayData.boundingBox
            } else if (n !== null && n.type === 3) {
                this._boundingBoxData = n.boundingBox
            } else {
                this._boundingBoxData = null
            }
            if (this._displayData !== e || this._textureData !== i || this._meshData !== r) {
                if (this._meshData !== null) {
                    this._pivotX = 0;
                    this._pivotY = 0
                } else if (this._textureData !== null) {
                    var s = this._displayData;
                    var o = this._textureData.parent.scale * this._armature._armatureData.scale;
                    var l = this._textureData.frame;
                    this._pivotX = s.pivot.x;
                    this._pivotY = s.pivot.y;
                    var h = l !== null ? l: this._textureData.region;
                    var f = h.width;
                    var u = h.height;
                    if (this._textureData.rotated && l === null) {
                        f = h.height;
                        u = h.width
                    }
                    this._pivotX *= f * o;
                    this._pivotY *= u * o;
                    if (l !== null) {
                        this._pivotX += l.x * o;
                        this._pivotY += l.y * o
                    }
                    if (!t.DragonBones.yDown) {
                        this._pivotY -= (this._textureData.rotated ? this._textureData.region.width: this._textureData.region.height) * o
                    }
                } else {
                    this._pivotX = 0;
                    this._pivotY = 0
                }
                if (this._displayData !== null && n !== null && this._displayData !== n && this._meshData === null) {
                    n.transform.toMatrix(a._helpMatrix);
                    a._helpMatrix.invert();
                    a._helpMatrix.transformPoint(0, 0, a._helpPoint);
                    this._pivotX -= a._helpPoint.x;
                    this._pivotY -= a._helpPoint.y;
                    this._displayData.transform.toMatrix(a._helpMatrix);
                    a._helpMatrix.invert();
                    a._helpMatrix.transformPoint(0, 0, a._helpPoint);
                    this._pivotX += a._helpPoint.x;
                    this._pivotY += a._helpPoint.y
                }
                if (n !== null) {
                    this.origin = n.transform
                } else if (this._displayData !== null) {
                    this.origin = this._displayData.transform
                } else {
                    this.origin = null
                }
                if (this._meshData !== r) {
                    if (this._meshData !== null) {
                        if (this._meshData.weight !== null) {
                            this._deformVertices.length = this._meshData.weight.count * 2;
                            this._meshBones.length = this._meshData.weight.bones.length;
                            for (var _ = 0,
                                     c = this._meshBones.length; _ < c; ++_) {
                                this._meshBones[_] = this._armature.getBone(this._meshData.weight.bones[_].name)
                            }
                        } else {
                            var m = this._meshData.parent.parent.parent.intArray[this._meshData.offset + 0];
                            this._deformVertices.length = m * 2;
                            this._meshBones.length = 0
                        }
                        var p = this._armature._glueSlots;
                        if (this._meshData.glue !== null) {
                            this._meshSlots.length = this._meshData.glue.meshes.length;
                            for (var _ = 0,
                                     c = this._meshSlots.length; _ < c; ++_) {
                                var d = this._meshData.glue.meshes[_];
                                if (d !== null) {
                                    var y = false;
                                    for (var g = 0,
                                             v = this._armature.getSlots(); g < v.length; g++) {
                                        var b = v[g];
                                        for (var D = 0,
                                                 T = b._displayDatas; D < T.length; D++) {
                                            var A = T[D];
                                            if (A !== null && A.type === 2 && A.offset === d.offset) {
                                                y = true;
                                                this._meshSlots[_] = b;
                                                break
                                            }
                                        }
                                        if (y) {
                                            break
                                        }
                                    }
                                    if (!y) {
                                        this._meshSlots[_] = null
                                    }
                                } else {
                                    this._meshSlots[_] = null
                                }
                            }
                            if (p.indexOf(this) < 0) {
                                p.push(this)
                            }
                        } else {
                            var x = p.indexOf(this);
                            if (x >= 0) {
                                p.slice(x, 1)
                            }
                        }
                        for (var _ = 0,
                                 c = this._deformVertices.length; _ < c; ++_) {
                            this._deformVertices[_] = 0
                        }
                        this._meshDirty = true
                    } else {
                        this._deformVertices.length = 0;
                        this._meshBones.length = 0;
                        this._meshSlots.length = 0
                    }
                } else if (this._meshData !== null && this._textureData !== i) {
                    this._meshDirty = true
                }
                this._displayDirty = true;
                this._transformDirty = true
            }
        };
        a.prototype._updateDisplay = function() {
            var e = this._display !== null ? this._display: this._rawDisplay;
            var a = this._childArmature;
            if (this._displayIndex >= 0 && this._displayIndex < this._displayList.length) {
                this._display = this._displayList[this._displayIndex];
                if (this._display !== null && this._display instanceof t.Armature) {
                    this._childArmature = this._display;
                    this._display = this._childArmature.display
                } else {
                    this._childArmature = null
                }
            } else {
                this._display = null;
                this._childArmature = null
            }
            var i = this._display !== null ? this._display: this._rawDisplay;
            if (i !== e) {
                this._onUpdateDisplay();
                this._replaceDisplay(e);
                this._transformDirty = true;
                this._visibleDirty = true;
                this._blendModeDirty = true;
                this._colorDirty = true
            }
            if (i === this._rawDisplay || i === this._meshDisplay) {
                this._updateFrame()
            }
            if (this._childArmature !== a) {
                if (a !== null) {
                    a._parent = null;
                    a.clock = null;
                    if (a.inheritAnimation) {
                        a.animation.reset()
                    }
                }
                if (this._childArmature !== null) {
                    this._childArmature._parent = this;
                    this._childArmature.clock = this._armature.clock;
                    if (this._childArmature.inheritAnimation) {
                        if (this._childArmature.cacheFrameRate === 0) {
                            var r = this._armature.cacheFrameRate;
                            if (r !== 0) {
                                this._childArmature.cacheFrameRate = r
                            }
                        }
                        var n = null;
                        if (this._displayData !== null && this._displayData.type === 1) {
                            n = this._displayData.actions
                        } else if (this._displayIndex >= 0 && this._rawDisplayDatas !== null) {
                            var s = this._displayIndex < this._rawDisplayDatas.length ? this._rawDisplayDatas[this._displayIndex] : null;
                            if (s === null) {
                                s = this._getDefaultRawDisplayData(this._displayIndex)
                            }
                            if (s !== null && s.type === 1) {
                                n = s.actions
                            }
                        }
                        if (n !== null && n.length > 0) {
                            for (var o = 0,
                                     l = n; o < l.length; o++) {
                                var h = l[o];
                                var f = t.BaseObject.borrowObject(t.EventObject);
                                t.EventObject.actionDataToInstance(h, f, this._armature);
                                f.slot = this;
                                this._armature._bufferAction(f, false)
                            }
                        } else {
                            this._childArmature.animation.play()
                        }
                    }
                }
            }
        };
        a.prototype._updateGlobalTransformMatrix = function(t) {
            var e = this._parent._boneData.type === 0 ? this._parent.globalTransformMatrix: this._parent._getGlobalTransformMatrix(this.global.x, this.global.y);
            this.globalTransformMatrix.copyFrom(this._localMatrix);
            this.globalTransformMatrix.concat(e);
            if (t) {
                this.global.fromMatrix(this.globalTransformMatrix)
            } else {
                this._globalDirty = true
            }
        };
        a.prototype._setArmature = function(t) {
            if (this._armature === t) {
                return
            }
            if (this._armature !== null) {
                this._armature._removeSlotFromSlotList(this)
            }
            this._armature = t;
            this._onUpdateDisplay();
            if (this._armature !== null) {
                this._armature._addSlotToSlotList(this);
                this._addDisplay()
            } else {
                this._removeDisplay()
            }
        };
        a.prototype._setDisplayIndex = function(t, e) {
            if (e === void 0) {
                e = false
            }
            if (e) {
                if (this._animationDisplayIndex === t) {
                    return false
                }
                this._animationDisplayIndex = t
            }
            if (this._displayIndex === t) {
                return false
            }
            this._displayIndex = t;
            this._displayDirty = true;
            this._updateDisplayData();
            return this._displayDirty
        };
        a.prototype._setZorder = function(t) {
            if (this._zOrder === t) {}
            this._zOrder = t;
            this._zOrderDirty = true;
            return this._zOrderDirty
        };
        a.prototype._setColor = function(t) {
            this._colorTransform.copyFrom(t);
            this._colorDirty = true;
            return this._colorDirty
        };
        a.prototype._setDisplayList = function(e) {
            if (e !== null && e.length > 0) {
                if (this._displayList.length !== e.length) {
                    this._displayList.length = e.length
                }
                for (var a = 0,
                         i = e.length; a < i; ++a) {
                    var r = e[a];
                    if (r !== null && r !== this._rawDisplay && r !== this._meshDisplay && !(r instanceof t.Armature) && this._displayList.indexOf(r) < 0) {
                        this._initDisplay(r, true)
                    }
                    this._displayList[a] = r
                }
            } else if (this._displayList.length > 0) {
                this._displayList.length = 0
            }
            if (this._displayIndex >= 0 && this._displayIndex < this._displayList.length) {
                this._displayDirty = this._display !== this._displayList[this._displayIndex]
            } else {
                this._displayDirty = this._display !== null
            }
            this._updateDisplayData();
            return this._displayDirty
        };
        a.prototype.init = function(t, e, a, i) {
            if (this._slotData !== null) {
                return
            }
            this._slotData = t;
            this._visibleDirty = true;
            this._blendModeDirty = true;
            this._colorDirty = true;
            this._blendMode = this._slotData.blendMode;
            this._zOrder = this._slotData.zOrder;
            this._colorTransform.copyFrom(this._slotData.color);
            this._rawDisplay = a;
            this._meshDisplay = i;
            this.rawDisplayDatas = e;
            this._initDisplay(this._rawDisplay, false);
            if (this._rawDisplay !== this._meshDisplay) {
                this._initDisplay(this._meshDisplay, false)
            }
        };
        a.prototype.update = function(t) {
            if (this._displayDirty) {
                this._displayDirty = false;
                this._updateDisplay();
                if (this._transformDirty) {
                    if (this.origin !== null) {
                        this.global.copyFrom(this.origin).add(this.offset).toMatrix(this._localMatrix)
                    } else {
                        this.global.copyFrom(this.offset).toMatrix(this._localMatrix)
                    }
                }
            }
            if (this._zOrderDirty) {
                this._zOrderDirty = false;
                this._updateZOrder()
            }
            if (t >= 0 && this._cachedFrameIndices !== null) {
                var e = this._cachedFrameIndices[t];
                if (e >= 0 && this._cachedFrameIndex === e) {
                    this._transformDirty = false
                } else if (e >= 0) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = e
                } else if (this._transformDirty || this._parent._childrenTransformDirty) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1
                } else if (this._cachedFrameIndex >= 0) {
                    this._transformDirty = false;
                    this._cachedFrameIndices[t] = this._cachedFrameIndex
                } else {
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1
                }
            } else if (this._transformDirty || this._parent._childrenTransformDirty) {
                t = -1;
                this._transformDirty = true;
                this._cachedFrameIndex = -1
            }
            if (this._display === null) {
                return
            }
            if (this._visibleDirty) {
                this._visibleDirty = false;
                this._updateVisible()
            }
            if (this._blendModeDirty) {
                this._blendModeDirty = false;
                this._updateBlendMode()
            }
            if (this._colorDirty) {
                this._colorDirty = false;
                this._updateColor()
            }
            if (this._meshData !== null && this._display === this._meshDisplay) {
                var a = this._meshData.weight !== null;
                var i = this._parent._boneData.type !== 0;
                var r = this._meshData.glue !== null;
                if (this._meshDirty || a && this._isMeshBonesUpdate() || i && this._parent._childrenTransformDirty || r && this._parent._childrenTransformDirty) {
                    this._meshDirty = false;
                    this._updateMesh()
                }
                if (a || i || r) {
                    return
                }
            }
            if (this._transformDirty) {
                this._transformDirty = false;
                if (this._cachedFrameIndex < 0) {
                    var n = t >= 0;
                    this._updateGlobalTransformMatrix(n);
                    if (n && this._cachedFrameIndices !== null) {
                        this._cachedFrameIndex = this._cachedFrameIndices[t] = this._armature._armatureData.setCacheFrame(this.globalTransformMatrix, this.global)
                    }
                } else {
                    this._armature._armatureData.getCacheFrame(this.globalTransformMatrix, this.global, this._cachedFrameIndex)
                }
                this._updateTransform()
            }
        };
        a.prototype.updateTransformAndMatrix = function() {
            if (this._transformDirty) {
                this._transformDirty = false;
                this._updateGlobalTransformMatrix(false)
            }
        };
        a.prototype.replaceDisplayData = function(t, e) {
            if (e === void 0) {
                e = -1
            }
            if (e < 0) {
                if (this._displayIndex < 0) {
                    e = 0
                } else {
                    e = this._displayIndex
                }
            }
            if (this._displayDatas.length <= e) {
                this._displayDatas.length = e + 1;
                for (var a = 0,
                         i = this._displayDatas.length; a < i; ++a) {
                    if (!this._displayDatas[a]) {
                        this._displayDatas[a] = null
                    }
                }
            }
            this._displayDatas[e] = t
        };
        a.prototype.containsPoint = function(t, e) {
            if (this._boundingBoxData === null) {
                return false
            }
            this.updateTransformAndMatrix();
            a._helpMatrix.copyFrom(this.globalTransformMatrix);
            a._helpMatrix.invert();
            a._helpMatrix.transformPoint(t, e, a._helpPoint);
            return this._boundingBoxData.containsPoint(a._helpPoint.x, a._helpPoint.y)
        };
        a.prototype.intersectsSegment = function(t, e, i, r, n, s, o) {
            if (n === void 0) {
                n = null
            }
            if (s === void 0) {
                s = null
            }
            if (o === void 0) {
                o = null
            }
            if (this._boundingBoxData === null) {
                return 0
            }
            this.updateTransformAndMatrix();
            a._helpMatrix.copyFrom(this.globalTransformMatrix);
            a._helpMatrix.invert();
            a._helpMatrix.transformPoint(t, e, a._helpPoint);
            t = a._helpPoint.x;
            e = a._helpPoint.y;
            a._helpMatrix.transformPoint(i, r, a._helpPoint);
            i = a._helpPoint.x;
            r = a._helpPoint.y;
            var l = this._boundingBoxData.intersectsSegment(t, e, i, r, n, s, o);
            if (l > 0) {
                if (l === 1 || l === 2) {
                    if (n !== null) {
                        this.globalTransformMatrix.transformPoint(n.x, n.y, n);
                        if (s !== null) {
                            s.x = n.x;
                            s.y = n.y
                        }
                    } else if (s !== null) {
                        this.globalTransformMatrix.transformPoint(s.x, s.y, s)
                    }
                } else {
                    if (n !== null) {
                        this.globalTransformMatrix.transformPoint(n.x, n.y, n)
                    }
                    if (s !== null) {
                        this.globalTransformMatrix.transformPoint(s.x, s.y, s)
                    }
                }
                if (o !== null) {
                    this.globalTransformMatrix.transformPoint(Math.cos(o.x), Math.sin(o.x), a._helpPoint, true);
                    o.x = Math.atan2(a._helpPoint.y, a._helpPoint.x);
                    this.globalTransformMatrix.transformPoint(Math.cos(o.y), Math.sin(o.y), a._helpPoint, true);
                    o.y = Math.atan2(a._helpPoint.y, a._helpPoint.x)
                }
            }
            return l
        };
        a.prototype.invalidUpdate = function() {
            this._displayDirty = true;
            this._transformDirty = true
        };
        Object.defineProperty(a.prototype, "visible", {
            get: function() {
                return this._visible
            },
            set: function(t) {
                if (this._visible === t) {
                    return
                }
                this._visible = t;
                this._updateVisible()
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "displayIndex", {
            get: function() {
                return this._displayIndex
            },
            set: function(t) {
                if (this._setDisplayIndex(t)) {
                    this.update( - 1)
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "name", {
            get: function() {
                return this._slotData.name
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "displayList", {
            get: function() {
                return this._displayList.concat()
            },
            set: function(e) {
                var a = this._displayList.concat();
                var i = new Array;
                if (this._setDisplayList(e)) {
                    this.update( - 1)
                }
                for (var r = 0,
                         n = a; r < n.length; r++) {
                    var s = n[r];
                    if (s !== null && s !== this._rawDisplay && s !== this._meshDisplay && this._displayList.indexOf(s) < 0 && i.indexOf(s) < 0) {
                        i.push(s)
                    }
                }
                for (var o = 0,
                         l = i; o < l.length; o++) {
                    var s = l[o];
                    if (s instanceof t.Armature) {} else {
                        this._disposeDisplay(s, true)
                    }
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "slotData", {
            get: function() {
                return this._slotData
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "rawDisplayDatas", {
            get: function() {
                return this._rawDisplayDatas
            },
            set: function(t) {
                if (this._rawDisplayDatas === t) {
                    return
                }
                this._displayDirty = true;
                this._rawDisplayDatas = t;
                if (this._rawDisplayDatas !== null) {
                    this._displayDatas.length = this._rawDisplayDatas.length;
                    for (var e = 0,
                             a = this._displayDatas.length; e < a; ++e) {
                        var i = this._rawDisplayDatas[e];
                        if (i === null) {
                            i = this._getDefaultRawDisplayData(e)
                        }
                        this._displayDatas[e] = i
                    }
                } else {
                    this._displayDatas.length = 0
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "boundingBoxData", {
            get: function() {
                return this._boundingBoxData
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "rawDisplay", {
            get: function() {
                return this._rawDisplay
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "meshDisplay", {
            get: function() {
                return this._meshDisplay
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "display", {
            get: function() {
                return this._display
            },
            set: function(t) {
                if (this._display === t) {
                    return
                }
                var e = this._displayList.length;
                if (this._displayIndex < 0 && e === 0) {
                    this._displayIndex = 0
                }
                if (this._displayIndex < 0) {
                    return
                } else {
                    var a = this.displayList;
                    if (e <= this._displayIndex) {
                        a.length = this._displayIndex + 1
                    }
                    a[this._displayIndex] = t;
                    this.displayList = a
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "childArmature", {
            get: function() {
                return this._childArmature
            },
            set: function(t) {
                if (this._childArmature === t) {
                    return
                }
                this.display = t
            },
            enumerable: true,
            configurable: true
        });
        a.prototype.getDisplay = function() {
            return this.display
        };
        a.prototype.setDisplay = function(t) {
            this.display = t
        };
        return a
    } (t.TransformObject);
    t.Slot = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        a.prototype._onClear = function() {
            this._armature = null;
            this._target = null;
            this._root = null;
            this._bone = null
        };
        Object.defineProperty(a.prototype, "name", {
            get: function() {
                return this._constraintData.name
            },
            enumerable: true,
            configurable: true
        });
        a._helpMatrix = new t.Matrix;
        a._helpTransform = new t.Transform;
        a._helpPoint = new t.Point;
        return a
    } (t.BaseObject);
    t.Constraint = e;
    var a = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        a.toString = function() {
            return "[class dragonBones.IKConstraint]"
        };
        a.prototype._onClear = function() {
            e.prototype._onClear.call(this);
            this._scaleEnabled = false;
            this._bendPositive = false;
            this._weight = 1;
            this._constraintData = null
        };
        a.prototype._computeA = function() {
            var e = this._target.global;
            var a = this._root.global;
            var i = this._root.globalTransformMatrix;
            var r = Math.atan2(e.y - a.y, e.x - a.x);
            if (a.scaleX < 0) {
                r += Math.PI
            }
            a.rotation += t.Transform.normalizeRadian(r - a.rotation) * this._weight;
            a.toMatrix(i)
        };
        a.prototype._computeB = function() {
            var e = this._bone._boneData.length;
            var a = this._root;
            var i = this._target.global;
            var r = a.global;
            var n = this._bone.global;
            var s = this._bone.globalTransformMatrix;
            var o = s.a * e;
            var l = s.b * e;
            var h = o * o + l * l;
            var f = Math.sqrt(h);
            var u = n.x - r.x;
            var _ = n.y - r.y;
            var c = u * u + _ * _;
            var m = Math.sqrt(c);
            var p = n.rotation;
            var d = r.rotation;
            var y = Math.atan2(_, u);
            u = i.x - r.x;
            _ = i.y - r.y;
            var g = u * u + _ * _;
            var v = Math.sqrt(g);
            var b = 0;
            if (f + m <= v || v + f <= m || v + m <= f) {
                b = Math.atan2(i.y - r.y, i.x - r.x);
                if (f + m <= v) {} else if (m < f) {
                    b += Math.PI
                }
            } else {
                var D = (c - h + g) / (2 * g);
                var T = Math.sqrt(c - D * D * g) / v;
                var A = r.x + u * D;
                var x = r.y + _ * D;
                var P = -_ * T;
                var O = u * T;
                var S = false;
                if (a._parent !== null) {
                    var E = a._parent.globalTransformMatrix;
                    S = E.a * E.d - E.b * E.c < 0
                }
                if (S !== this._bendPositive) {
                    n.x = A - P;
                    n.y = x - O
                } else {
                    n.x = A + P;
                    n.y = x + O
                }
                b = Math.atan2(n.y - r.y, n.x - r.x)
            }
            var B = t.Transform.normalizeRadian(b - y);
            r.rotation = d + B * this._weight;
            r.toMatrix(a.globalTransformMatrix);
            var M = y + B * this._weight;
            n.x = r.x + Math.cos(M) * m;
            n.y = r.y + Math.sin(M) * m;
            var w = Math.atan2(i.y - n.y, i.x - n.x);
            if (n.scaleX < 0) {
                w += Math.PI
            }
            n.rotation = r.rotation + p - d + t.Transform.normalizeRadian(w - B - p) * this._weight;
            n.toMatrix(s)
        };
        a.prototype.init = function(t, e) {
            if (this._constraintData !== null) {
                return
            }
            this._constraintData = t;
            this._armature = e;
            this._target = this._armature.getBone(this._constraintData.target.name);
            this._root = this._armature.getBone(this._constraintData.root.name);
            this._bone = this._constraintData.bone !== null ? this._armature.getBone(this._constraintData.bone.name) : null; {
                var a = this._constraintData;
                this._scaleEnabled = a.scaleEnabled;
                this._scaleEnabled = this._scaleEnabled;
                this._bendPositive = a.bendPositive;
                this._weight = a.weight
            }
            this._root._hasConstraint = true
        };
        a.prototype.update = function() {
            this._root.updateByConstraint();
            if (this._bone !== null) {
                this._bone.updateByConstraint();
                this._computeB()
            } else {
                this._computeA()
            }
        };
        a.prototype.invalidUpdate = function() {
            this._root.invalidUpdate();
            if (this._bone !== null) {
                this._bone.invalidUpdate()
            }
        };
        return a
    } (e);
    t.IKConstraint = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function t(t) {
            if (t === void 0) {
                t = 0
            }
            this.time = 0;
            this.timeScale = 1;
            this._systemTime = 0;
            this._animatebles = [];
            this._clock = null;
            this.time = t;
            this._systemTime = (new Date).getTime() * .001
        }
        t.prototype.advanceTime = function(t) {
            if (t !== t) {
                t = 0
            }
            var e = Date.now() * .001;
            if (t < 0) {
                t = e - this._systemTime
            }
            this._systemTime = e;
            if (this.timeScale !== 1) {
                t *= this.timeScale
            }
            if (t === 0) {
                return
            }
            if (t < 0) {
                this.time -= t
            } else {
                this.time += t
            }
            var a = 0,
                i = 0,
                r = this._animatebles.length;
            for (; a < r; ++a) {
                var n = this._animatebles[a];
                if (n !== null) {
                    if (i > 0) {
                        this._animatebles[a - i] = n;
                        this._animatebles[a] = null
                    }
                    n.advanceTime(t)
                } else {
                    i++
                }
            }
            if (i > 0) {
                r = this._animatebles.length;
                for (; a < r; ++a) {
                    var s = this._animatebles[a];
                    if (s !== null) {
                        this._animatebles[a - i] = s
                    } else {
                        i++
                    }
                }
                this._animatebles.length -= i
            }
        };
        t.prototype.contains = function(t) {
            if (t === this) {
                return false
            }
            var e = t;
            while (e !== this && e !== null) {
                e = e.clock
            }
            return e === this
        };
        t.prototype.add = function(t) {
            if (this._animatebles.indexOf(t) < 0) {
                this._animatebles.push(t);
                t.clock = this
            }
        };
        t.prototype.remove = function(t) {
            var e = this._animatebles.indexOf(t);
            if (e >= 0) {
                this._animatebles[e] = null;
                t.clock = null
            }
        };
        t.prototype.clear = function() {
            for (var t = 0,
                     e = this._animatebles; t < e.length; t++) {
                var a = e[t];
                if (a !== null) {
                    a.clock = null
                }
            }
        };
        Object.defineProperty(t.prototype, "clock", {
            get: function() {
                return this._clock
            },
            set: function(t) {
                if (this._clock === t) {
                    return
                }
                if (this._clock !== null) {
                    this._clock.remove(this)
                }
                this._clock = t;
                if (this._clock !== null) {
                    this._clock.add(this)
                }
            },
            enumerable: true,
            configurable: true
        });
        t.clock = new t;
        return t
    } ();
    t.WorldClock = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var t = e !== null && e.apply(this, arguments) || this;
            t._animationNames = [];
            t._animationStates = [];
            t._animations = {};
            t._animationConfig = null;
            return t
        }
        a.toString = function() {
            return "[class dragonBones.Animation]"
        };
        a.prototype._onClear = function() {
            for (var t = 0,
                     e = this._animationStates; t < e.length; t++) {
                var a = e[t];
                a.returnToPool()
            }
            for (var i in this._animations) {
                delete this._animations[i]
            }
            if (this._animationConfig !== null) {
                this._animationConfig.returnToPool()
            }
            this.timeScale = 1;
            this._lockUpdate = false;
            this._animationDirty = false;
            this._inheritTimeScale = 1;
            this._animationNames.length = 0;
            this._animationStates.length = 0;
            this._armature = null;
            this._animationConfig = null;
            this._lastAnimationState = null
        };
        a.prototype._fadeOut = function(t) {
            switch (t.fadeOutMode) {
                case 1:
                    for (var e = 0,
                             a = this._animationStates; e < a.length; e++) {
                        var i = a[e];
                        if (i._parent !== null) {
                            continue
                        }
                        if (i.layer === t.layer) {
                            i.fadeOut(t.fadeOutTime, t.pauseFadeOut)
                        }
                    }
                    break;
                case 2:
                    for (var r = 0,
                             n = this._animationStates; r < n.length; r++) {
                        var i = n[r];
                        if (i._parent !== null) {
                            continue
                        }
                        if (i.group === t.group) {
                            i.fadeOut(t.fadeOutTime, t.pauseFadeOut)
                        }
                    }
                    break;
                case 3:
                    for (var s = 0,
                             o = this._animationStates; s < o.length; s++) {
                        var i = o[s];
                        if (i._parent !== null) {
                            continue
                        }
                        if (i.layer === t.layer && i.group === t.group) {
                            i.fadeOut(t.fadeOutTime, t.pauseFadeOut)
                        }
                    }
                    break;
                case 4:
                    for (var l = 0,
                             h = this._animationStates; l < h.length; l++) {
                        var i = h[l];
                        if (i._parent !== null) {
                            continue
                        }
                        i.fadeOut(t.fadeOutTime, t.pauseFadeOut)
                    }
                    break;
                case 0:
                case 5:
                default:
                    break
            }
        };
        a.prototype.init = function(e) {
            if (this._armature !== null) {
                return
            }
            this._armature = e;
            this._animationConfig = t.BaseObject.borrowObject(t.AnimationConfig)
        };
        a.prototype.advanceTime = function(t) {
            if (t < 0) {
                t = -t
            }
            if (this._armature.inheritAnimation && this._armature._parent !== null) {
                this._inheritTimeScale = this._armature._parent._armature.animation._inheritTimeScale * this.timeScale
            } else {
                this._inheritTimeScale = this.timeScale
            }
            if (this._inheritTimeScale !== 1) {
                t *= this._inheritTimeScale
            }
            var e = this._animationStates.length;
            if (e === 1) {
                var a = this._animationStates[0];
                if (a._fadeState > 0 && a._subFadeState > 0) {
                    this._armature._dragonBones.bufferObject(a);
                    this._animationStates.length = 0;
                    this._lastAnimationState = null
                } else {
                    var i = a._animationData;
                    var r = i.cacheFrameRate;
                    if (this._animationDirty && r > 0) {
                        this._animationDirty = false;
                        for (var n = 0,
                                 s = this._armature.getBones(); n < s.length; n++) {
                            var o = s[n];
                            o._cachedFrameIndices = i.getBoneCachedFrameIndices(o.name)
                        }
                        for (var l = 0,
                                 h = this._armature.getSlots(); l < h.length; l++) {
                            var f = h[l];
                            var u = f.rawDisplayDatas;
                            if (u !== null && u.length > 0) {
                                var _ = u[0];
                                if (_ !== null) {
                                    if (_.parent === this._armature.armatureData.defaultSkin) {
                                        f._cachedFrameIndices = i.getSlotCachedFrameIndices(f.name);
                                        continue
                                    }
                                }
                            }
                            f._cachedFrameIndices = null
                        }
                    }
                    a.advanceTime(t, r)
                }
            } else if (e > 1) {
                for (var c = 0,
                         m = 0; c < e; ++c) {
                    var a = this._animationStates[c];
                    if (a._fadeState > 0 && a._subFadeState > 0) {
                        m++;
                        this._armature._dragonBones.bufferObject(a);
                        this._animationDirty = true;
                        if (this._lastAnimationState === a) {
                            this._lastAnimationState = null
                        }
                    } else {
                        if (m > 0) {
                            this._animationStates[c - m] = a
                        }
                        a.advanceTime(t, 0)
                    }
                    if (c === e - 1 && m > 0) {
                        this._animationStates.length -= m;
                        if (this._lastAnimationState === null && this._animationStates.length > 0) {
                            this._lastAnimationState = this._animationStates[this._animationStates.length - 1]
                        }
                    }
                }
                this._armature._cacheFrameIndex = -1
            } else {
                this._armature._cacheFrameIndex = -1
            }
        };
        a.prototype.reset = function() {
            for (var t = 0,
                     e = this._animationStates; t < e.length; t++) {
                var a = e[t];
                a.returnToPool()
            }
            this._animationDirty = false;
            this._animationConfig.clear();
            this._animationStates.length = 0;
            this._lastAnimationState = null
        };
        a.prototype.stop = function(t) {
            if (t === void 0) {
                t = null
            }
            if (t !== null) {
                var e = this.getState(t);
                if (e !== null) {
                    e.stop()
                }
            } else {
                for (var a = 0,
                         i = this._animationStates; a < i.length; a++) {
                    var e = i[a];
                    e.stop()
                }
            }
        };
        a.prototype.playConfig = function(e) {
            var a = e.animation;
            if (! (a in this._animations)) {
                console.warn("Non-existent animation.\n", "DragonBones name: " + this._armature.armatureData.parent.name, "Armature name: " + this._armature.name, "Animation name: " + a);
                return null
            }
            var i = this._animations[a];
            if (e.fadeOutMode === 5) {
                for (var r = 0,
                         n = this._animationStates; r < n.length; r++) {
                    var s = n[r];
                    if (s._animationData === i) {
                        return s
                    }
                }
            }
            if (this._animationStates.length === 0) {
                e.fadeInTime = 0
            } else if (e.fadeInTime < 0) {
                e.fadeInTime = i.fadeInTime
            }
            if (e.fadeOutTime < 0) {
                e.fadeOutTime = e.fadeInTime
            }
            if (e.timeScale <= -100) {
                e.timeScale = 1 / i.scale
            }
            if (i.frameCount > 1) {
                if (e.position < 0) {
                    e.position %= i.duration;
                    e.position = i.duration - e.position
                } else if (e.position === i.duration) {
                    e.position -= 1e-6
                } else if (e.position > i.duration) {
                    e.position %= i.duration
                }
                if (e.duration > 0 && e.position + e.duration > i.duration) {
                    e.duration = i.duration - e.position
                }
                if (e.playTimes < 0) {
                    e.playTimes = i.playTimes
                }
            } else {
                e.playTimes = 1;
                e.position = 0;
                if (e.duration > 0) {
                    e.duration = 0
                }
            }
            if (e.duration === 0) {
                e.duration = -1
            }
            this._fadeOut(e);
            var o = t.BaseObject.borrowObject(t.AnimationState);
            o.init(this._armature, i, e);
            this._animationDirty = true;
            this._armature._cacheFrameIndex = -1;
            if (this._animationStates.length > 0) {
                var l = false;
                for (var h = 0,
                         f = this._animationStates.length; h < f; ++h) {
                    if (o.layer > this._animationStates[h].layer) {
                        l = true;
                        this._animationStates.splice(h, 0, o);
                        break
                    } else if (h !== f - 1 && o.layer > this._animationStates[h + 1].layer) {
                        l = true;
                        this._animationStates.splice(h + 1, 0, o);
                        break
                    }
                }
                if (!l) {
                    this._animationStates.push(o)
                }
            } else {
                this._animationStates.push(o)
            }
            for (var u = 0,
                     _ = this._armature.getSlots(); u < _.length; u++) {
                var c = _[u];
                var m = c.childArmature;
                if (m !== null && m.inheritAnimation && m.animation.hasAnimation(a) && m.animation.getState(a) === null) {
                    m.animation.fadeIn(a)
                }
            }
            var p = false;
            for (var d in i.animationTimelines) {
                if (!this._lockUpdate) {
                    p = true;
                    this._lockUpdate = true
                }
                var y = this.fadeIn(d, e.fadeInTime, 1, o.layer, null, 0);
                if (y !== null) {
                    y.resetToPose = false;
                    y._parent = o;
                    y.stop()
                }
            }
            if (p) {
                this._lockUpdate = false
            }
            if (!this._lockUpdate) {
                if (e.fadeInTime <= 0) {
                    this._armature.advanceTime(0)
                }
                this._lastAnimationState = o
            }
            return o
        };
        a.prototype.play = function(t, e) {
            if (t === void 0) {
                t = null
            }
            if (e === void 0) {
                e = -1
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = e;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t !== null ? t: "";
            if (t !== null && t.length > 0) {
                this.playConfig(this._animationConfig)
            } else if (this._lastAnimationState === null) {
                var a = this._armature.armatureData.defaultAnimation;
                if (a !== null) {
                    this._animationConfig.animation = a.name;
                    this.playConfig(this._animationConfig)
                }
            } else if (!this._lastAnimationState.isPlaying && !this._lastAnimationState.isCompleted) {
                this._lastAnimationState.play()
            } else {
                this._animationConfig.animation = this._lastAnimationState.name;
                this.playConfig(this._animationConfig)
            }
            return this._lastAnimationState
        };
        a.prototype.fadeIn = function(t, e, a, i, r, n) {
            if (e === void 0) {
                e = -1
            }
            if (a === void 0) {
                a = -1
            }
            if (i === void 0) {
                i = 0
            }
            if (r === void 0) {
                r = null
            }
            if (n === void 0) {
                n = 3
            }
            this._animationConfig.clear();
            this._animationConfig.fadeOutMode = n;
            this._animationConfig.playTimes = a;
            this._animationConfig.layer = i;
            this._animationConfig.fadeInTime = e;
            this._animationConfig.animation = t;
            this._animationConfig.group = r !== null ? r: "";
            return this.playConfig(this._animationConfig)
        };
        a.prototype.gotoAndPlayByTime = function(t, e, a) {
            if (e === void 0) {
                e = 0
            }
            if (a === void 0) {
                a = -1
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = a;
            this._animationConfig.position = e;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t;
            return this.playConfig(this._animationConfig)
        };
        a.prototype.gotoAndPlayByFrame = function(t, e, a) {
            if (e === void 0) {
                e = 0
            }
            if (a === void 0) {
                a = -1
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = a;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t;
            var i = t in this._animations ? this._animations[t] : null;
            if (i !== null) {
                this._animationConfig.position = i.duration * e / i.frameCount
            }
            return this.playConfig(this._animationConfig)
        };
        a.prototype.gotoAndPlayByProgress = function(t, e, a) {
            if (e === void 0) {
                e = 0
            }
            if (a === void 0) {
                a = -1
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = a;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t;
            var i = t in this._animations ? this._animations[t] : null;
            if (i !== null) {
                this._animationConfig.position = i.duration * (e > 0 ? e: 0)
            }
            return this.playConfig(this._animationConfig)
        };
        a.prototype.gotoAndStopByTime = function(t, e) {
            if (e === void 0) {
                e = 0
            }
            var a = this.gotoAndPlayByTime(t, e, 1);
            if (a !== null) {
                a.stop()
            }
            return a
        };
        a.prototype.gotoAndStopByFrame = function(t, e) {
            if (e === void 0) {
                e = 0
            }
            var a = this.gotoAndPlayByFrame(t, e, 1);
            if (a !== null) {
                a.stop()
            }
            return a
        };
        a.prototype.gotoAndStopByProgress = function(t, e) {
            if (e === void 0) {
                e = 0
            }
            var a = this.gotoAndPlayByProgress(t, e, 1);
            if (a !== null) {
                a.stop()
            }
            return a
        };
        a.prototype.getState = function(t) {
            var e = this._animationStates.length;
            while (e--) {
                var a = this._animationStates[e];
                if (a.name === t) {
                    return a
                }
            }
            return null
        };
        a.prototype.hasAnimation = function(t) {
            return t in this._animations
        };
        a.prototype.getStates = function() {
            return this._animationStates
        };
        Object.defineProperty(a.prototype, "isPlaying", {
            get: function() {
                for (var t = 0,
                         e = this._animationStates; t < e.length; t++) {
                    var a = e[t];
                    if (a.isPlaying) {
                        return true
                    }
                }
                return false
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "isCompleted", {
            get: function() {
                for (var t = 0,
                         e = this._animationStates; t < e.length; t++) {
                    var a = e[t];
                    if (!a.isCompleted) {
                        return false
                    }
                }
                return this._animationStates.length > 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "lastAnimationName", {
            get: function() {
                return this._lastAnimationState !== null ? this._lastAnimationState.name: ""
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animationNames", {
            get: function() {
                return this._animationNames
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animations", {
            get: function() {
                return this._animations
            },
            set: function(t) {
                if (this._animations === t) {
                    return
                }
                this._animationNames.length = 0;
                for (var e in this._animations) {
                    delete this._animations[e]
                }
                for (var e in t) {
                    this._animationNames.push(e);
                    this._animations[e] = t[e]
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animationConfig", {
            get: function() {
                this._animationConfig.clear();
                return this._animationConfig
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "lastAnimationState", {
            get: function() {
                return this._lastAnimationState
            },
            enumerable: true,
            configurable: true
        });
        a.prototype.gotoAndPlay = function(t, e, a, i, r, n, s, o, l) {
            if (e === void 0) {
                e = -1
            }
            if (a === void 0) {
                a = -1
            }
            if (i === void 0) {
                i = -1
            }
            if (r === void 0) {
                r = 0
            }
            if (n === void 0) {
                n = null
            }
            if (s === void 0) {
                s = 3
            }
            if (o === void 0) {
                o = true
            }
            if (l === void 0) {
                l = true
            }
            o;
            l;
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.fadeOutMode = s;
            this._animationConfig.playTimes = i;
            this._animationConfig.layer = r;
            this._animationConfig.fadeInTime = e;
            this._animationConfig.animation = t;
            this._animationConfig.group = n !== null ? n: "";
            var h = this._animations[t];
            if (h && a > 0) {
                this._animationConfig.timeScale = h.duration / a
            }
            return this.playConfig(this._animationConfig)
        };
        a.prototype.gotoAndStop = function(t, e) {
            if (e === void 0) {
                e = 0
            }
            return this.gotoAndStopByTime(t, e)
        };
        Object.defineProperty(a.prototype, "animationList", {
            get: function() {
                return this._animationNames
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animationDataList", {
            get: function() {
                var t = [];
                for (var e = 0,
                         a = this._animationNames.length; e < a; ++e) {
                    t.push(this._animations[this._animationNames[e]])
                }
                return t
            },
            enumerable: true,
            configurable: true
        });
        return a
    } (t.BaseObject);
    t.Animation = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(r, e);
        function r() {
            var t = e !== null && e.apply(this, arguments) || this;
            t._blendState = new i;
            t._boneMask = [];
            t._boneTimelines = [];
            t._surfaceTimelines = [];
            t._slotTimelines = [];
            t._constraintTimelines = [];
            t._animationTimelines = [];
            t._poseTimelines = [];
            t._bonePoses = {};
            t._actionTimeline = null;
            t._zOrderTimeline = null;
            t._parent = null;
            return t
        }
        r.toString = function() {
            return "[class dragonBones.AnimationState]"
        };
        r.prototype._onClear = function() {
            for (var t = 0,
                     e = this._boneTimelines; t < e.length; t++) {
                var a = e[t];
                a.returnToPool()
            }
            for (var i = 0,
                     r = this._surfaceTimelines; i < r.length; i++) {
                var a = r[i];
                a.returnToPool()
            }
            for (var n = 0,
                     s = this._slotTimelines; n < s.length; n++) {
                var a = s[n];
                a.returnToPool()
            }
            for (var o = 0,
                     l = this._constraintTimelines; o < l.length; o++) {
                var a = l[o];
                a.returnToPool()
            }
            for (var h = 0,
                     f = this._animationTimelines; h < f.length; h++) {
                var a = f[h];
                a.returnToPool()
            }
            for (var u in this._bonePoses) {
                this._bonePoses[u].returnToPool();
                delete this._bonePoses[u]
            }
            if (this._actionTimeline !== null) {
                this._actionTimeline.returnToPool()
            }
            if (this._zOrderTimeline !== null) {
                this._zOrderTimeline.returnToPool()
            }
            this.actionEnabled = false;
            this.additiveBlending = false;
            this.displayControl = false;
            this.resetToPose = false;
            this.playTimes = 1;
            this.layer = 0;
            this.timeScale = 1;
            this.weight = 1;
            this.autoFadeOutTime = 0;
            this.fadeTotalTime = 0;
            this.name = "";
            this.group = "";
            this._timelineDirty = 2;
            this._playheadState = 0;
            this._fadeState = -1;
            this._subFadeState = -1;
            this._position = 0;
            this._duration = 0;
            this._fadeTime = 0;
            this._time = 0;
            this._fadeProgress = 0;
            this._weightResult = 0;
            this._blendState.clear();
            this._boneMask.length = 0;
            this._boneTimelines.length = 0;
            this._surfaceTimelines.length = 0;
            this._slotTimelines.length = 0;
            this._constraintTimelines.length = 0;
            this._animationTimelines.length = 0;
            this._poseTimelines.length = 0;
            this._animationData = null;
            this._armature = null;
            this._actionTimeline = null;
            this._zOrderTimeline = null;
            this._parent = null
        };
        r.prototype._updateTimelines = function() {
            {
                for (var e = 0,
                         a = this._armature._constraints; e < a.length; e++) {
                    var i = a[e];
                    var r = this._animationData.getConstraintTimelines(i.name);
                    if (r !== null) {
                        for (var n = 0,
                                 s = r; n < s.length; n++) {
                            var o = s[n];
                            switch (o.type) {
                                case 30:
                                {
                                    var l = t.BaseObject.borrowObject(t.IKConstraintTimelineState);
                                    l.constraint = i;
                                    l.init(this._armature, this, o);
                                    this._constraintTimelines.push(l);
                                    break
                                }
                                default:
                                    break
                            }
                        }
                    } else if (this.resetToPose) {
                        var l = t.BaseObject.borrowObject(t.IKConstraintTimelineState);
                        l.constraint = i;
                        l.init(this._armature, this, null);
                        this._constraintTimelines.push(l);
                        this._poseTimelines.push(l)
                    }
                }
            } {
                for (var h = 0,
                         f = this._armature.animation.getStates(); h < f.length; h++) {
                    var u = f[h];
                    if (u._parent !== this) {
                        continue
                    }
                    var r = this._animationData.getAnimationTimelines(u.name);
                    if (r === null) {
                        continue
                    }
                    for (var _ = 0,
                             c = r; _ < c.length; _++) {
                        var o = c[_];
                        switch (o.type) {
                            case 40:
                            {
                                var l = t.BaseObject.borrowObject(t.AnimationTimelineState);
                                l.animationState = u;
                                l.init(this._armature, this, o);
                                this._animationTimelines.push(l);
                                break
                            }
                            default:
                                break
                        }
                    }
                }
            }
        };
        r.prototype._updateBoneAndSlotTimelines = function() {
            {
                var e = {};
                for (var i = 0,
                         r = this._boneTimelines; i < r.length; i++) {
                    var n = r[i];
                    var s = n.bone.name;
                    if (! (s in e)) {
                        e[s] = []
                    }
                    e[s].push(n)
                }
                for (var o = 0,
                         l = this._armature.getBones(); o < l.length; o++) {
                    var h = l[o];
                    var s = h.name;
                    if (!this.containsBoneMask(s)) {
                        continue
                    }
                    if (s in e) {
                        delete e[s]
                    } else if (h._boneData.type === 0) {
                        var f = this._animationData.getBoneTimelines(s);
                        var u = s in this._bonePoses ? this._bonePoses[s] : this._bonePoses[s] = t.BaseObject.borrowObject(a);
                        if (f !== null) {
                            for (var _ = 0,
                                     c = f; _ < c.length; _++) {
                                var m = c[_];
                                switch (m.type) {
                                    case 10:
                                    {
                                        var n = t.BaseObject.borrowObject(t.BoneAllTimelineState);
                                        n.bone = h;
                                        n.bonePose = u;
                                        n.init(this._armature, this, m);
                                        this._boneTimelines.push(n);
                                        break
                                    }
                                    case 11:
                                    {
                                        var n = t.BaseObject.borrowObject(t.BoneTranslateTimelineState);
                                        n.bone = h;
                                        n.bonePose = u;
                                        n.init(this._armature, this, m);
                                        this._boneTimelines.push(n);
                                        break
                                    }
                                    case 12:
                                    {
                                        var n = t.BaseObject.borrowObject(t.BoneRotateTimelineState);
                                        n.bone = h;
                                        n.bonePose = u;
                                        n.init(this._armature, this, m);
                                        this._boneTimelines.push(n);
                                        break
                                    }
                                    case 13:
                                    {
                                        var n = t.BaseObject.borrowObject(t.BoneScaleTimelineState);
                                        n.bone = h;
                                        n.bonePose = u;
                                        n.init(this._armature, this, m);
                                        this._boneTimelines.push(n);
                                        break
                                    }
                                    default:
                                        break
                                }
                            }
                        } else if (this.resetToPose) {
                            var n = t.BaseObject.borrowObject(t.BoneAllTimelineState);
                            n.bone = h;
                            n.bonePose = u;
                            n.init(this._armature, this, null);
                            this._boneTimelines.push(n);
                            this._poseTimelines.push(n)
                        }
                    } else if (h._boneData.type === 1) {
                        var f = this._animationData.getSurfaceTimelines(s);
                        if (f !== null) {
                            for (var p = 0,
                                     d = f; p < d.length; p++) {
                                var m = d[p];
                                switch (m.type) {
                                    case 50:
                                    {
                                        var n = t.BaseObject.borrowObject(t.SurfaceTimelineState);
                                        n.surface = h;
                                        n.init(this._armature, this, m);
                                        this._surfaceTimelines.push(n);
                                        break
                                    }
                                    default:
                                        break
                                }
                            }
                        } else if (this.resetToPose) {
                            var n = t.BaseObject.borrowObject(t.SurfaceTimelineState);
                            n.surface = h;
                            n.init(this._armature, this, null);
                            this._surfaceTimelines.push(n);
                            this._poseTimelines.push(n)
                        }
                    }
                }
                for (var y in e) {
                    for (var g = 0,
                             v = e[y]; g < v.length; g++) {
                        var n = v[g];
                        this._boneTimelines.splice(this._boneTimelines.indexOf(n), 1);
                        n.returnToPool()
                    }
                }
            } {
                var b = {};
                var D = [];
                for (var T = 0,
                         A = this._slotTimelines; T < A.length; T++) {
                    var n = A[T];
                    var s = n.slot.name;
                    if (! (s in b)) {
                        b[s] = []
                    }
                    b[s].push(n)
                }
                for (var x = 0,
                         P = this._armature.getSlots(); x < P.length; x++) {
                    var O = P[x];
                    var S = O.parent.name;
                    if (!this.containsBoneMask(S)) {
                        continue
                    }
                    var s = O.name;
                    var f = this._animationData.getSlotTimelines(s);
                    if (s in b) {
                        delete b[s]
                    } else {
                        var E = false;
                        var B = false;
                        D.length = 0;
                        if (f !== null) {
                            for (var M = 0,
                                     w = f; M < w.length; M++) {
                                var m = w[M];
                                switch (m.type) {
                                    case 20:
                                    {
                                        var n = t.BaseObject.borrowObject(t.SlotDislayTimelineState);
                                        n.slot = O;
                                        n.init(this._armature, this, m);
                                        this._slotTimelines.push(n);
                                        E = true;
                                        break
                                    }
                                    case 21:
                                    {
                                        var n = t.BaseObject.borrowObject(t.SlotColorTimelineState);
                                        n.slot = O;
                                        n.init(this._armature, this, m);
                                        this._slotTimelines.push(n);
                                        B = true;
                                        break
                                    }
                                    case 22:
                                    {
                                        var n = t.BaseObject.borrowObject(t.SlotFFDTimelineState);
                                        n.slot = O;
                                        n.init(this._armature, this, m);
                                        this._slotTimelines.push(n);
                                        D.push(n.meshOffset);
                                        break
                                    }
                                    default:
                                        break
                                }
                            }
                        }
                        if (this.resetToPose) {
                            if (!E) {
                                var n = t.BaseObject.borrowObject(t.SlotDislayTimelineState);
                                n.slot = O;
                                n.init(this._armature, this, null);
                                this._slotTimelines.push(n);
                                this._poseTimelines.push(n)
                            }
                            if (!B) {
                                var n = t.BaseObject.borrowObject(t.SlotColorTimelineState);
                                n.slot = O;
                                n.init(this._armature, this, null);
                                this._slotTimelines.push(n);
                                this._poseTimelines.push(n)
                            }
                            if (O.rawDisplayDatas !== null) {
                                for (var C = 0,
                                         I = O.rawDisplayDatas; C < I.length; C++) {
                                    var F = I[C];
                                    if (F !== null && F.type === 2) {
                                        var N = F.offset;
                                        if (D.indexOf(N) < 0) {
                                            var n = t.BaseObject.borrowObject(t.SlotFFDTimelineState);
                                            n.meshOffset = N;
                                            n.slot = O;
                                            n.init(this._armature, this, null);
                                            this._slotTimelines.push(n);
                                            this._poseTimelines.push(n)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                for (var y in b) {
                    for (var R = 0,
                             k = b[y]; R < k.length; R++) {
                        var n = k[R];
                        this._slotTimelines.splice(this._slotTimelines.indexOf(n), 1);
                        n.returnToPool()
                    }
                }
            }
        };
        r.prototype._advanceFadeTime = function(e) {
            var a = this._fadeState > 0;
            if (this._subFadeState < 0) {
                this._subFadeState = 0;
                var i = a ? t.EventObject.FADE_OUT: t.EventObject.FADE_IN;
                if (this._armature.eventDispatcher.hasDBEventListener(i)) {
                    var r = t.BaseObject.borrowObject(t.EventObject);
                    r.type = i;
                    r.armature = this._armature;
                    r.animationState = this;
                    this._armature._dragonBones.bufferEvent(r)
                }
            }
            if (e < 0) {
                e = -e
            }
            this._fadeTime += e;
            if (this._fadeTime >= this.fadeTotalTime) {
                this._subFadeState = 1;
                this._fadeProgress = a ? 0 : 1
            } else if (this._fadeTime > 0) {
                this._fadeProgress = a ? 1 - this._fadeTime / this.fadeTotalTime: this._fadeTime / this.fadeTotalTime
            } else {
                this._fadeProgress = a ? 1 : 0
            }
            if (this._subFadeState > 0) {
                if (!a) {
                    this._playheadState |= 1;
                    this._fadeState = 0
                }
                var i = a ? t.EventObject.FADE_OUT_COMPLETE: t.EventObject.FADE_IN_COMPLETE;
                if (this._armature.eventDispatcher.hasDBEventListener(i)) {
                    var r = t.BaseObject.borrowObject(t.EventObject);
                    r.type = i;
                    r.armature = this._armature;
                    r.animationState = this;
                    this._armature._dragonBones.bufferEvent(r)
                }
            }
        };
        r.prototype.init = function(e, a, i) {
            if (this._armature !== null) {
                return
            }
            this._armature = e;
            this._animationData = a;
            this.resetToPose = i.resetToPose;
            this.additiveBlending = i.additiveBlending;
            this.displayControl = i.displayControl;
            this.actionEnabled = i.actionEnabled;
            this.layer = i.layer;
            this.playTimes = i.playTimes;
            this.timeScale = i.timeScale;
            this.fadeTotalTime = i.fadeInTime;
            this.autoFadeOutTime = i.autoFadeOutTime;
            this.weight = i.weight;
            this.name = i.name.length > 0 ? i.name: i.animation;
            this.group = i.group;
            if (i.pauseFadeIn) {
                this._playheadState = 2
            } else {
                this._playheadState = 3
            }
            if (i.duration < 0) {
                this._position = 0;
                this._duration = this._animationData.duration;
                if (i.position !== 0) {
                    if (this.timeScale >= 0) {
                        this._time = i.position
                    } else {
                        this._time = i.position - this._duration
                    }
                } else {
                    this._time = 0
                }
            } else {
                this._position = i.position;
                this._duration = i.duration;
                this._time = 0
            }
            if (this.timeScale < 0 && this._time === 0) {
                this._time = -1e-6
            }
            if (this.fadeTotalTime <= 0) {
                this._fadeProgress = .999999
            }
            if (i.boneMask.length > 0) {
                this._boneMask.length = i.boneMask.length;
                for (var r = 0,
                         n = this._boneMask.length; r < n; ++r) {
                    this._boneMask[r] = i.boneMask[r]
                }
            }
            this._actionTimeline = t.BaseObject.borrowObject(t.ActionTimelineState);
            this._actionTimeline.init(this._armature, this, this._animationData.actionTimeline);
            this._actionTimeline.currentTime = this._time;
            if (this._actionTimeline.currentTime < 0) {
                this._actionTimeline.currentTime = this._duration - this._actionTimeline.currentTime
            }
            if (this._animationData.zOrderTimeline !== null) {
                this._zOrderTimeline = t.BaseObject.borrowObject(t.ZOrderTimelineState);
                this._zOrderTimeline.init(this._armature, this, this._animationData.zOrderTimeline)
            }
        };
        r.prototype.advanceTime = function(e, a) {
            this._blendState.dirty = false;
            if (this._fadeState !== 0 || this._subFadeState !== 0) {
                this._advanceFadeTime(e)
            }
            if (this._playheadState === 3) {
                if (this.timeScale !== 1) {
                    e *= this.timeScale
                }
                this._time += e
            }
            if (this._timelineDirty !== 0) {
                if (this._timelineDirty === 2) {
                    this._updateTimelines()
                }
                this._timelineDirty = 0;
                this._updateBoneAndSlotTimelines()
            }
            if (this.weight === 0) {
                return
            }
            var i = this._fadeState === 0 && a > 0;
            var r = true;
            var n = true;
            var s = this._time;
            this._weightResult = this.weight * this._fadeProgress;
            if (this._parent !== null) {
                this._weightResult *= this._parent._weightResult / this._parent._fadeProgress
            }
            if (this._actionTimeline.playState <= 0) {
                this._actionTimeline.update(s)
            }
            if (i) {
                var o = a * 2;
                this._actionTimeline.currentTime = Math.floor(this._actionTimeline.currentTime * o) / o
            }
            if (this._zOrderTimeline !== null && this._zOrderTimeline.playState <= 0) {
                this._zOrderTimeline.update(s)
            }
            if (i) {
                var l = Math.floor(this._actionTimeline.currentTime * a);
                if (this._armature._cacheFrameIndex === l) {
                    r = false;
                    n = false
                } else {
                    this._armature._cacheFrameIndex = l;
                    if (this._animationData.cachedFrames[l]) {
                        n = false
                    } else {
                        this._animationData.cachedFrames[l] = true
                    }
                }
            }
            if (r) {
                if (n) {
                    for (var h = 0,
                             f = this._boneTimelines.length; h < f; ++h) {
                        var u = this._boneTimelines[h];
                        if (u.playState <= 0) {
                            u.update(s)
                        }
                        if (h === f - 1 || u.bone !== this._boneTimelines[h + 1].bone) {
                            var _ = u.bone._blendState.update(this._weightResult, this.layer);
                            if (_ !== 0) {
                                u.blend(_)
                            }
                        }
                    }
                }
                for (var h = 0,
                         f = this._surfaceTimelines.length; h < f; ++h) {
                    var u = this._surfaceTimelines[h];
                    var _ = u.surface._blendState.update(this._weightResult, this.layer);
                    if (u.playState <= 0) {
                        u.update(s)
                    }
                    if (_ !== 0) {
                        u.blend(_)
                    }
                }
                if (this.displayControl) {
                    for (var h = 0,
                             f = this._slotTimelines.length; h < f; ++h) {
                        var u = this._slotTimelines[h];
                        var c = u.slot.displayController;
                        if (c === null || c === this.name || c === this.group) {
                            if (u.playState <= 0) {
                                u.update(s)
                            }
                        }
                    }
                }
                for (var h = 0,
                         f = this._constraintTimelines.length; h < f; ++h) {
                    var u = this._constraintTimelines[h];
                    if (u.playState <= 0) {
                        u.update(s)
                    }
                }
                for (var h = 0,
                         f = this._animationTimelines.length; h < f; ++h) {
                    var u = this._animationTimelines[h];
                    var _ = u.animationState._blendState.update(this._weightResult, this.layer);
                    if (u.playState <= 0) {
                        u.update(s)
                    }
                    if (_ !== 0) {
                        u.blend(_)
                    }
                }
            }
            if (this._fadeState === 0) {
                if (this._subFadeState > 0) {
                    this._subFadeState = 0;
                    if (this._poseTimelines.length > 0) {
                        for (var m = 0,
                                 p = this._poseTimelines; m < p.length; m++) {
                            var u = p[m];
                            if (u instanceof t.BoneTimelineState) {
                                this._boneTimelines.splice(this._boneTimelines.indexOf(u), 1)
                            } else if (u instanceof t.SurfaceTimelineState) {
                                this._surfaceTimelines.splice(this._surfaceTimelines.indexOf(u), 1)
                            } else if (u instanceof t.SlotTimelineState) {
                                this._slotTimelines.splice(this._slotTimelines.indexOf(u), 1)
                            } else if (u instanceof t.ConstraintTimelineState) {
                                this._constraintTimelines.splice(this._constraintTimelines.indexOf(u), 1)
                            }
                            u.returnToPool()
                        }
                        this._poseTimelines.length = 0
                    }
                }
                if (this._actionTimeline.playState > 0) {
                    if (this.autoFadeOutTime >= 0) {
                        this.fadeOut(this.autoFadeOutTime)
                    }
                }
            }
        };
        r.prototype.play = function() {
            this._playheadState = 3
        };
        r.prototype.stop = function() {
            this._playheadState &= 1
        };
        r.prototype.fadeOut = function(t, e) {
            if (e === void 0) {
                e = true
            }
            if (t < 0) {
                t = 0
            }
            if (e) {
                this._playheadState &= 2
            }
            if (this._fadeState > 0) {
                if (t > this.fadeTotalTime - this._fadeTime) {
                    return
                }
            } else {
                this._fadeState = 1;
                this._subFadeState = -1;
                if (t <= 0 || this._fadeProgress <= 0) {
                    this._fadeProgress = 1e-6
                }
                for (var a = 0,
                         i = this._boneTimelines; a < i.length; a++) {
                    var r = i[a];
                    r.fadeOut()
                }
                for (var n = 0,
                         s = this._surfaceTimelines; n < s.length; n++) {
                    var r = s[n];
                    r.fadeOut()
                }
                for (var o = 0,
                         l = this._slotTimelines; o < l.length; o++) {
                    var r = l[o];
                    r.fadeOut()
                }
                for (var h = 0,
                         f = this._constraintTimelines; h < f.length; h++) {
                    var r = f[h];
                    r.fadeOut()
                }
                for (var u = 0,
                         _ = this._animationTimelines; u < _.length; u++) {
                    var r = _[u];
                    r.animationState.fadeOut(t, e);
                    r.fadeOut()
                }
            }
            this.displayControl = false;
            this.fadeTotalTime = this._fadeProgress > 1e-6 ? t / this._fadeProgress: 0;
            this._fadeTime = this.fadeTotalTime * (1 - this._fadeProgress)
        };
        r.prototype.containsBoneMask = function(t) {
            return this._boneMask.length === 0 || this._boneMask.indexOf(t) >= 0
        };
        r.prototype.addBoneMask = function(t, e) {
            if (e === void 0) {
                e = true
            }
            var a = this._armature.getBone(t);
            if (a === null) {
                return
            }
            if (this._boneMask.indexOf(t) < 0) {
                this._boneMask.push(t)
            }
            if (e) {
                for (var i = 0,
                         r = this._armature.getBones(); i < r.length; i++) {
                    var n = r[i];
                    if (this._boneMask.indexOf(n.name) < 0 && a.contains(n)) {
                        this._boneMask.push(n.name)
                    }
                }
            }
            this._timelineDirty = 1
        };
        r.prototype.removeBoneMask = function(t, e) {
            if (e === void 0) {
                e = true
            }
            var a = this._boneMask.indexOf(t);
            if (a >= 0) {
                this._boneMask.splice(a, 1)
            }
            if (e) {
                var i = this._armature.getBone(t);
                if (i !== null) {
                    var r = this._armature.getBones();
                    if (this._boneMask.length > 0) {
                        for (var n = 0,
                                 s = r; n < s.length; n++) {
                            var o = s[n];
                            var l = this._boneMask.indexOf(o.name);
                            if (l >= 0 && i.contains(o)) {
                                this._boneMask.splice(l, 1)
                            }
                        }
                    } else {
                        for (var h = 0,
                                 f = r; h < f.length; h++) {
                            var o = f[h];
                            if (o === i) {
                                continue
                            }
                            if (!i.contains(o)) {
                                this._boneMask.push(o.name)
                            }
                        }
                    }
                }
            }
            this._timelineDirty = 1
        };
        r.prototype.removeAllBoneMask = function() {
            this._boneMask.length = 0;
            this._timelineDirty = 1
        };
        Object.defineProperty(r.prototype, "isFadeIn", {
            get: function() {
                return this._fadeState < 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "isFadeOut", {
            get: function() {
                return this._fadeState > 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "isFadeComplete", {
            get: function() {
                return this._fadeState === 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "isPlaying", {
            get: function() {
                return (this._playheadState & 2) !== 0 && this._actionTimeline.playState <= 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "isCompleted", {
            get: function() {
                return this._actionTimeline.playState > 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "currentPlayTimes", {
            get: function() {
                return this._actionTimeline.currentPlayTimes
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "totalTime", {
            get: function() {
                return this._duration
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "currentTime", {
            get: function() {
                return this._actionTimeline.currentTime
            },
            set: function(t) {
                var e = this._actionTimeline.currentPlayTimes - (this._actionTimeline.playState > 0 ? 1 : 0);
                if (t < 0 || this._duration < t) {
                    t = t % this._duration + e * this._duration;
                    if (t < 0) {
                        t += this._duration
                    }
                }
                if (this.playTimes > 0 && e === this.playTimes - 1 && t === this._duration) {
                    t = this._duration - 1e-6
                }
                if (this._time === t) {
                    return
                }
                this._time = t;
                this._actionTimeline.setCurrentTime(this._time);
                if (this._zOrderTimeline !== null) {
                    this._zOrderTimeline.playState = -1
                }
                for (var a = 0,
                         i = this._boneTimelines; a < i.length; a++) {
                    var r = i[a];
                    r.playState = -1
                }
                for (var n = 0,
                         s = this._slotTimelines; n < s.length; n++) {
                    var r = s[n];
                    r.playState = -1
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "animationData", {
            get: function() {
                return this._animationData
            },
            enumerable: true,
            configurable: true
        });
        return r
    } (t.BaseObject);
    t.AnimationState = e;
    var a = function(e) {
        __extends(a, e);
        function a() {
            var a = e !== null && e.apply(this, arguments) || this;
            a.current = new t.Transform;
            a.delta = new t.Transform;
            a.result = new t.Transform;
            return a
        }
        a.toString = function() {
            return "[class dragonBones.BonePose]"
        };
        a.prototype._onClear = function() {
            this.current.identity();
            this.delta.identity();
            this.result.identity()
        };
        return a
    } (t.BaseObject);
    t.BonePose = a;
    var i = function() {
        function t() {}
        t.prototype.update = function(t, e) {
            if (this.dirty) {
                if (this.leftWeight > 0) {
                    if (this.layer !== e) {
                        if (this.layerWeight >= this.leftWeight) {
                            this.leftWeight = 0;
                            return 0
                        } else {
                            this.layer = e;
                            this.leftWeight -= this.layerWeight;
                            this.layerWeight = 0
                        }
                    }
                } else {
                    return 0
                }
                t *= this.leftWeight;
                this.layerWeight += t;
                this.blendWeight = t;
                return 2
            }
            this.dirty = true;
            this.layer = e;
            this.layerWeight = t;
            this.leftWeight = 1;
            this.blendWeight = t;
            return 1
        };
        t.prototype.clear = function() {
            this.dirty = false;
            this.layer = 0;
            this.leftWeight = 0;
            this.layerWeight = 0;
            this.blendWeight = 0
        };
        return t
    } ();
    t.BlendState = i
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.prototype._onClear = function() {
            this.playState = -1;
            this.currentPlayTimes = -1;
            this.currentTime = -1;
            this._tweenState = 0;
            this._frameRate = 0;
            this._frameValueOffset = 0;
            this._frameCount = 0;
            this._frameOffset = 0;
            this._frameIndex = -1;
            this._frameRateR = 0;
            this._position = 0;
            this._duration = 0;
            this._timeScale = 1;
            this._timeOffset = 0;
            this._dragonBonesData = null;
            this._animationData = null;
            this._timelineData = null;
            this._armature = null;
            this._animationState = null;
            this._actionTimeline = null;
            this._frameArray = null;
            this._frameIntArray = null;
            this._frameFloatArray = null;
            this._timelineArray = null;
            this._frameIndices = null
        };
        e.prototype._setCurrentTime = function(t) {
            var e = this.playState;
            var a = this.currentPlayTimes;
            var i = this.currentTime;
            if (this._actionTimeline !== null && this._frameCount <= 1) {
                this.playState = this._actionTimeline.playState >= 0 ? 1 : -1;
                this.currentPlayTimes = 1;
                this.currentTime = this._actionTimeline.currentTime
            } else if (this._actionTimeline === null || this._timeScale !== 1 || this._timeOffset !== 0) {
                var r = this._animationState.playTimes;
                var n = r * this._duration;
                t *= this._timeScale;
                if (this._timeOffset !== 0) {
                    t += this._timeOffset * this._animationData.duration
                }
                if (r > 0 && (t >= n || t <= -n)) {
                    if (this.playState <= 0 && this._animationState._playheadState === 3) {
                        this.playState = 1
                    }
                    this.currentPlayTimes = r;
                    if (t < 0) {
                        this.currentTime = 0
                    } else {
                        this.currentTime = this._duration + 1e-6
                    }
                } else {
                    if (this.playState !== 0 && this._animationState._playheadState === 3) {
                        this.playState = 0
                    }
                    if (t < 0) {
                        t = -t;
                        this.currentPlayTimes = Math.floor(t / this._duration);
                        this.currentTime = this._duration - t % this._duration
                    } else {
                        this.currentPlayTimes = Math.floor(t / this._duration);
                        this.currentTime = t % this._duration
                    }
                }
                this.currentTime += this._position
            } else {
                this.playState = this._actionTimeline.playState;
                this.currentPlayTimes = this._actionTimeline.currentPlayTimes;
                this.currentTime = this._actionTimeline.currentTime
            }
            if (this.currentPlayTimes === a && this.currentTime === i) {
                return false
            }
            if (e < 0 && this.playState !== e || this.playState <= 0 && this.currentPlayTimes !== a) {
                this._frameIndex = -1
            }
            return true
        };
        e.prototype.init = function(t, e, a) {
            this._armature = t;
            this._animationState = e;
            this._timelineData = a;
            this._actionTimeline = this._animationState._actionTimeline;
            if (this === this._actionTimeline) {
                this._actionTimeline = null
            }
            this._animationData = this._animationState._animationData;
            this._frameRate = this._animationData.parent.frameRate;
            this._frameRateR = 1 / this._frameRate;
            this._position = this._animationState._position;
            this._duration = this._animationState._duration;
            this._dragonBonesData = this._animationData.parent.parent;
            if (this._timelineData !== null) {
                this._frameIntArray = this._dragonBonesData.frameIntArray;
                this._frameFloatArray = this._dragonBonesData.frameFloatArray;
                this._frameArray = this._dragonBonesData.frameArray;
                this._timelineArray = this._dragonBonesData.timelineArray;
                this._frameIndices = this._dragonBonesData.frameIndices;
                this._frameCount = this._timelineArray[this._timelineData.offset + 2];
                this._frameValueOffset = this._timelineArray[this._timelineData.offset + 4];
                this._timeScale = 100 / this._timelineArray[this._timelineData.offset + 0];
                this._timeOffset = this._timelineArray[this._timelineData.offset + 1] * .01
            }
        };
        e.prototype.fadeOut = function() {};
        e.prototype.update = function(t) {
            if (this._setCurrentTime(t)) {
                if (this._frameCount > 1) {
                    var e = Math.floor(this.currentTime * this._frameRate);
                    var a = this._frameIndices[this._timelineData.frameIndicesOffset + e];
                    if (this._frameIndex !== a) {
                        this._frameIndex = a;
                        this._frameOffset = this._animationData.frameOffset + this._timelineArray[this._timelineData.offset + 5 + this._frameIndex];
                        this._onArriveAtFrame()
                    }
                } else if (this._frameIndex < 0) {
                    this._frameIndex = 0;
                    if (this._timelineData !== null) {
                        this._frameOffset = this._animationData.frameOffset + this._timelineArray[this._timelineData.offset + 5]
                    }
                    this._onArriveAtFrame()
                }
                if (this._tweenState !== 0) {
                    this._onUpdateFrame()
                }
            }
        };
        return e
    } (t.BaseObject);
    t.TimelineState = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e._getEasingValue = function(t, e, a) {
            var i = e;
            switch (t) {
                case 3:
                    i = Math.pow(e, 2);
                    break;
                case 4:
                    i = 1 - Math.pow(1 - e, 2);
                    break;
                case 5:
                    i = .5 * (1 - Math.cos(e * Math.PI));
                    break
            }
            return (i - e) * a + e
        };
        e._getEasingCurveValue = function(t, e, a, i) {
            if (t <= 0) {
                return 0
            } else if (t >= 1) {
                return 1
            }
            var r = a + 1;
            var n = Math.floor(t * r);
            var s = n === 0 ? 0 : e[i + n - 1];
            var o = n === r - 1 ? 1e4: e[i + n];
            return (s + (o - s) * (t * r - n)) * 1e-4
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this._tweenType = 0;
            this._curveCount = 0;
            this._framePosition = 0;
            this._frameDurationR = 0;
            this._tweenProgress = 0;
            this._tweenEasing = 0
        };
        e.prototype._onArriveAtFrame = function() {
            if (this._frameCount > 1 && (this._frameIndex !== this._frameCount - 1 || this._animationState.playTimes === 0 || this._animationState.currentPlayTimes < this._animationState.playTimes - 1)) {
                this._tweenType = this._frameArray[this._frameOffset + 1];
                this._tweenState = this._tweenType === 0 ? 1 : 2;
                if (this._tweenType === 2) {
                    this._curveCount = this._frameArray[this._frameOffset + 2]
                } else if (this._tweenType !== 0 && this._tweenType !== 1) {
                    this._tweenEasing = this._frameArray[this._frameOffset + 2] * .01
                }
                this._framePosition = this._frameArray[this._frameOffset] * this._frameRateR;
                if (this._frameIndex === this._frameCount - 1) {
                    this._frameDurationR = 1 / (this._animationData.duration - this._framePosition)
                } else {
                    var t = this._animationData.frameOffset + this._timelineArray[this._timelineData.offset + 5 + this._frameIndex + 1];
                    var e = this._frameArray[t] * this._frameRateR - this._framePosition;
                    if (e > 0) {
                        this._frameDurationR = 1 / e
                    } else {
                        this._frameDurationR = 0
                    }
                }
            } else {
                this._tweenState = 1
            }
        };
        e.prototype._onUpdateFrame = function() {
            if (this._tweenState === 2) {
                this._tweenProgress = (this.currentTime - this._framePosition) * this._frameDurationR;
                if (this._tweenType === 2) {
                    this._tweenProgress = e._getEasingCurveValue(this._tweenProgress, this._frameArray, this._curveCount, this._frameOffset + 3)
                } else if (this._tweenType !== 1) {
                    this._tweenProgress = e._getEasingValue(this._tweenType, this._tweenProgress, this._tweenEasing)
                }
            } else {
                this._tweenProgress = 0
            }
        };
        return e
    } (e);
    t.TweenTimelineState = a;
    var i = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.bone = null;
            this.bonePose = null
        };
        e.prototype.blend = function(t) {
            var e = this.bone._blendState.blendWeight;
            var a = this.bone.animationPose;
            var i = this.bonePose.result;
            if (t === 2) {
                a.x += i.x * e;
                a.y += i.y * e;
                a.rotation += i.rotation * e;
                a.skew += i.skew * e;
                a.scaleX += (i.scaleX - 1) * e;
                a.scaleY += (i.scaleY - 1) * e
            } else if (e !== 1) {
                a.x = i.x * e;
                a.y = i.y * e;
                a.rotation = i.rotation * e;
                a.skew = i.skew * e;
                a.scaleX = (i.scaleX - 1) * e + 1;
                a.scaleY = (i.scaleY - 1) * e + 1
            } else {
                a.x = i.x;
                a.y = i.y;
                a.rotation = i.rotation;
                a.skew = i.skew;
                a.scaleX = i.scaleX;
                a.scaleY = i.scaleY
            }
            if (this._animationState._fadeState !== 0 || this._animationState._subFadeState !== 0) {
                this.bone._transformDirty = true
            }
        };
        return e
    } (a);
    t.BoneTimelineState = i;
    var r = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.slot = null
        };
        return e
    } (a);
    t.SlotTimelineState = r;
    var n = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.constraint = null
        };
        return e
    } (a);
    t.ConstraintTimelineState = n
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        a.toString = function() {
            return "[class dragonBones.ActionTimelineState]"
        };
        a.prototype._onCrossFrame = function(e) {
            var a = this._armature.eventDispatcher;
            if (this._animationState.actionEnabled) {
                var i = this._animationData.frameOffset + this._timelineArray[this._timelineData.offset + 5 + e];
                var r = this._frameArray[i + 1];
                var n = this._animationData.parent.actions;
                for (var s = 0; s < r; ++s) {
                    var o = this._frameArray[i + 2 + s];
                    var l = n[o];
                    if (l.type === 0) {
                        var h = t.BaseObject.borrowObject(t.EventObject);
                        h.time = this._frameArray[i] / this._frameRate;
                        h.animationState = this._animationState;
                        t.EventObject.actionDataToInstance(l, h, this._armature);
                        this._armature._bufferAction(h, true)
                    } else {
                        var f = l.type === 10 ? t.EventObject.FRAME_EVENT: t.EventObject.SOUND_EVENT;
                        if (l.type === 11 || a.hasDBEventListener(f)) {
                            var h = t.BaseObject.borrowObject(t.EventObject);
                            h.time = this._frameArray[i] / this._frameRate;
                            h.animationState = this._animationState;
                            t.EventObject.actionDataToInstance(l, h, this._armature);
                            this._armature._dragonBones.bufferEvent(h)
                        }
                    }
                }
            }
        };
        a.prototype._onArriveAtFrame = function() {};
        a.prototype._onUpdateFrame = function() {};
        a.prototype.update = function(e) {
            var a = this.playState;
            var i = this.currentPlayTimes;
            var r = this.currentTime;
            if (this._setCurrentTime(e)) {
                var n = this._armature.eventDispatcher;
                if (a < 0) {
                    if (this.playState !== a) {
                        if (this._animationState.displayControl && this._animationState.resetToPose) {
                            this._armature._sortZOrder(null, 0)
                        }
                        i = this.currentPlayTimes;
                        if (n.hasDBEventListener(t.EventObject.START)) {
                            var s = t.BaseObject.borrowObject(t.EventObject);
                            s.type = t.EventObject.START;
                            s.armature = this._armature;
                            s.animationState = this._animationState;
                            this._armature._dragonBones.bufferEvent(s)
                        }
                    } else {
                        return
                    }
                }
                var o = this._animationState.timeScale < 0;
                var l = null;
                var h = null;
                if (this.currentPlayTimes !== i) {
                    if (n.hasDBEventListener(t.EventObject.LOOP_COMPLETE)) {
                        l = t.BaseObject.borrowObject(t.EventObject);
                        l.type = t.EventObject.LOOP_COMPLETE;
                        l.armature = this._armature;
                        l.animationState = this._animationState
                    }
                    if (this.playState > 0) {
                        if (n.hasDBEventListener(t.EventObject.COMPLETE)) {
                            h = t.BaseObject.borrowObject(t.EventObject);
                            h.type = t.EventObject.COMPLETE;
                            h.armature = this._armature;
                            h.animationState = this._animationState
                        }
                    }
                }
                if (this._frameCount > 1) {
                    var f = this._timelineData;
                    var u = Math.floor(this.currentTime * this._frameRate);
                    var _ = this._frameIndices[f.frameIndicesOffset + u];
                    if (this._frameIndex !== _) {
                        var c = this._frameIndex;
                        this._frameIndex = _;
                        if (this._timelineArray !== null) {
                            this._frameOffset = this._animationData.frameOffset + this._timelineArray[f.offset + 5 + this._frameIndex];
                            if (o) {
                                if (c < 0) {
                                    var m = Math.floor(r * this._frameRate);
                                    c = this._frameIndices[f.frameIndicesOffset + m];
                                    if (this.currentPlayTimes === i) {
                                        if (c === _) {
                                            c = -1
                                        }
                                    }
                                }
                                while (c >= 0) {
                                    var p = this._animationData.frameOffset + this._timelineArray[f.offset + 5 + c];
                                    var d = this._frameArray[p] / this._frameRate;
                                    if (this._position <= d && d <= this._position + this._duration) {
                                        this._onCrossFrame(c)
                                    }
                                    if (l !== null && c === 0) {
                                        this._armature._dragonBones.bufferEvent(l);
                                        l = null
                                    }
                                    if (c > 0) {
                                        c--
                                    } else {
                                        c = this._frameCount - 1
                                    }
                                    if (c === _) {
                                        break
                                    }
                                }
                            } else {
                                if (c < 0) {
                                    var m = Math.floor(r * this._frameRate);
                                    c = this._frameIndices[f.frameIndicesOffset + m];
                                    var p = this._animationData.frameOffset + this._timelineArray[f.offset + 5 + c];
                                    var d = this._frameArray[p] / this._frameRate;
                                    if (this.currentPlayTimes === i) {
                                        if (r <= d) {
                                            if (c > 0) {
                                                c--
                                            } else {
                                                c = this._frameCount - 1
                                            }
                                        } else if (c === _) {
                                            c = -1
                                        }
                                    }
                                }
                                while (c >= 0) {
                                    if (c < this._frameCount - 1) {
                                        c++
                                    } else {
                                        c = 0
                                    }
                                    var p = this._animationData.frameOffset + this._timelineArray[f.offset + 5 + c];
                                    var d = this._frameArray[p] / this._frameRate;
                                    if (this._position <= d && d <= this._position + this._duration) {
                                        this._onCrossFrame(c)
                                    }
                                    if (l !== null && c === 0) {
                                        this._armature._dragonBones.bufferEvent(l);
                                        l = null
                                    }
                                    if (c === _) {
                                        break
                                    }
                                }
                            }
                        }
                    }
                } else if (this._frameIndex < 0) {
                    this._frameIndex = 0;
                    if (this._timelineData !== null) {
                        this._frameOffset = this._animationData.frameOffset + this._timelineArray[this._timelineData.offset + 5];
                        var d = this._frameArray[this._frameOffset] / this._frameRate;
                        if (this.currentPlayTimes === i) {
                            if (r <= d) {
                                this._onCrossFrame(this._frameIndex)
                            }
                        } else if (this._position <= d) {
                            if (!o && l !== null) {
                                this._armature._dragonBones.bufferEvent(l);
                                l = null
                            }
                            this._onCrossFrame(this._frameIndex)
                        }
                    }
                }
                if (l !== null) {
                    this._armature._dragonBones.bufferEvent(l)
                }
                if (h !== null) {
                    this._armature._dragonBones.bufferEvent(h)
                }
            }
        };
        a.prototype.setCurrentTime = function(t) {
            this._setCurrentTime(t);
            this._frameIndex = -1
        };
        return a
    } (t.TimelineState);
    t.ActionTimelineState = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.ZOrderTimelineState]"
        };
        e.prototype._onArriveAtFrame = function() {
            if (this.playState >= 0) {
                var t = this._frameArray[this._frameOffset + 1];
                if (t > 0) {
                    this._armature._sortZOrder(this._frameArray, this._frameOffset + 2)
                } else {
                    this._armature._sortZOrder(null, 0)
                }
            }
        };
        e.prototype._onUpdateFrame = function() {};
        return e
    } (t.TimelineState);
    t.ZOrderTimelineState = a;
    var i = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        a.toString = function() {
            return "[class dragonBones.BoneAllTimelineState]"
        };
        a.prototype._onArriveAtFrame = function() {
            e.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var t = this._animationData.frameFloatOffset + this._frameValueOffset + this._frameIndex * 6;
                var a = this._armature._armatureData.scale;
                var i = this._frameFloatArray;
                var r = this.bonePose.current;
                var n = this.bonePose.delta;
                r.x = i[t++] * a;
                r.y = i[t++] * a;
                r.rotation = i[t++];
                r.skew = i[t++];
                r.scaleX = i[t++];
                r.scaleY = i[t++];
                if (this._tweenState === 2) {
                    if (this._frameIndex === this._frameCount - 1) {
                        t = this._animationData.frameFloatOffset + this._frameValueOffset
                    }
                    n.x = i[t++] * a - r.x;
                    n.y = i[t++] * a - r.y;
                    n.rotation = i[t++] - r.rotation;
                    n.skew = i[t++] - r.skew;
                    n.scaleX = i[t++] - r.scaleX;
                    n.scaleY = i[t++] - r.scaleY
                } else {
                    n.x = 0;
                    n.y = 0;
                    n.rotation = 0;
                    n.skew = 0;
                    n.scaleX = 0;
                    n.scaleY = 0
                }
            } else {
                var r = this.bonePose.current;
                var n = this.bonePose.delta;
                r.x = 0;
                r.y = 0;
                r.rotation = 0;
                r.skew = 0;
                r.scaleX = 1;
                r.scaleY = 1;
                n.x = 0;
                n.y = 0;
                n.rotation = 0;
                n.skew = 0;
                n.scaleX = 0;
                n.scaleY = 0
            }
        };
        a.prototype._onUpdateFrame = function() {
            e.prototype._onUpdateFrame.call(this);
            var t = this.bonePose.current;
            var a = this.bonePose.delta;
            var i = this.bonePose.result;
            this.bone._transformDirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            i.x = t.x + a.x * this._tweenProgress;
            i.y = t.y + a.y * this._tweenProgress;
            i.rotation = t.rotation + a.rotation * this._tweenProgress;
            i.skew = t.skew + a.skew * this._tweenProgress;
            i.scaleX = t.scaleX + a.scaleX * this._tweenProgress;
            i.scaleY = t.scaleY + a.scaleY * this._tweenProgress
        };
        a.prototype.fadeOut = function() {
            var e = this.bonePose.result;
            e.rotation = t.Transform.normalizeRadian(e.rotation);
            e.skew = t.Transform.normalizeRadian(e.skew)
        };
        return a
    } (t.BoneTimelineState);
    t.BoneAllTimelineState = i;
    var r = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.BoneTranslateTimelineState]"
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var e = this._animationData.frameFloatOffset + this._frameValueOffset + this._frameIndex * 2;
                var a = this._armature._armatureData.scale;
                var i = this._frameFloatArray;
                var r = this.bonePose.current;
                var n = this.bonePose.delta;
                r.x = i[e++] * a;
                r.y = i[e++] * a;
                if (this._tweenState === 2) {
                    if (this._frameIndex === this._frameCount - 1) {
                        e = this._animationData.frameFloatOffset + this._frameValueOffset
                    }
                    n.x = i[e++] * a - r.x;
                    n.y = i[e++] * a - r.y
                } else {
                    n.x = 0;
                    n.y = 0
                }
            } else {
                var r = this.bonePose.current;
                var n = this.bonePose.delta;
                r.x = 0;
                r.y = 0;
                n.x = 0;
                n.y = 0
            }
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            var e = this.bonePose.current;
            var a = this.bonePose.delta;
            var i = this.bonePose.result;
            this.bone._transformDirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            i.x = e.x + a.x * this._tweenProgress;
            i.y = e.y + a.y * this._tweenProgress
        };
        return e
    } (t.BoneTimelineState);
    t.BoneTranslateTimelineState = r;
    var n = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        a.toString = function() {
            return "[class dragonBones.BoneRotateTimelineState]"
        };
        a.prototype._onArriveAtFrame = function() {
            e.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var a = this._animationData.frameFloatOffset + this._frameValueOffset + this._frameIndex * 2;
                var i = this._frameFloatArray;
                var r = this.bonePose.current;
                var n = this.bonePose.delta;
                r.rotation = i[a++];
                r.skew = i[a++];
                if (this._tweenState === 2) {
                    if (this._frameIndex === this._frameCount - 1) {
                        a = this._animationData.frameFloatOffset + this._frameValueOffset;
                        n.rotation = t.Transform.normalizeRadian(i[a++] - r.rotation)
                    } else {
                        n.rotation = i[a++] - r.rotation
                    }
                    n.skew = i[a++] - r.skew
                } else {
                    n.rotation = 0;
                    n.skew = 0
                }
            } else {
                var r = this.bonePose.current;
                var n = this.bonePose.delta;
                r.rotation = 0;
                r.skew = 0;
                n.rotation = 0;
                n.skew = 0
            }
        };
        a.prototype._onUpdateFrame = function() {
            e.prototype._onUpdateFrame.call(this);
            var t = this.bonePose.current;
            var a = this.bonePose.delta;
            var i = this.bonePose.result;
            this.bone._transformDirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            i.rotation = t.rotation + a.rotation * this._tweenProgress;
            i.skew = t.skew + a.skew * this._tweenProgress
        };
        a.prototype.fadeOut = function() {
            var e = this.bonePose.result;
            e.rotation = t.Transform.normalizeRadian(e.rotation);
            e.skew = t.Transform.normalizeRadian(e.skew)
        };
        return a
    } (t.BoneTimelineState);
    t.BoneRotateTimelineState = n;
    var s = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.BoneScaleTimelineState]"
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var e = this._animationData.frameFloatOffset + this._frameValueOffset + this._frameIndex * 2;
                var a = this._frameFloatArray;
                var i = this.bonePose.current;
                var r = this.bonePose.delta;
                i.scaleX = a[e++];
                i.scaleY = a[e++];
                if (this._tweenState === 2) {
                    if (this._frameIndex === this._frameCount - 1) {
                        e = this._animationData.frameFloatOffset + this._frameValueOffset
                    }
                    r.scaleX = a[e++] - i.scaleX;
                    r.scaleY = a[e++] - i.scaleY
                } else {
                    r.scaleX = 0;
                    r.scaleY = 0
                }
            } else {
                var i = this.bonePose.current;
                var r = this.bonePose.delta;
                i.scaleX = 1;
                i.scaleY = 1;
                r.scaleX = 0;
                r.scaleY = 0
            }
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            var e = this.bonePose.current;
            var a = this.bonePose.delta;
            var i = this.bonePose.result;
            this.bone._transformDirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            i.scaleX = e.scaleX + a.scaleX * this._tweenProgress;
            i.scaleY = e.scaleY + a.scaleY * this._tweenProgress
        };
        return e
    } (t.BoneTimelineState);
    t.BoneScaleTimelineState = s;
    var o = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e._current = [];
            e._delta = [];
            e._result = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.SurfaceTimelineState]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.surface = null;
            this._frameFloatOffset = 0;
            this._valueCount = 0;
            this._deformCount = 0;
            this._valueOffset = 0;
            this._current.length = 0;
            this._delta.length = 0;
            this._result.length = 0
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var e = this._animationData.frameFloatOffset + this._frameValueOffset + this._frameIndex * this._valueCount;
                var a = this._armature._armatureData.scale;
                var i = this._frameFloatArray;
                if (this._tweenState === 2) {
                    var r = e + this._valueCount;
                    if (this._frameIndex === this._frameCount - 1) {
                        r = this._animationData.frameFloatOffset + this._frameValueOffset
                    }
                    for (var n = 0; n < this._valueCount; ++n) {
                        this._delta[n] = i[r + n] * a - (this._current[n] = i[e + n] * a)
                    }
                } else {
                    for (var n = 0; n < this._valueCount; ++n) {
                        this._current[n] = i[e + n] * a
                    }
                }
            } else {
                for (var n = 0; n < this._valueCount; ++n) {
                    this._current[n] = 0
                }
            }
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            this.surface._transformDirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            for (var e = 0; e < this._valueCount; ++e) {
                this._result[e] = this._current[e] + this._delta[e] * this._tweenProgress
            }
        };
        e.prototype.init = function(e, a, i) {
            t.prototype.init.call(this, e, a, i);
            if (this._timelineData !== null) {
                var r = this._animationData.frameIntOffset + this._timelineArray[this._timelineData.offset + 3];
                this._deformCount = this._frameIntArray[r + 1];
                this._valueCount = this._frameIntArray[r + 2];
                this._valueOffset = this._frameIntArray[r + 3];
                this._frameFloatOffset = this._frameIntArray[r + 4] + this._animationData.frameFloatOffset
            } else {
                this._deformCount = this.surface._deformVertices.length;
                this._valueCount = this._deformCount;
                this._valueOffset = 0;
                this._frameFloatOffset = 0
            }
            this._current.length = this._valueCount;
            this._delta.length = this._valueCount;
            this._result.length = this._valueCount;
            for (var n = 0; n < this._valueCount; ++n) {
                this._delta[n] = 0
            }
        };
        e.prototype.blend = function(t) {
            var e = this.surface._blendState.blendWeight;
            var a = this.surface._deformVertices;
            for (var i = 0; i < this._deformCount; ++i) {
                var r = 0;
                if (i < this._valueOffset) {
                    r = this._frameFloatArray[this._frameFloatOffset + i]
                } else if (i < this._valueOffset + this._valueCount) {
                    r = this._result[i - this._valueOffset]
                } else {
                    r = this._frameFloatArray[this._frameFloatOffset + i - this._valueCount]
                }
                if (t === 2) {
                    a[i] += r * e
                } else if (e !== 1) {
                    a[i] = r * e
                } else {
                    a[i] = r
                }
            }
            if (this._animationState._fadeState !== 0 || this._animationState._subFadeState !== 0) {
                this.surface._transformDirty = true
            }
        };
        return e
    } (t.TweenTimelineState);
    t.SurfaceTimelineState = o;
    var l = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.SlotDislayTimelineState]"
        };
        e.prototype._onArriveAtFrame = function() {
            if (this.playState >= 0) {
                var t = this._timelineData !== null ? this._frameArray[this._frameOffset + 1] : this.slot._slotData.displayIndex;
                if (this.slot.displayIndex !== t) {
                    this.slot._setDisplayIndex(t, true)
                }
            }
        };
        return e
    } (t.SlotTimelineState);
    t.SlotDislayTimelineState = l;
    var h = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e._current = [0, 0, 0, 0, 0, 0, 0, 0];
            e._delta = [0, 0, 0, 0, 0, 0, 0, 0];
            e._result = [0, 0, 0, 0, 0, 0, 0, 0];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.SlotColorTimelineState]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this._dirty = false
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var e = this._dragonBonesData.intArray;
                var a = this._frameIntArray;
                var i = this._animationData.frameIntOffset + this._frameValueOffset + this._frameIndex * 1;
                var r = a[i];
                if (r < 0) {
                    r += 65536
                }
                this._current[0] = e[r++];
                this._current[1] = e[r++];
                this._current[2] = e[r++];
                this._current[3] = e[r++];
                this._current[4] = e[r++];
                this._current[5] = e[r++];
                this._current[6] = e[r++];
                this._current[7] = e[r++];
                if (this._tweenState === 2) {
                    if (this._frameIndex === this._frameCount - 1) {
                        r = a[this._animationData.frameIntOffset + this._frameValueOffset]
                    } else {
                        r = a[i + 1 * 1]
                    }
                    if (r < 0) {
                        r += 65536
                    }
                    this._delta[0] = e[r++] - this._current[0];
                    this._delta[1] = e[r++] - this._current[1];
                    this._delta[2] = e[r++] - this._current[2];
                    this._delta[3] = e[r++] - this._current[3];
                    this._delta[4] = e[r++] - this._current[4];
                    this._delta[5] = e[r++] - this._current[5];
                    this._delta[6] = e[r++] - this._current[6];
                    this._delta[7] = e[r++] - this._current[7]
                }
            } else {
                var n = this.slot._slotData.color;
                this._current[0] = n.alphaMultiplier * 100;
                this._current[1] = n.redMultiplier * 100;
                this._current[2] = n.greenMultiplier * 100;
                this._current[3] = n.blueMultiplier * 100;
                this._current[4] = n.alphaOffset;
                this._current[5] = n.redOffset;
                this._current[6] = n.greenOffset;
                this._current[7] = n.blueOffset
            }
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            this._dirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            this._result[0] = (this._current[0] + this._delta[0] * this._tweenProgress) * .01;
            this._result[1] = (this._current[1] + this._delta[1] * this._tweenProgress) * .01;
            this._result[2] = (this._current[2] + this._delta[2] * this._tweenProgress) * .01;
            this._result[3] = (this._current[3] + this._delta[3] * this._tweenProgress) * .01;
            this._result[4] = this._current[4] + this._delta[4] * this._tweenProgress;
            this._result[5] = this._current[5] + this._delta[5] * this._tweenProgress;
            this._result[6] = this._current[6] + this._delta[6] * this._tweenProgress;
            this._result[7] = this._current[7] + this._delta[7] * this._tweenProgress
        };
        e.prototype.fadeOut = function() {
            this._tweenState = 0;
            this._dirty = false
        };
        e.prototype.update = function(e) {
            t.prototype.update.call(this, e);
            if (this._tweenState !== 0 || this._dirty) {
                var a = this.slot._colorTransform;
                if (this._animationState._fadeState !== 0 || this._animationState._subFadeState !== 0) {
                    if (a.alphaMultiplier !== this._result[0] || a.redMultiplier !== this._result[1] || a.greenMultiplier !== this._result[2] || a.blueMultiplier !== this._result[3] || a.alphaOffset !== this._result[4] || a.redOffset !== this._result[5] || a.greenOffset !== this._result[6] || a.blueOffset !== this._result[7]) {
                        var i = Math.pow(this._animationState._fadeProgress, 4);
                        a.alphaMultiplier += (this._result[0] - a.alphaMultiplier) * i;
                        a.redMultiplier += (this._result[1] - a.redMultiplier) * i;
                        a.greenMultiplier += (this._result[2] - a.greenMultiplier) * i;
                        a.blueMultiplier += (this._result[3] - a.blueMultiplier) * i;
                        a.alphaOffset += (this._result[4] - a.alphaOffset) * i;
                        a.redOffset += (this._result[5] - a.redOffset) * i;
                        a.greenOffset += (this._result[6] - a.greenOffset) * i;
                        a.blueOffset += (this._result[7] - a.blueOffset) * i;
                        this.slot._colorDirty = true
                    }
                } else if (this._dirty) {
                    this._dirty = false;
                    if (a.alphaMultiplier !== this._result[0] || a.redMultiplier !== this._result[1] || a.greenMultiplier !== this._result[2] || a.blueMultiplier !== this._result[3] || a.alphaOffset !== this._result[4] || a.redOffset !== this._result[5] || a.greenOffset !== this._result[6] || a.blueOffset !== this._result[7]) {
                        a.alphaMultiplier = this._result[0];
                        a.redMultiplier = this._result[1];
                        a.greenMultiplier = this._result[2];
                        a.blueMultiplier = this._result[3];
                        a.alphaOffset = this._result[4];
                        a.redOffset = this._result[5];
                        a.greenOffset = this._result[6];
                        a.blueOffset = this._result[7];
                        this.slot._colorDirty = true
                    }
                }
            }
        };
        return e
    } (t.SlotTimelineState);
    t.SlotColorTimelineState = h;
    var f = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e._current = [];
            e._delta = [];
            e._result = [];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.SlotFFDTimelineState]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.meshOffset = 0;
            this._dirty = false;
            this._frameFloatOffset = 0;
            this._valueCount = 0;
            this._deformCount = 0;
            this._valueOffset = 0;
            this._current.length = 0;
            this._delta.length = 0;
            this._result.length = 0
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var e = this._animationData.frameFloatOffset + this._frameValueOffset + this._frameIndex * this._valueCount;
                var a = this._armature._armatureData.scale;
                var i = this._frameFloatArray;
                if (this._tweenState === 2) {
                    var r = e + this._valueCount;
                    if (this._frameIndex === this._frameCount - 1) {
                        r = this._animationData.frameFloatOffset + this._frameValueOffset
                    }
                    for (var n = 0; n < this._valueCount; ++n) {
                        this._delta[n] = i[r + n] * a - (this._current[n] = i[e + n] * a)
                    }
                } else {
                    for (var n = 0; n < this._valueCount; ++n) {
                        this._current[n] = i[e + n] * a
                    }
                }
            } else {
                for (var n = 0; n < this._valueCount; ++n) {
                    this._current[n] = 0
                }
            }
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            this._dirty = true;
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            for (var e = 0; e < this._valueCount; ++e) {
                this._result[e] = this._current[e] + this._delta[e] * this._tweenProgress
            }
        };
        e.prototype.init = function(e, a, i) {
            t.prototype.init.call(this, e, a, i);
            if (this._timelineData !== null) {
                var r = this._animationData.frameIntOffset + this._timelineArray[this._timelineData.offset + 3];
                this.meshOffset = this._frameIntArray[r + 0];
                if (this.meshOffset < 0) {
                    this.meshOffset += 65536
                }
                this._deformCount = this._frameIntArray[r + 1];
                this._valueCount = this._frameIntArray[r + 2];
                this._valueOffset = this._frameIntArray[r + 3];
                this._frameFloatOffset = this._frameIntArray[r + 4] + this._animationData.frameFloatOffset
            } else {
                this._deformCount = this.slot._deformVertices.length;
                this._valueCount = this._deformCount;
                this._valueOffset = 0;
                this._frameFloatOffset = 0
            }
            this._current.length = this._valueCount;
            this._delta.length = this._valueCount;
            this._result.length = this._valueCount;
            for (var n = 0; n < this._valueCount; ++n) {
                this._delta[n] = 0
            }
        };
        e.prototype.fadeOut = function() {
            this._tweenState = 0;
            this._dirty = false
        };
        e.prototype.update = function(e) {
            if (this.slot._meshData === null || this.slot._meshData.offset !== this.meshOffset) {
                return
            }
            t.prototype.update.call(this, e);
            if (this._tweenState !== 0 || this._dirty) {
                var a = this.slot._deformVertices;
                if (this._animationState._fadeState !== 0 || this._animationState._subFadeState !== 0) {
                    var i = Math.pow(this._animationState._fadeProgress, 2);
                    for (var r = 0; r < this._deformCount; ++r) {
                        if (r < this._valueOffset) {
                            a[r] += (this._frameFloatArray[this._frameFloatOffset + r] - a[r]) * i
                        } else if (r < this._valueOffset + this._valueCount) {
                            a[r] += (this._result[r - this._valueOffset] - a[r]) * i
                        } else {
                            a[r] += (this._frameFloatArray[this._frameFloatOffset + r - this._valueCount] - a[r]) * i
                        }
                    }
                    this.slot._meshDirty = true
                } else if (this._dirty) {
                    this._dirty = false;
                    for (var r = 0; r < this._deformCount; ++r) {
                        if (r < this._valueOffset) {
                            a[r] = this._frameFloatArray[this._frameFloatOffset + r]
                        } else if (r < this._valueOffset + this._valueCount) {
                            a[r] = this._result[r - this._valueOffset]
                        } else {
                            a[r] = this._frameFloatArray[this._frameFloatOffset + r - this._valueCount]
                        }
                    }
                    this.slot._meshDirty = true
                }
            }
        };
        return e
    } (t.SlotTimelineState);
    t.SlotFFDTimelineState = f;
    var u = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.toString = function() {
            return "[class dragonBones.IKConstraintTimelineState]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this._current = 0;
            this._delta = 0
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            var e = this.constraint;
            if (this._timelineData !== null) {
                var a = this._animationData.frameIntOffset + this._frameValueOffset + this._frameIndex * 2;
                var i = this._frameIntArray;
                var r = i[a++] !== 0;
                this._current = i[a++] * .01;
                if (this._tweenState === 2) {
                    if (this._frameIndex === this._frameCount - 1) {
                        a = this._animationData.frameIntOffset + this._frameValueOffset
                    }
                    this._delta = i[a + 1] * .01 - this._current
                } else {
                    this._delta = 0
                }
                e._bendPositive = r
            } else {
                var n = e._constraintData;
                this._current = n.weight;
                this._delta = 0;
                e._bendPositive = n.bendPositive
            }
            e.invalidUpdate()
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            var e = this.constraint;
            e._weight = this._current + this._delta * this._tweenProgress;
            e.invalidUpdate()
        };
        return e
    } (t.ConstraintTimelineState);
    t.IKConstraintTimelineState = u;
    var _ = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e._floats = [0, 0, 0, 0, 0, 0];
            return e
        }
        e.toString = function() {
            return "[class dragonBones.AnimationTimelineState]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            this.animationState = null
        };
        e.prototype._onArriveAtFrame = function() {
            t.prototype._onArriveAtFrame.call(this);
            if (this._timelineData === null) {
                return
            }
            var e = this._animationData.frameIntOffset + this._frameValueOffset + this._frameIndex * 2;
            var a = 1 / this.animationState._animationData.parent.frameRate;
            var i = this._frameIntArray;
            this._floats[0] = i[e++] * a;
            this._floats[3] = i[e++] * .01;
            if (this._tweenState === 2) {
                if (this._frameIndex === this._frameCount - 1) {
                    e = this._animationData.frameIntOffset + this._frameValueOffset
                }
                this._floats[1] = i[e++] * a - this._floats[0];
                this._floats[4] = i[e++] * .01 - this._floats[3]
            } else {
                this._floats[1] = 0;
                this._floats[4] = 0
            }
        };
        e.prototype._onUpdateFrame = function() {
            t.prototype._onUpdateFrame.call(this);
            if (this._tweenState !== 2) {
                this._tweenState = 0
            }
            if (this._floats[0] >= 0) {
                this._floats[2] = this._floats[0] + this._floats[1] * this._tweenProgress
            }
            this._floats[5] = this._floats[3] + this._floats[4] * this._tweenProgress
        };
        e.prototype.blend = function(t) {
            var e = this.animationState;
            var a = e._blendState.blendWeight;
            if (t === 2) {
                e.weight += this._floats[5] * a;
                e.currentTime += this._floats[2] * a
            } else {
                e.weight = this._floats[5] * a;
                e.currentTime = this._floats[2] * a
            }
        };
        return e
    } (t.TweenTimelineState);
    t.AnimationTimelineState = _
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        e.actionDataToInstance = function(t, a, i) {
            if (t.type === 0) {
                a.type = e.FRAME_EVENT
            } else {
                a.type = t.type === 10 ? e.FRAME_EVENT: e.SOUND_EVENT
            }
            a.name = t.name;
            a.armature = i;
            a.actionData = t;
            a.data = t.data;
            if (t.bone !== null) {
                a.bone = i.getBone(t.bone.name)
            }
            if (t.slot !== null) {
                a.slot = i.getSlot(t.slot.name)
            }
        };
        e.toString = function() {
            return "[class dragonBones.EventObject]"
        };
        e.prototype._onClear = function() {
            this.time = 0;
            this.type = "";
            this.name = "";
            this.armature = null;
            this.bone = null;
            this.slot = null;
            this.animationState = null;
            this.actionData = null;
            this.data = null
        };
        e.START = "start";
        e.LOOP_COMPLETE = "loopComplete";
        e.COMPLETE = "complete";
        e.FADE_IN = "fadeIn";
        e.FADE_IN_COMPLETE = "fadeInComplete";
        e.FADE_OUT = "fadeOut";
        e.FADE_OUT_COMPLETE = "fadeOutComplete";
        e.FRAME_EVENT = "frameEvent";
        e.SOUND_EVENT = "soundEvent";
        return e
    } (t.BaseObject);
    t.EventObject = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function e() {}
        e._getArmatureType = function(t) {
            switch (t.toLowerCase()) {
                case "stage":
                    return 2;
                case "armature":
                    return 0;
                case "movieclip":
                    return 1;
                default:
                    return 0
            }
        };
        e._getBoneType = function(t) {
            switch (t.toLowerCase()) {
                case "bone":
                    return 0;
                case "surface":
                    return 1;
                default:
                    return 0
            }
        };
        e._getDisplayType = function(t) {
            switch (t.toLowerCase()) {
                case "image":
                    return 0;
                case "mesh":
                    return 2;
                case "armature":
                    return 1;
                case "boundingbox":
                    return 3;
                default:
                    return 0
            }
        };
        e._getBoundingBoxType = function(t) {
            switch (t.toLowerCase()) {
                case "rectangle":
                    return 0;
                case "ellipse":
                    return 1;
                case "polygon":
                    return 2;
                default:
                    return 0
            }
        };
        e._getActionType = function(t) {
            switch (t.toLowerCase()) {
                case "play":
                    return 0;
                case "frame":
                    return 10;
                case "sound":
                    return 11;
                default:
                    return 0
            }
        };
        e._getBlendMode = function(t) {
            switch (t.toLowerCase()) {
                case "normal":
                    return 0;
                case "add":
                    return 1;
                case "alpha":
                    return 2;
                case "darken":
                    return 3;
                case "difference":
                    return 4;
                case "erase":
                    return 5;
                case "hardlight":
                    return 6;
                case "invert":
                    return 7;
                case "layer":
                    return 8;
                case "lighten":
                    return 9;
                case "multiply":
                    return 10;
                case "overlay":
                    return 11;
                case "screen":
                    return 12;
                case "subtract":
                    return 13;
                default:
                    return 0
            }
        };
        e.parseDragonBonesData = function(e) {
            if (e instanceof ArrayBuffer) {
                return t.BinaryDataParser.getInstance().parseDragonBonesData(e)
            } else {
                return t.ObjectDataParser.getInstance().parseDragonBonesData(e)
            }
        };
        e.parseTextureAtlasData = function(a, i) {
            if (i === void 0) {
                i = 1
            }
            console.warn("已废弃");
            var r = {};
            var n = a[e.SUB_TEXTURE];
            for (var s = 0,
                     o = n.length; s < o; s++) {
                var l = n[s];
                var h = l[e.NAME];
                var f = new t.Rectangle;
                var u = null;
                f.x = l[e.X] / i;
                f.y = l[e.Y] / i;
                f.width = l[e.WIDTH] / i;
                f.height = l[e.HEIGHT] / i;
                if (e.FRAME_WIDTH in l) {
                    u = new t.Rectangle;
                    u.x = l[e.FRAME_X] / i;
                    u.y = l[e.FRAME_Y] / i;
                    u.width = l[e.FRAME_WIDTH] / i;
                    u.height = l[e.FRAME_HEIGHT] / i
                }
                r[h] = {
                    region: f,
                    frame: u,
                    rotated: false
                }
            }
            return r
        };
        e.DATA_VERSION_2_3 = "2.3";
        e.DATA_VERSION_3_0 = "3.0";
        e.DATA_VERSION_4_0 = "4.0";
        e.DATA_VERSION_4_5 = "4.5";
        e.DATA_VERSION_5_0 = "5.0";
        e.DATA_VERSION_5_5 = "5.5";
        e.DATA_VERSION = e.DATA_VERSION_5_5;
        e.DATA_VERSIONS = [e.DATA_VERSION_4_0, e.DATA_VERSION_4_5, e.DATA_VERSION_5_0, e.DATA_VERSION_5_5];
        e.TEXTURE_ATLAS = "textureAtlas";
        e.SUB_TEXTURE = "SubTexture";
        e.FORMAT = "format";
        e.IMAGE_PATH = "imagePath";
        e.WIDTH = "width";
        e.HEIGHT = "height";
        e.ROTATED = "rotated";
        e.FRAME_X = "frameX";
        e.FRAME_Y = "frameY";
        e.FRAME_WIDTH = "frameWidth";
        e.FRAME_HEIGHT = "frameHeight";
        e.DRADON_BONES = "dragonBones";
        e.USER_DATA = "userData";
        e.ARMATURE = "armature";
        e.BONE = "bone";
        e.SURFACE = "surface";
        e.SLOT = "slot";
        e.CONSTRAINT = "constraint";
        e.IK = "ik";
        e.SKIN = "skin";
        e.DISPLAY = "display";
        e.ANIMATION = "animation";
        e.Z_ORDER = "zOrder";
        e.FFD = "ffd";
        e.FRAME = "frame";
        e.TRANSLATE_FRAME = "translateFrame";
        e.ROTATE_FRAME = "rotateFrame";
        e.SCALE_FRAME = "scaleFrame";
        e.DISPLAY_FRAME = "displayFrame";
        e.COLOR_FRAME = "colorFrame";
        e.DEFAULT_ACTIONS = "defaultActions";
        e.ACTIONS = "actions";
        e.EVENTS = "events";
        e.INTS = "ints";
        e.FLOATS = "floats";
        e.STRINGS = "strings";
        e.CANVAS = "canvas";
        e.TRANSFORM = "transform";
        e.PIVOT = "pivot";
        e.AABB = "aabb";
        e.COLOR = "color";
        e.VERSION = "version";
        e.COMPATIBLE_VERSION = "compatibleVersion";
        e.FRAME_RATE = "frameRate";
        e.TYPE = "type";
        e.SUB_TYPE = "subType";
        e.NAME = "name";
        e.PARENT = "parent";
        e.TARGET = "target";
        e.STAGE = "stage";
        e.SHARE = "share";
        e.PATH = "path";
        e.LENGTH = "length";
        e.DISPLAY_INDEX = "displayIndex";
        e.BLEND_MODE = "blendMode";
        e.INHERIT_TRANSLATION = "inheritTranslation";
        e.INHERIT_ROTATION = "inheritRotation";
        e.INHERIT_SCALE = "inheritScale";
        e.INHERIT_REFLECTION = "inheritReflection";
        e.INHERIT_ANIMATION = "inheritAnimation";
        e.INHERIT_DEFORM = "inheritDeform";
        e.SEGMENT_X = "segmentX";
        e.SEGMENT_Y = "segmentY";
        e.BEND_POSITIVE = "bendPositive";
        e.CHAIN = "chain";
        e.WEIGHT = "weight";
        e.FADE_IN_TIME = "fadeInTime";
        e.PLAY_TIMES = "playTimes";
        e.SCALE = "scale";
        e.OFFSET = "offset";
        e.POSITION = "position";
        e.DURATION = "duration";
        e.TWEEN_EASING = "tweenEasing";
        e.TWEEN_ROTATE = "tweenRotate";
        e.TWEEN_SCALE = "tweenScale";
        e.CLOCK_WISE = "clockwise";
        e.CURVE = "curve";
        e.SOUND = "sound";
        e.EVENT = "event";
        e.ACTION = "action";
        e.X = "x";
        e.Y = "y";
        e.SKEW_X = "skX";
        e.SKEW_Y = "skY";
        e.SCALE_X = "scX";
        e.SCALE_Y = "scY";
        e.VALUE = "value";
        e.ROTATE = "rotate";
        e.SKEW = "skew";
        e.ALPHA_OFFSET = "aO";
        e.RED_OFFSET = "rO";
        e.GREEN_OFFSET = "gO";
        e.BLUE_OFFSET = "bO";
        e.ALPHA_MULTIPLIER = "aM";
        e.RED_MULTIPLIER = "rM";
        e.GREEN_MULTIPLIER = "gM";
        e.BLUE_MULTIPLIER = "bM";
        e.UVS = "uvs";
        e.VERTICES = "vertices";
        e.TRIANGLES = "triangles";
        e.WEIGHTS = "weights";
        e.SLOT_POSE = "slotPose";
        e.BONE_POSE = "bonePose";
        e.GLUE_WEIGHTS = "glueWeights";
        e.GLUE_MESHES = "glueMeshes";
        e.GOTO_AND_PLAY = "gotoAndPlay";
        e.DEFAULT_NAME = "default";
        return e
    } ();
    t.DataParser = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(i, e);
        function i() {
            var a = e !== null && e.apply(this, arguments) || this;
            a._rawTextureAtlasIndex = 0;
            a._rawBones = [];
            a._data = null;
            a._armature = null;
            a._bone = null;
            a._surface = null;
            a._slot = null;
            a._skin = null;
            a._mesh = null;
            a._animation = null;
            a._timeline = null;
            a._rawTextureAtlases = null;
            a._defaultColorOffset = -1;
            a._prevClockwise = 0;
            a._prevRotation = 0;
            a._helpMatrixA = new t.Matrix;
            a._helpMatrixB = new t.Matrix;
            a._helpTransform = new t.Transform;
            a._helpColorTransform = new t.ColorTransform;
            a._helpPoint = new t.Point;
            a._helpArray = [];
            a._intArray = [];
            a._floatArray = [];
            a._frameIntArray = [];
            a._frameFloatArray = [];
            a._frameArray = [];
            a._timelineArray = [];
            a._cacheRawMeshes = [];
            a._cacheMeshes = [];
            a._actionFrames = [];
            a._weightSlotPose = {};
            a._weightBonePoses = {};
            a._cacheBones = {};
            a._slotChildActions = {};
            return a
        }
        i._getBoolean = function(t, e, a) {
            if (e in t) {
                var i = t[e];
                var r = typeof i;
                if (r === "boolean") {
                    return i
                } else if (r === "string") {
                    switch (i) {
                        case "0":
                        case "NaN":
                        case "":
                        case "false":
                        case "null":
                        case "undefined":
                            return false;
                        default:
                            return true
                    }
                } else {
                    return !! i
                }
            }
            return a
        };
        i._getNumber = function(t, e, a) {
            if (e in t) {
                var i = t[e];
                if (i === null || i === "NaN") {
                    return a
                }
                return + i || 0
            }
            return a
        };
        i._getString = function(e, a, i) {
            if (a in e) {
                var r = e[a];
                var n = typeof r;
                if (n === "string") {
                    if (t.DragonBones.webAssembly) {
                        for (var s = 0,
                                 o = r.length; s < o; ++s) {
                            if (r.charCodeAt(s) > 255) {
                                return encodeURI(r)
                            }
                        }
                    }
                    return r
                }
                return String(r)
            }
            return i
        };
        i.prototype._getCurvePoint = function(t, e, a, i, r, n, s, o, l, h) {
            var f = 1 - l;
            var u = f * f;
            var _ = l * l;
            var c = f * u;
            var m = 3 * l * u;
            var p = 3 * f * _;
            var d = l * _;
            h.x = c * t + m * a + p * r + d * s;
            h.y = c * e + m * i + p * n + d * o
        };
        i.prototype._samplingEasingCurve = function(t, e) {
            var a = t.length;
            var i = -2;
            for (var r = 0,
                     n = e.length; r < n; ++r) {
                var s = (r + 1) / (n + 1);
                while ((i + 6 < a ? t[i + 6] : 1) < s) {
                    i += 6
                }
                var o = i >= 0 && i + 6 < a;
                var l = o ? t[i] : 0;
                var h = o ? t[i + 1] : 0;
                var f = t[i + 2];
                var u = t[i + 3];
                var _ = t[i + 4];
                var c = t[i + 5];
                var m = o ? t[i + 6] : 1;
                var p = o ? t[i + 7] : 1;
                var d = 0;
                var y = 1;
                while (y - d > 1e-4) {
                    var g = (y + d) * .5;
                    this._getCurvePoint(l, h, f, u, _, c, m, p, g, this._helpPoint);
                    if (s - this._helpPoint.x > 0) {
                        d = g
                    } else {
                        y = g
                    }
                }
                e[r] = this._helpPoint.y
            }
        };
        i.prototype._parseActionDataInFrame = function(e, a, i, r) {
            if (t.DataParser.EVENT in e) {
                this._mergeActionFrame(e[t.DataParser.EVENT], a, 10, i, r)
            }
            if (t.DataParser.SOUND in e) {
                this._mergeActionFrame(e[t.DataParser.SOUND], a, 11, i, r)
            }
            if (t.DataParser.ACTION in e) {
                this._mergeActionFrame(e[t.DataParser.ACTION], a, 0, i, r)
            }
            if (t.DataParser.EVENTS in e) {
                this._mergeActionFrame(e[t.DataParser.EVENTS], a, 10, i, r)
            }
            if (t.DataParser.ACTIONS in e) {
                this._mergeActionFrame(e[t.DataParser.ACTIONS], a, 0, i, r)
            }
        };
        i.prototype._mergeActionFrame = function(e, i, r, n, s) {
            var o = t.DragonBones.webAssembly ? this._armature.actions.size() : this._armature.actions.length;
            var l = this._parseActionData(e, r, n, s);
            var h = 0;
            var f = null;
            for (var u = 0,
                     _ = l; u < _.length; u++) {
                var c = _[u];
                this._armature.addAction(c, false)
            }
            if (this._actionFrames.length === 0) {
                f = new a;
                f.frameStart = 0;
                this._actionFrames.push(f);
                f = null
            }
            for (var m = 0,
                     p = this._actionFrames; m < p.length; m++) {
                var d = p[m];
                if (d.frameStart === i) {
                    f = d;
                    break
                } else if (d.frameStart > i) {
                    break
                }
                h++
            }
            if (f === null) {
                f = new a;
                f.frameStart = i;
                this._actionFrames.splice(h + 1, 0, f)
            }
            for (var y = 0; y < l.length; ++y) {
                f.actions.push(o + y)
            }
        };
        i.prototype._parseArmature = function(e, a) {
            var r = t.BaseObject.borrowObject(t.ArmatureData);
            r.name = i._getString(e, t.DataParser.NAME, "");
            r.frameRate = i._getNumber(e, t.DataParser.FRAME_RATE, this._data.frameRate);
            r.scale = a;
            if (t.DataParser.TYPE in e && typeof e[t.DataParser.TYPE] === "string") {
                r.type = t.DataParser._getArmatureType(e[t.DataParser.TYPE])
            } else {
                r.type = i._getNumber(e, t.DataParser.TYPE, 0)
            }
            if (r.frameRate === 0) {
                r.frameRate = 24
            }
            this._armature = r;
            if (t.DataParser.CANVAS in e) {
                var n = e[t.DataParser.CANVAS];
                var s = t.BaseObject.borrowObject(t.CanvasData);
                if (t.DataParser.COLOR in n) {
                    s.hasBackground = true
                } else {
                    s.hasBackground = false
                }
                s.color = i._getNumber(n, t.DataParser.COLOR, 0);
                s.x = i._getNumber(n, t.DataParser.X, 0) * r.scale;
                s.y = i._getNumber(n, t.DataParser.Y, 0) * r.scale;
                s.width = i._getNumber(n, t.DataParser.WIDTH, 0) * r.scale;
                s.height = i._getNumber(n, t.DataParser.HEIGHT, 0) * r.scale;
                r.canvas = s
            }
            if (t.DataParser.AABB in e) {
                var o = e[t.DataParser.AABB];
                r.aabb.x = i._getNumber(o, t.DataParser.X, 0) * r.scale;
                r.aabb.y = i._getNumber(o, t.DataParser.Y, 0) * r.scale;
                r.aabb.width = i._getNumber(o, t.DataParser.WIDTH, 0) * r.scale;
                r.aabb.height = i._getNumber(o, t.DataParser.HEIGHT, 0) * r.scale
            }
            if (t.DataParser.BONE in e) {
                var l = e[t.DataParser.BONE];
                for (var h = 0,
                         f = l; h < f.length; h++) {
                    var u = f[h];
                    var _ = i._getString(u, t.DataParser.PARENT, "");
                    var c = this._parseBone(u);
                    if (_.length > 0) {
                        var m = r.getBone(_);
                        if (m !== null) {
                            c.parent = m
                        } else {
                            if (! (_ in this._cacheBones)) {
                                this._cacheBones[_] = []
                            }
                            this._cacheBones[_].push(c)
                        }
                    }
                    if (c.name in this._cacheBones) {
                        for (var p = 0,
                                 d = this._cacheBones[c.name]; p < d.length; p++) {
                            var y = d[p];
                            y.parent = c
                        }
                        delete this._cacheBones[c.name]
                    }
                    r.addBone(c);
                    this._rawBones.push(c)
                }
            }
            if (t.DataParser.IK in e) {
                var g = e[t.DataParser.IK];
                for (var v = 0,
                         b = g; v < b.length; v++) {
                    var D = b[v];
                    var T = this._parseIKConstraint(D);
                    if (T) {
                        r.addConstraint(T)
                    }
                }
            }
            r.sortBones();
            if (t.DataParser.SLOT in e) {
                var A = 0;
                var x = e[t.DataParser.SLOT];
                for (var P = 0,
                         O = x; P < O.length; P++) {
                    var S = O[P];
                    r.addSlot(this._parseSlot(S, A++))
                }
            }
            if (t.DataParser.SKIN in e) {
                var E = e[t.DataParser.SKIN];
                for (var B = 0,
                         M = E; B < M.length; B++) {
                    var w = M[B];
                    r.addSkin(this._parseSkin(w))
                }
            }
            for (var C = 0,
                     I = this._cacheRawMeshes.length; C < I; ++C) {
                var F = this._cacheRawMeshes[C];
                if (! (t.DataParser.GLUE_WEIGHTS in F) || !(t.DataParser.GLUE_MESHES in F)) {
                    continue
                }
                this._parseMeshGlue(F, this._cacheMeshes[C])
            }
            for (var C = 0,
                     I = this._cacheRawMeshes.length; C < I; ++C) {
                var N = this._cacheRawMeshes[C];
                var R = i._getString(N, t.DataParser.SHARE, "");
                if (R.length === 0) {
                    continue
                }
                var k = i._getString(N, t.DataParser.SKIN, t.DataParser.DEFAULT_NAME);
                if (k.length === 0) {
                    k = t.DataParser.DEFAULT_NAME
                }
                var j = r.getMesh(k, "", R);
                if (j === null) {
                    continue
                }
                var L = this._cacheMeshes[C];
                L.offset = j.offset;
                L.weight = j.weight;
                L.glue = j.glue
            }
            if (t.DataParser.ANIMATION in e) {
                var U = e[t.DataParser.ANIMATION];
                for (var Y = 0,
                         V = U; Y < V.length; Y++) {
                    var X = V[Y];
                    var $ = this._parseAnimation(X);
                    r.addAnimation($)
                }
            }
            if (t.DataParser.DEFAULT_ACTIONS in e) {
                var H = this._parseActionData(e[t.DataParser.DEFAULT_ACTIONS], 0, null, null);
                for (var G = 0,
                         W = H; G < W.length; G++) {
                    var z = W[G];
                    r.addAction(z, true);
                    if (z.type === 0) {
                        var $ = r.getAnimation(z.name);
                        if ($ !== null) {
                            r.defaultAnimation = $
                        }
                    }
                }
            }
            if (t.DataParser.ACTIONS in e) {
                var H = this._parseActionData(e[t.DataParser.ACTIONS], 0, null, null);
                for (var K = 0,
                         Z = H; K < Z.length; K++) {
                    var z = Z[K];
                    r.addAction(z, false)
                }
            }
            this._rawBones.length = 0;
            this._cacheRawMeshes.length = 0;
            this._cacheMeshes.length = 0;
            this._armature = null;
            for (var q in this._weightSlotPose) {
                delete this._weightSlotPose[q]
            }
            for (var q in this._weightBonePoses) {
                delete this._weightBonePoses[q]
            }
            for (var q in this._cacheBones) {
                delete this._cacheBones[q]
            }
            for (var q in this._slotChildActions) {
                delete this._slotChildActions[q]
            }
            return r
        };
        i.prototype._parseBone = function(e) {
            var a = 0;
            var r = this._armature.scale;
            if (t.DataParser.TYPE in e && typeof e[t.DataParser.TYPE] === "string") {
                a = t.DataParser._getBoneType(e[t.DataParser.TYPE])
            } else {
                a = i._getNumber(e, t.DataParser.TYPE, 0)
            }
            if (a === 0) {
                var n = t.BaseObject.borrowObject(t.BoneData);
                n.inheritTranslation = i._getBoolean(e, t.DataParser.INHERIT_TRANSLATION, true);
                n.inheritRotation = i._getBoolean(e, t.DataParser.INHERIT_ROTATION, true);
                n.inheritScale = i._getBoolean(e, t.DataParser.INHERIT_SCALE, true);
                n.inheritReflection = i._getBoolean(e, t.DataParser.INHERIT_REFLECTION, true);
                n.length = i._getNumber(e, t.DataParser.LENGTH, 0) * r;
                n.name = i._getString(e, t.DataParser.NAME, "");
                if (t.DataParser.TRANSFORM in e) {
                    this._parseTransform(e[t.DataParser.TRANSFORM], n.transform, r)
                }
                return n
            }
            var s = t.BaseObject.borrowObject(t.SurfaceData);
            s.name = i._getString(e, t.DataParser.NAME, "");
            s.segmentX = i._getNumber(e, t.DataParser.SEGMENT_X, 0);
            s.segmentY = i._getNumber(e, t.DataParser.SEGMENT_Y, 0);
            s.vertices.length = (s.segmentX + 1) * (s.segmentY + 1) * 2;
            if (t.DataParser.VERTICES in e) {
                var o = e[t.DataParser.VERTICES];
                for (var l = 0,
                         h = s.vertices.length; l < h; ++l) {
                    if (l < o.length) {
                        s.vertices[l] = o[l] * r
                    } else {
                        s.vertices[l] = 0
                    }
                }
            }
            return s
        };
        i.prototype._parseIKConstraint = function(e) {
            var a = this._armature.getBone(i._getString(e, t.DataParser.BONE, ""));
            if (a === null) {
                return null
            }
            var r = this._armature.getBone(i._getString(e, t.DataParser.TARGET, ""));
            if (r === null) {
                return null
            }
            var n = t.BaseObject.borrowObject(t.IKConstraintData);
            n.scaleEnabled = i._getBoolean(e, t.DataParser.SCALE, false);
            n.bendPositive = i._getBoolean(e, t.DataParser.BEND_POSITIVE, true);
            n.weight = i._getNumber(e, t.DataParser.WEIGHT, 1);
            n.name = i._getString(e, t.DataParser.NAME, "");
            n.target = r;
            var s = i._getNumber(e, t.DataParser.CHAIN, 0);
            if (s > 0 && a.parent !== null) {
                n.root = a.parent;
                n.bone = a
            } else {
                n.root = a;
                n.bone = null
            }
            return n
        };
        i.prototype._parseSlot = function(e, a) {
            var r = t.BaseObject.borrowObject(t.SlotData);
            r.displayIndex = i._getNumber(e, t.DataParser.DISPLAY_INDEX, 0);
            r.zOrder = a;
            r.name = i._getString(e, t.DataParser.NAME, "");
            r.parent = this._armature.getBone(i._getString(e, t.DataParser.PARENT, ""));
            if (t.DataParser.BLEND_MODE in e && typeof e[t.DataParser.BLEND_MODE] === "string") {
                r.blendMode = t.DataParser._getBlendMode(e[t.DataParser.BLEND_MODE])
            } else {
                r.blendMode = i._getNumber(e, t.DataParser.BLEND_MODE, 0)
            }
            if (t.DataParser.COLOR in e) {
                r.color = t.SlotData.createColor();
                this._parseColorTransform(e[t.DataParser.COLOR], r.color)
            } else {
                r.color = t.SlotData.DEFAULT_COLOR
            }
            if (t.DataParser.ACTIONS in e) {
                this._slotChildActions[r.name] = this._parseActionData(e[t.DataParser.ACTIONS], 0, null, null)
            }
            return r
        };
        i.prototype._parseSkin = function(e) {
            var a = t.BaseObject.borrowObject(t.SkinData);
            a.name = i._getString(e, t.DataParser.NAME, t.DataParser.DEFAULT_NAME);
            if (a.name.length === 0) {
                a.name = t.DataParser.DEFAULT_NAME
            }
            if (t.DataParser.SLOT in e) {
                var r = e[t.DataParser.SLOT];
                this._skin = a;
                for (var n = 0,
                         s = r; n < s.length; n++) {
                    var o = s[n];
                    var l = i._getString(o, t.DataParser.NAME, "");
                    var h = this._armature.getSlot(l);
                    if (h !== null) {
                        this._slot = h;
                        if (t.DataParser.DISPLAY in o) {
                            var f = o[t.DataParser.DISPLAY];
                            for (var u = 0,
                                     _ = f; u < _.length; u++) {
                                var c = _[u];
                                if (c) {
                                    a.addDisplay(l, this._parseDisplay(c))
                                } else {
                                    a.addDisplay(l, null)
                                }
                            }
                        }
                        this._slot = null
                    }
                }
                this._skin = null
            }
            return a
        };
        i.prototype._parseDisplay = function(e) {
            var a = i._getString(e, t.DataParser.NAME, "");
            var r = i._getString(e, t.DataParser.PATH, "");
            var n = 0;
            var s = null;
            if (t.DataParser.TYPE in e && typeof e[t.DataParser.TYPE] === "string") {
                n = t.DataParser._getDisplayType(e[t.DataParser.TYPE])
            } else {
                n = i._getNumber(e, t.DataParser.TYPE, n)
            }
            switch (n) {
                case 0:
                    var o = s = t.BaseObject.borrowObject(t.ImageDisplayData);
                    o.name = a;
                    o.path = r.length > 0 ? r: a;
                    this._parsePivot(e, o);
                    break;
                case 1:
                    var l = s = t.BaseObject.borrowObject(t.ArmatureDisplayData);
                    l.name = a;
                    l.path = r.length > 0 ? r: a;
                    l.inheritAnimation = true;
                    if (t.DataParser.ACTIONS in e) {
                        var h = this._parseActionData(e[t.DataParser.ACTIONS], 0, null, null);
                        for (var f = 0,
                                 u = h; f < u.length; f++) {
                            var _ = u[f];
                            l.addAction(_)
                        }
                    } else if (this._slot.name in this._slotChildActions) {
                        var c = this._skin.getDisplays(this._slot.name);
                        if (c === null ? this._slot.displayIndex === 0 : this._slot.displayIndex === c.length) {
                            for (var m = 0,
                                     p = this._slotChildActions[this._slot.name]; m < p.length; m++) {
                                var _ = p[m];
                                l.addAction(_)
                            }
                            delete this._slotChildActions[this._slot.name]
                        }
                    }
                    break;
                case 2:
                    var d = s = t.BaseObject.borrowObject(t.MeshDisplayData);
                    d.inheritDeform = i._getBoolean(e, t.DataParser.INHERIT_DEFORM, true);
                    d.name = a;
                    d.path = r.length > 0 ? r: a;
                    if (t.DataParser.SHARE in e) {
                        this._cacheRawMeshes.push(e);
                        this._cacheMeshes.push(d)
                    } else {
                        this._parseMesh(e, d)
                    }
                    if (t.DataParser.GLUE_WEIGHTS in e && t.DataParser.GLUE_MESHES in e) {
                        this._cacheRawMeshes.push(e);
                        this._cacheMeshes.push(d)
                    }
                    break;
                case 3:
                    var y = this._parseBoundingBox(e);
                    if (y !== null) {
                        var g = s = t.BaseObject.borrowObject(t.BoundingBoxDisplayData);
                        g.name = a;
                        g.path = r.length > 0 ? r: a;
                        g.boundingBox = y
                    }
                    break
            }
            if (s !== null && t.DataParser.TRANSFORM in e) {
                this._parseTransform(e[t.DataParser.TRANSFORM], s.transform, this._armature.scale)
            }
            return s
        };
        i.prototype._parsePivot = function(e, a) {
            if (t.DataParser.PIVOT in e) {
                var r = e[t.DataParser.PIVOT];
                a.pivot.x = i._getNumber(r, t.DataParser.X, 0);
                a.pivot.y = i._getNumber(r, t.DataParser.Y, 0)
            } else {
                a.pivot.x = .5;
                a.pivot.y = .5
            }
        };
        i.prototype._parseMesh = function(e, a) {
            var i = e[t.DataParser.VERTICES];
            var r = e[t.DataParser.UVS];
            var n = e[t.DataParser.TRIANGLES];
            var s = Math.floor(i.length / 2);
            var o = Math.floor(n.length / 3);
            var l = this._floatArray.length;
            var h = l + s * 2;
            var f = this._intArray.length;
            var u = this._skin.name + "_" + this._slot.name + "_" + a.name;
            a.offset = f;
            this._intArray.length += 1 + 1 + 1 + 1 + o * 3;
            this._intArray[f + 0] = s;
            this._intArray[f + 1] = o;
            this._intArray[f + 2] = l;
            for (var _ = 0,
                     c = o * 3; _ < c; ++_) {
                this._intArray[f + 4 + _] = n[_]
            }
            this._floatArray.length += s * 2 + s * 2;
            for (var _ = 0,
                     c = s * 2; _ < c; ++_) {
                this._floatArray[l + _] = i[_];
                this._floatArray[h + _] = r[_]
            }
            if (t.DataParser.WEIGHTS in e) {
                var m = e[t.DataParser.WEIGHTS];
                var p = e[t.DataParser.SLOT_POSE];
                var d = e[t.DataParser.BONE_POSE];
                var y = this._armature.sortedBones;
                var g = new Array;
                var v = Math.floor(d.length / 7);
                var b = this._floatArray.length;
                var D = Math.floor(m.length - s) / 2;
                var T = this._intArray.length;
                var A = t.BaseObject.borrowObject(t.WeightData);
                A.count = D;
                A.offset = T;
                g.length = v;
                this._intArray.length += 1 + 1 + v + s + D;
                this._intArray[T + 1] = b;
                for (var _ = 0; _ < v; ++_) {
                    var x = d[_ * 7];
                    var P = this._rawBones[x];
                    A.addBone(P);
                    g[_] = x;
                    this._intArray[T + 2 + _] = y.indexOf(P)
                }
                this._floatArray.length += D * 3;
                this._helpMatrixA.copyFromArray(p, 0);
                for (var _ = 0,
                         O = 0,
                         S = T + 2 + v,
                         E = b; _ < s; ++_) {
                    var B = _ * 2;
                    var M = this._intArray[S++] = m[O++];
                    var w = this._floatArray[l + B];
                    var C = this._floatArray[l + B + 1];
                    this._helpMatrixA.transformPoint(w, C, this._helpPoint);
                    w = this._helpPoint.x;
                    C = this._helpPoint.y;
                    for (var I = 0; I < M; ++I) {
                        var x = m[O++];
                        var F = g.indexOf(x);
                        this._helpMatrixB.copyFromArray(d, F * 7 + 1);
                        this._helpMatrixB.invert();
                        this._helpMatrixB.transformPoint(w, C, this._helpPoint);
                        this._intArray[S++] = F;
                        this._floatArray[E++] = m[O++];
                        this._floatArray[E++] = this._helpPoint.x;
                        this._floatArray[E++] = this._helpPoint.y
                    }
                }
                a.weight = A;
                this._weightSlotPose[u] = p;
                this._weightBonePoses[u] = d
            }
        };
        i.prototype._parseMeshGlue = function(e, a) {
            var i = e[t.DataParser.GLUE_WEIGHTS];
            var r = e[t.DataParser.GLUE_MESHES];
            a.glue = t.BaseObject.borrowObject(t.GlueData);
            a.glue.weights.length = i.length;
            for (var n = 0,
                     s = i.length; n < s; ++n) {
                a.glue.weights[n] = i[n]
            }
            for (var n = 0,
                     s = r.length; n < s; n += 3) {
                var o = this._armature.getMesh(r[n], r[n + 1], r[n + 2]);
                a.glue.addMesh(o)
            }
        };
        i.prototype._parseBoundingBox = function(e) {
            var a = null;
            var r = 0;
            if (t.DataParser.SUB_TYPE in e && typeof e[t.DataParser.SUB_TYPE] === "string") {
                r = t.DataParser._getBoundingBoxType(e[t.DataParser.SUB_TYPE])
            } else {
                r = i._getNumber(e, t.DataParser.SUB_TYPE, r)
            }
            switch (r) {
                case 0:
                    a = t.BaseObject.borrowObject(t.RectangleBoundingBoxData);
                    break;
                case 1:
                    a = t.BaseObject.borrowObject(t.EllipseBoundingBoxData);
                    break;
                case 2:
                    a = this._parsePolygonBoundingBox(e);
                    break
            }
            if (a !== null) {
                a.color = i._getNumber(e, t.DataParser.COLOR, 0);
                if (a.type === 0 || a.type === 1) {
                    a.width = i._getNumber(e, t.DataParser.WIDTH, 0);
                    a.height = i._getNumber(e, t.DataParser.HEIGHT, 0)
                }
            }
            return a
        };
        i.prototype._parsePolygonBoundingBox = function(e) {
            var a = t.BaseObject.borrowObject(t.PolygonBoundingBoxData);
            if (t.DataParser.VERTICES in e) {
                var i = this._armature.scale;
                var r = e[t.DataParser.VERTICES];
                var n = a.vertices;
                if (t.DragonBones.webAssembly) {
                    n.resize(r.length, 0)
                } else {
                    n.length = r.length
                }
                for (var s = 0,
                         o = r.length; s < o; s += 2) {
                    var l = r[s] * i;
                    var h = r[s + 1] * i;
                    if (t.DragonBones.webAssembly) {
                        n.set(s, l);
                        n.set(s + 1, h)
                    } else {
                        n[s] = l;
                        n[s + 1] = h
                    }
                    if (s === 0) {
                        a.x = l;
                        a.y = h;
                        a.width = l;
                        a.height = h
                    } else {
                        if (l < a.x) {
                            a.x = l
                        } else if (l > a.width) {
                            a.width = l
                        }
                        if (h < a.y) {
                            a.y = h
                        } else if (h > a.height) {
                            a.height = h
                        }
                    }
                }
                a.width -= a.x;
                a.height -= a.y
            } else {
                console.warn("Data error.\n Please reexport DragonBones Data to fixed the bug.")
            }
            return a
        };
        i.prototype._parseAnimation = function(e) {
            var a = t.BaseObject.borrowObject(t.AnimationData);
            a.frameCount = Math.max(i._getNumber(e, t.DataParser.DURATION, 1), 1);
            a.playTimes = i._getNumber(e, t.DataParser.PLAY_TIMES, 1);
            a.duration = a.frameCount / this._armature.frameRate;
            a.fadeInTime = i._getNumber(e, t.DataParser.FADE_IN_TIME, 0);
            a.scale = i._getNumber(e, t.DataParser.SCALE, 1);
            a.name = i._getString(e, t.DataParser.NAME, t.DataParser.DEFAULT_NAME);
            if (a.name.length === 0) {
                a.name = t.DataParser.DEFAULT_NAME
            }
            a.frameIntOffset = this._frameIntArray.length;
            a.frameFloatOffset = this._frameFloatArray.length;
            a.frameOffset = this._frameArray.length;
            this._animation = a;
            if (t.DataParser.FRAME in e) {
                var r = e[t.DataParser.FRAME];
                var n = r.length;
                if (n > 0) {
                    for (var s = 0,
                             o = 0; s < n; ++s) {
                        var l = r[s];
                        this._parseActionDataInFrame(l, o, null, null);
                        o += i._getNumber(l, t.DataParser.DURATION, 1)
                    }
                }
            }
            if (t.DataParser.Z_ORDER in e) {
                this._animation.zOrderTimeline = this._parseTimeline(e[t.DataParser.Z_ORDER], null, t.DataParser.FRAME, 1, false, false, 0, this._parseZOrderFrame)
            }
            if (t.DataParser.BONE in e) {
                var h = e[t.DataParser.BONE];
                for (var f = 0,
                         u = h; f < u.length; f++) {
                    var _ = u[f];
                    this._parseBoneTimeline(_)
                }
            }
            if (t.DataParser.SURFACE in e) {
                var h = e[t.DataParser.SURFACE];
                for (var c = 0,
                         m = h; c < m.length; c++) {
                    var _ = m[c];
                    var p = i._getString(_, t.DataParser.NAME, "");
                    this._surface = this._armature.getBone(p);
                    if (this._surface === null) {
                        continue
                    }
                    var d = this._parseTimeline(_, null, t.DataParser.FRAME, 50, false, true, 0, this._parseSurfaceFrame);
                    if (d !== null) {
                        this._animation.addSurfaceTimeline(this._surface, d)
                    }
                    this._surface = null
                }
            }
            if (t.DataParser.SLOT in e) {
                var h = e[t.DataParser.SLOT];
                for (var y = 0,
                         g = h; y < g.length; y++) {
                    var _ = g[y];
                    this._parseSlotTimeline(_)
                }
            }
            if (t.DataParser.FFD in e) {
                var h = e[t.DataParser.FFD];
                for (var v = 0,
                         b = h; v < b.length; v++) {
                    var _ = b[v];
                    var D = i._getString(_, t.DataParser.SKIN, t.DataParser.DEFAULT_NAME);
                    var T = i._getString(_, t.DataParser.SLOT, "");
                    var A = i._getString(_, t.DataParser.NAME, "");
                    if (D.length === 0) {
                        D = t.DataParser.DEFAULT_NAME
                    }
                    this._slot = this._armature.getSlot(T);
                    this._mesh = this._armature.getMesh(D, T, A);
                    if (this._slot === null || this._mesh === null) {
                        continue
                    }
                    var d = this._parseTimeline(_, null, t.DataParser.FRAME, 22, false, true, 0, this._parseSlotFFDFrame);
                    if (d !== null) {
                        this._animation.addSlotTimeline(this._slot, d)
                    }
                    this._slot = null;
                    this._mesh = null
                }
            }
            if (t.DataParser.IK in e) {
                var h = e[t.DataParser.IK];
                for (var x = 0,
                         P = h; x < P.length; x++) {
                    var _ = P[x];
                    var O = i._getString(_, t.DataParser.NAME, "");
                    var S = this._armature.getConstraint(O);
                    if (S === null) {
                        continue
                    }
                    var d = this._parseTimeline(_, null, t.DataParser.FRAME, 30, true, false, 2, this._parseIKConstraintFrame);
                    if (d !== null) {
                        this._animation.addConstraintTimeline(S, d)
                    }
                }
            }
            if (t.DataParser.ANIMATION in e) {
                var h = e[t.DataParser.ANIMATION];
                for (var E = 0,
                         B = h; E < B.length; E++) {
                    var _ = B[E];
                    var M = i._getString(_, t.DataParser.NAME, "");
                    var d = this._parseTimeline(_, null, t.DataParser.FRAME, 40, true, false, 2, this._parseAnimationFrame);
                    if (d !== null) {
                        this._animation.addAnimationTimeline(M, d)
                    }
                }
            }
            if (this._actionFrames.length > 0) {
                this._animation.actionTimeline = this._parseTimeline(null, this._actionFrames, "", 0, false, false, 0, this._parseActionFrame);
                this._actionFrames.length = 0
            }
            this._animation = null;
            return a
        };
        i.prototype._parseTimeline = function(e, r, n, s, o, l, h, f) {
            if (e !== null && n.length > 0 && n in e) {
                r = e[n]
            }
            if (r === null) {
                return null
            }
            var u = r.length;
            if (u === 0) {
                return null
            }
            var _ = this._frameIntArray.length;
            var c = this._frameFloatArray.length;
            var m = t.BaseObject.borrowObject(t.TimelineData);
            var p = this._timelineArray.length;
            this._timelineArray.length += 1 + 1 + 1 + 1 + 1 + u;
            if (e !== null) {
                this._timelineArray[p + 0] = Math.round(i._getNumber(e, t.DataParser.SCALE, 1) * 100);
                this._timelineArray[p + 1] = Math.round(i._getNumber(e, t.DataParser.OFFSET, 0) * 100)
            } else {
                this._timelineArray[p + 0] = 100;
                this._timelineArray[p + 1] = 0
            }
            this._timelineArray[p + 2] = u;
            this._timelineArray[p + 3] = h;
            if (o) {
                this._timelineArray[p + 4] = _ - this._animation.frameIntOffset
            } else if (l) {
                this._timelineArray[p + 4] = c - this._animation.frameFloatOffset
            } else {
                this._timelineArray[p + 4] = 0
            }
            this._timeline = m;
            m.type = s;
            m.offset = p;
            if (u === 1) {
                m.frameIndicesOffset = -1;
                this._timelineArray[p + 5 + 0] = f.call(this, r[0], 0, 0) - this._animation.frameOffset
            } else {
                var d = this._animation.frameCount + 1;
                var y = this._data.frameIndices;
                var g = 0;
                if (t.DragonBones.webAssembly) {
                    g = y.size();
                    y.resize(g + d, 0)
                } else {
                    g = y.length;
                    y.length += d
                }
                m.frameIndicesOffset = g;
                for (var v = 0,
                         b = 0,
                         D = 0,
                         T = 0; v < d; ++v) {
                    if (D + T <= v && b < u) {
                        var A = r[b];
                        D = v;
                        if (b === u - 1) {
                            T = this._animation.frameCount - D
                        } else {
                            if (A instanceof a) {
                                T = this._actionFrames[b + 1].frameStart - D
                            } else {
                                T = i._getNumber(A, t.DataParser.DURATION, 1)
                            }
                        }
                        this._timelineArray[p + 5 + b] = f.call(this, A, D, T) - this._animation.frameOffset;
                        b++
                    }
                    if (t.DragonBones.webAssembly) {
                        y.set(g + v, b - 1)
                    } else {
                        y[g + v] = b - 1
                    }
                }
            }
            this._timeline = null;
            return m
        };
        i.prototype._parseBoneTimeline = function(e) {
            var a = this._armature.getBone(i._getString(e, t.DataParser.NAME, ""));
            if (a === null) {
                return
            }
            this._bone = a;
            this._slot = this._armature.getSlot(this._bone.name);
            if (t.DataParser.TRANSLATE_FRAME in e) {
                var r = this._parseTimeline(e, null, t.DataParser.TRANSLATE_FRAME, 11, false, true, 2, this._parseBoneTranslateFrame);
                if (r !== null) {
                    this._animation.addBoneTimeline(a, r)
                }
            }
            if (t.DataParser.ROTATE_FRAME in e) {
                var r = this._parseTimeline(e, null, t.DataParser.ROTATE_FRAME, 12, false, true, 2, this._parseBoneRotateFrame);
                if (r !== null) {
                    this._animation.addBoneTimeline(a, r)
                }
            }
            if (t.DataParser.SCALE_FRAME in e) {
                var r = this._parseTimeline(e, null, t.DataParser.SCALE_FRAME, 13, false, true, 2, this._parseBoneScaleFrame);
                if (r !== null) {
                    this._animation.addBoneTimeline(a, r)
                }
            }
            if (t.DataParser.FRAME in e) {
                var r = this._parseTimeline(e, null, t.DataParser.FRAME, 10, false, true, 6, this._parseBoneAllFrame);
                if (r !== null) {
                    this._animation.addBoneTimeline(a, r)
                }
            }
            this._bone = null;
            this._slot = null
        };
        i.prototype._parseSlotTimeline = function(e) {
            var a = this._armature.getSlot(i._getString(e, t.DataParser.NAME, ""));
            if (a === null) {
                return
            }
            this._slot = a;
            var r = null;
            if (t.DataParser.DISPLAY_FRAME in e) {
                r = this._parseTimeline(e, null, t.DataParser.DISPLAY_FRAME, 20, false, false, 0, this._parseSlotDisplayFrame)
            } else {
                r = this._parseTimeline(e, null, t.DataParser.FRAME, 20, false, false, 0, this._parseSlotDisplayFrame)
            }
            if (r !== null) {
                this._animation.addSlotTimeline(a, r)
            }
            var n = null;
            if (t.DataParser.COLOR_FRAME in e) {
                n = this._parseTimeline(e, null, t.DataParser.COLOR_FRAME, 21, true, false, 1, this._parseSlotColorFrame)
            } else {
                n = this._parseTimeline(e, null, t.DataParser.FRAME, 21, true, false, 1, this._parseSlotColorFrame)
            }
            if (n !== null) {
                this._animation.addSlotTimeline(a, n)
            }
            this._slot = null
        };
        i.prototype._parseFrame = function(t, e, a) {
            t;
            a;
            var i = this._frameArray.length;
            this._frameArray.length += 1;
            this._frameArray[i + 0] = e;
            return i
        };
        i.prototype._parseTweenFrame = function(e, a, r) {
            var n = this._parseFrame(e, a, r);
            if (r > 0) {
                if (t.DataParser.CURVE in e) {
                    var s = r + 1;
                    this._helpArray.length = s;
                    this._samplingEasingCurve(e[t.DataParser.CURVE], this._helpArray);
                    this._frameArray.length += 1 + 1 + this._helpArray.length;
                    this._frameArray[n + 1] = 2;
                    this._frameArray[n + 2] = s;
                    for (var o = 0; o < s; ++o) {
                        this._frameArray[n + 3 + o] = Math.round(this._helpArray[o] * 1e4)
                    }
                } else {
                    var l = -2;
                    var h = l;
                    if (t.DataParser.TWEEN_EASING in e) {
                        h = i._getNumber(e, t.DataParser.TWEEN_EASING, l)
                    }
                    if (h === l) {
                        this._frameArray.length += 1;
                        this._frameArray[n + 1] = 0
                    } else if (h === 0) {
                        this._frameArray.length += 1;
                        this._frameArray[n + 1] = 1
                    } else if (h < 0) {
                        this._frameArray.length += 1 + 1;
                        this._frameArray[n + 1] = 3;
                        this._frameArray[n + 2] = Math.round( - h * 100)
                    } else if (h <= 1) {
                        this._frameArray.length += 1 + 1;
                        this._frameArray[n + 1] = 4;
                        this._frameArray[n + 2] = Math.round(h * 100)
                    } else {
                        this._frameArray.length += 1 + 1;
                        this._frameArray[n + 1] = 5;
                        this._frameArray[n + 2] = Math.round(h * 100 - 100)
                    }
                }
            } else {
                this._frameArray.length += 1;
                this._frameArray[n + 1] = 0
            }
            return n
        };
        i.prototype._parseActionFrame = function(t, e, a) {
            a;
            var i = this._frameArray.length;
            var r = t.actions.length;
            this._frameArray.length += 1 + 1 + r;
            this._frameArray[i + 0] = e;
            this._frameArray[i + 0 + 1] = r;
            for (var n = 0; n < r; ++n) {
                this._frameArray[i + 0 + 2 + n] = t.actions[n]
            }
            return i
        };
        i.prototype._parseZOrderFrame = function(e, a, i) {
            var r = this._parseFrame(e, a, i);
            if (t.DataParser.Z_ORDER in e) {
                var n = e[t.DataParser.Z_ORDER];
                if (n.length > 0) {
                    var s = this._armature.sortedSlots.length;
                    var o = new Array(s - n.length / 2);
                    var l = new Array(s);
                    for (var h = 0; h < o.length; ++h) {
                        o[h] = 0
                    }
                    for (var f = 0; f < s; ++f) {
                        l[f] = -1
                    }
                    var u = 0;
                    var _ = 0;
                    for (var c = 0,
                             m = n.length; c < m; c += 2) {
                        var p = n[c];
                        var d = n[c + 1];
                        while (u !== p) {
                            o[_++] = u++
                        }
                        var y = u + d;
                        l[y] = u++
                    }
                    while (u < s) {
                        o[_++] = u++
                    }
                    this._frameArray.length += 1 + s;
                    this._frameArray[r + 1] = s;
                    var g = s;
                    while (g--) {
                        if (l[g] === -1) {
                            this._frameArray[r + 2 + g] = o[--_] || 0
                        } else {
                            this._frameArray[r + 2 + g] = l[g] || 0
                        }
                    }
                    return r
                }
            }
            this._frameArray.length += 1;
            this._frameArray[r + 1] = 0;
            return r
        };
        i.prototype._parseBoneAllFrame = function(e, a, r) {
            this._helpTransform.identity();
            if (t.DataParser.TRANSFORM in e) {
                this._parseTransform(e[t.DataParser.TRANSFORM], this._helpTransform, 1)
            }
            var n = this._helpTransform.rotation;
            if (a !== 0) {
                if (this._prevClockwise === 0) {
                    n = this._prevRotation + t.Transform.normalizeRadian(n - this._prevRotation)
                } else {
                    if (this._prevClockwise > 0 ? n >= this._prevRotation: n <= this._prevRotation) {
                        this._prevClockwise = this._prevClockwise > 0 ? this._prevClockwise - 1 : this._prevClockwise + 1
                    }
                    n = this._prevRotation + n - this._prevRotation + t.Transform.PI_D * this._prevClockwise
                }
            }
            this._prevClockwise = i._getNumber(e, t.DataParser.TWEEN_ROTATE, 0);
            this._prevRotation = n;
            var s = this._parseTweenFrame(e, a, r);
            var o = this._frameFloatArray.length;
            this._frameFloatArray.length += 6;
            this._frameFloatArray[o++] = this._helpTransform.x;
            this._frameFloatArray[o++] = this._helpTransform.y;
            this._frameFloatArray[o++] = n;
            this._frameFloatArray[o++] = this._helpTransform.skew;
            this._frameFloatArray[o++] = this._helpTransform.scaleX;
            this._frameFloatArray[o++] = this._helpTransform.scaleY;
            this._parseActionDataInFrame(e, a, this._bone, this._slot);
            return s
        };
        i.prototype._parseBoneTranslateFrame = function(e, a, r) {
            var n = this._parseTweenFrame(e, a, r);
            var s = this._frameFloatArray.length;
            this._frameFloatArray.length += 2;
            this._frameFloatArray[s++] = i._getNumber(e, t.DataParser.X, 0);
            this._frameFloatArray[s++] = i._getNumber(e, t.DataParser.Y, 0);
            return n
        };
        i.prototype._parseBoneRotateFrame = function(e, a, r) {
            var n = i._getNumber(e, t.DataParser.ROTATE, 0) * t.Transform.DEG_RAD;
            if (a !== 0) {
                if (this._prevClockwise === 0) {
                    n = this._prevRotation + t.Transform.normalizeRadian(n - this._prevRotation)
                } else {
                    if (this._prevClockwise > 0 ? n >= this._prevRotation: n <= this._prevRotation) {
                        this._prevClockwise = this._prevClockwise > 0 ? this._prevClockwise - 1 : this._prevClockwise + 1
                    }
                    n = this._prevRotation + n - this._prevRotation + t.Transform.PI_D * this._prevClockwise
                }
            }
            this._prevClockwise = i._getNumber(e, t.DataParser.CLOCK_WISE, 0);
            this._prevRotation = n;
            var s = this._parseTweenFrame(e, a, r);
            var o = this._frameFloatArray.length;
            this._frameFloatArray.length += 2;
            this._frameFloatArray[o++] = n;
            this._frameFloatArray[o++] = i._getNumber(e, t.DataParser.SKEW, 0) * t.Transform.DEG_RAD;
            return s
        };
        i.prototype._parseBoneScaleFrame = function(e, a, r) {
            var n = this._parseTweenFrame(e, a, r);
            var s = this._frameFloatArray.length;
            this._frameFloatArray.length += 2;
            this._frameFloatArray[s++] = i._getNumber(e, t.DataParser.X, 1);
            this._frameFloatArray[s++] = i._getNumber(e, t.DataParser.Y, 1);
            return n
        };
        i.prototype._parseSurfaceFrame = function(e, a, r) {
            var n = this._frameFloatArray.length;
            var s = this._parseTweenFrame(e, a, r);
            var o = e[t.DataParser.VERTICES];
            var l = i._getNumber(e, t.DataParser.OFFSET, 0);
            var h = this._surface.vertices.length / 2;
            var f = 0;
            var u = 0;
            this._frameFloatArray.length += h * 2;
            for (var _ = 0; _ < h * 2; _ += 2) {
                if (_ < l || _ - l >= o.length) {
                    f = 0
                } else {
                    f = o[_ - l]
                }
                if (_ + 1 < l || _ + 1 - l >= o.length) {
                    u = 0
                } else {
                    u = o[_ + 1 - l]
                }
                this._frameFloatArray[n + _] = f;
                this._frameFloatArray[n + _ + 1] = u
            }
            if (a === 0) {
                var c = this._frameIntArray.length;
                this._frameIntArray.length += 1 + 1 + 1 + 1 + 1;
                this._frameIntArray[c + 0] = 0;
                this._frameIntArray[c + 1] = this._frameFloatArray.length - n;
                this._frameIntArray[c + 2] = this._frameFloatArray.length - n;
                this._frameIntArray[c + 3] = 0;
                this._frameIntArray[c + 4] = n - this._animation.frameFloatOffset;
                this._timelineArray[this._timeline.offset + 3] = c - this._animation.frameIntOffset
            }
            return s
        };
        i.prototype._parseSlotDisplayFrame = function(e, a, r) {
            var n = this._parseFrame(e, a, r);
            this._frameArray.length += 1;
            if (t.DataParser.VALUE in e) {
                this._frameArray[n + 1] = i._getNumber(e, t.DataParser.VALUE, 0)
            } else {
                this._frameArray[n + 1] = i._getNumber(e, t.DataParser.DISPLAY_INDEX, 0)
            }
            this._parseActionDataInFrame(e, a, this._slot.parent, this._slot);
            return n
        };
        i.prototype._parseSlotColorFrame = function(e, a, i) {
            var r = this._parseTweenFrame(e, a, i);
            var n = -1;
            if (t.DataParser.VALUE in e || t.DataParser.COLOR in e) {
                var s = t.DataParser.VALUE in e ? e[t.DataParser.VALUE] : e[t.DataParser.COLOR];
                for (var o in s) {
                    o;
                    this._parseColorTransform(s, this._helpColorTransform);
                    n = this._intArray.length;
                    this._intArray.length += 8;
                    this._intArray[n++] = Math.round(this._helpColorTransform.alphaMultiplier * 100);
                    this._intArray[n++] = Math.round(this._helpColorTransform.redMultiplier * 100);
                    this._intArray[n++] = Math.round(this._helpColorTransform.greenMultiplier * 100);
                    this._intArray[n++] = Math.round(this._helpColorTransform.blueMultiplier * 100);
                    this._intArray[n++] = Math.round(this._helpColorTransform.alphaOffset);
                    this._intArray[n++] = Math.round(this._helpColorTransform.redOffset);
                    this._intArray[n++] = Math.round(this._helpColorTransform.greenOffset);
                    this._intArray[n++] = Math.round(this._helpColorTransform.blueOffset);
                    n -= 8;
                    break
                }
            }
            if (n < 0) {
                if (this._defaultColorOffset < 0) {
                    this._defaultColorOffset = n = this._intArray.length;
                    this._intArray.length += 8;
                    this._intArray[n++] = 100;
                    this._intArray[n++] = 100;
                    this._intArray[n++] = 100;
                    this._intArray[n++] = 100;
                    this._intArray[n++] = 0;
                    this._intArray[n++] = 0;
                    this._intArray[n++] = 0;
                    this._intArray[n++] = 0
                }
                n = this._defaultColorOffset
            }
            var l = this._frameIntArray.length;
            this._frameIntArray.length += 1;
            this._frameIntArray[l] = n;
            return r
        };
        i.prototype._parseSlotFFDFrame = function(e, a, r) {
            var n = this._frameFloatArray.length;
            var s = this._parseTweenFrame(e, a, r);
            var o = t.DataParser.VERTICES in e ? e[t.DataParser.VERTICES] : null;
            var l = i._getNumber(e, t.DataParser.OFFSET, 0);
            var h = this._intArray[this._mesh.offset + 0];
            var f = this._mesh.parent.name + "_" + this._slot.name + "_" + this._mesh.name;
            var u = 0;
            var _ = 0;
            var c = 0;
            var m = 0;
            if (this._mesh.weight !== null) {
                var p = this._weightSlotPose[f];
                this._helpMatrixA.copyFromArray(p, 0);
                this._frameFloatArray.length += this._mesh.weight.count * 2;
                c = this._mesh.weight.offset + 2 + this._mesh.weight.bones.length
            } else {
                this._frameFloatArray.length += h * 2
            }
            for (var d = 0; d < h * 2; d += 2) {
                if (o === null) {
                    u = 0;
                    _ = 0
                } else {
                    if (d < l || d - l >= o.length) {
                        u = 0
                    } else {
                        u = o[d - l]
                    }
                    if (d + 1 < l || d + 1 - l >= o.length) {
                        _ = 0
                    } else {
                        _ = o[d + 1 - l]
                    }
                }
                if (this._mesh.weight !== null) {
                    var y = this._weightBonePoses[f];
                    var g = this._intArray[c++];
                    this._helpMatrixA.transformPoint(u, _, this._helpPoint, true);
                    u = this._helpPoint.x;
                    _ = this._helpPoint.y;
                    for (var v = 0; v < g; ++v) {
                        var b = this._intArray[c++];
                        this._helpMatrixB.copyFromArray(y, b * 7 + 1);
                        this._helpMatrixB.invert();
                        this._helpMatrixB.transformPoint(u, _, this._helpPoint, true);
                        this._frameFloatArray[n + m++] = this._helpPoint.x;
                        this._frameFloatArray[n + m++] = this._helpPoint.y
                    }
                } else {
                    this._frameFloatArray[n + d] = u;
                    this._frameFloatArray[n + d + 1] = _
                }
            }
            if (a === 0) {
                var D = this._frameIntArray.length;
                this._frameIntArray.length += 1 + 1 + 1 + 1 + 1;
                this._frameIntArray[D + 0] = this._mesh.offset;
                this._frameIntArray[D + 1] = this._frameFloatArray.length - n;
                this._frameIntArray[D + 2] = this._frameFloatArray.length - n;
                this._frameIntArray[D + 3] = 0;
                this._frameIntArray[D + 4] = n - this._animation.frameFloatOffset;
                this._timelineArray[this._timeline.offset + 3] = D - this._animation.frameIntOffset
            }
            return s
        };
        i.prototype._parseIKConstraintFrame = function(e, a, r) {
            var n = this._parseTweenFrame(e, a, r);
            var s = this._frameIntArray.length;
            this._frameIntArray.length += 2;
            this._frameIntArray[s++] = i._getBoolean(e, t.DataParser.BEND_POSITIVE, true) ? 1 : 0;
            this._frameIntArray[s++] = Math.round(i._getNumber(e, t.DataParser.WEIGHT, 1) * 100);
            return n
        };
        i.prototype._parseAnimationFrame = function(e, a, r) {
            var n = this._parseTweenFrame(e, a, r);
            var s = this._frameIntArray.length;
            this._frameIntArray.length += 2;
            this._frameIntArray[s++] = i._getNumber(e, t.DataParser.VALUE, 0);
            this._frameIntArray[s++] = Math.round(i._getNumber(e, t.DataParser.WEIGHT, 1) * 100);
            return n
        };
        i.prototype._parseActionData = function(e, a, r, n) {
            var s = new Array;
            if (typeof e === "string") {
                var o = t.BaseObject.borrowObject(t.ActionData);
                o.type = a;
                o.name = e;
                o.bone = r;
                o.slot = n;
                s.push(o)
            } else if (e instanceof Array) {
                for (var l = 0,
                         h = e; l < h.length; l++) {
                    var f = h[l];
                    var o = t.BaseObject.borrowObject(t.ActionData);
                    if (t.DataParser.GOTO_AND_PLAY in f) {
                        o.type = 0;
                        o.name = i._getString(f, t.DataParser.GOTO_AND_PLAY, "")
                    } else {
                        if (t.DataParser.TYPE in f && typeof f[t.DataParser.TYPE] === "string") {
                            o.type = t.DataParser._getActionType(f[t.DataParser.TYPE])
                        } else {
                            o.type = i._getNumber(f, t.DataParser.TYPE, a)
                        }
                        o.name = i._getString(f, t.DataParser.NAME, "")
                    }
                    if (t.DataParser.BONE in f) {
                        var u = i._getString(f, t.DataParser.BONE, "");
                        o.bone = this._armature.getBone(u)
                    } else {
                        o.bone = r
                    }
                    if (t.DataParser.SLOT in f) {
                        var _ = i._getString(f, t.DataParser.SLOT, "");
                        o.slot = this._armature.getSlot(_)
                    } else {
                        o.slot = n
                    }
                    var c = null;
                    if (t.DataParser.INTS in f) {
                        if (c === null) {
                            c = t.BaseObject.borrowObject(t.UserData)
                        }
                        var m = f[t.DataParser.INTS];
                        for (var p = 0,
                                 d = m; p < d.length; p++) {
                            var y = d[p];
                            c.addInt(y)
                        }
                    }
                    if (t.DataParser.FLOATS in f) {
                        if (c === null) {
                            c = t.BaseObject.borrowObject(t.UserData)
                        }
                        var g = f[t.DataParser.FLOATS];
                        for (var v = 0,
                                 b = g; v < b.length; v++) {
                            var y = b[v];
                            c.addFloat(y)
                        }
                    }
                    if (t.DataParser.STRINGS in f) {
                        if (c === null) {
                            c = t.BaseObject.borrowObject(t.UserData)
                        }
                        var D = f[t.DataParser.STRINGS];
                        for (var T = 0,
                                 A = D; T < A.length; T++) {
                            var y = A[T];
                            c.addString(y)
                        }
                    }
                    o.data = c;
                    s.push(o)
                }
            }
            return s
        };
        i.prototype._parseTransform = function(e, a, r) {
            a.x = i._getNumber(e, t.DataParser.X, 0) * r;
            a.y = i._getNumber(e, t.DataParser.Y, 0) * r;
            if (t.DataParser.ROTATE in e || t.DataParser.SKEW in e) {
                a.rotation = t.Transform.normalizeRadian(i._getNumber(e, t.DataParser.ROTATE, 0) * t.Transform.DEG_RAD);
                a.skew = t.Transform.normalizeRadian(i._getNumber(e, t.DataParser.SKEW, 0) * t.Transform.DEG_RAD)
            } else if (t.DataParser.SKEW_X in e || t.DataParser.SKEW_Y in e) {
                a.rotation = t.Transform.normalizeRadian(i._getNumber(e, t.DataParser.SKEW_Y, 0) * t.Transform.DEG_RAD);
                a.skew = t.Transform.normalizeRadian(i._getNumber(e, t.DataParser.SKEW_X, 0) * t.Transform.DEG_RAD) - a.rotation
            }
            a.scaleX = i._getNumber(e, t.DataParser.SCALE_X, 1);
            a.scaleY = i._getNumber(e, t.DataParser.SCALE_Y, 1)
        };
        i.prototype._parseColorTransform = function(e, a) {
            a.alphaMultiplier = i._getNumber(e, t.DataParser.ALPHA_MULTIPLIER, 100) * .01;
            a.redMultiplier = i._getNumber(e, t.DataParser.RED_MULTIPLIER, 100) * .01;
            a.greenMultiplier = i._getNumber(e, t.DataParser.GREEN_MULTIPLIER, 100) * .01;
            a.blueMultiplier = i._getNumber(e, t.DataParser.BLUE_MULTIPLIER, 100) * .01;
            a.alphaOffset = i._getNumber(e, t.DataParser.ALPHA_OFFSET, 0);
            a.redOffset = i._getNumber(e, t.DataParser.RED_OFFSET, 0);
            a.greenOffset = i._getNumber(e, t.DataParser.GREEN_OFFSET, 0);
            a.blueOffset = i._getNumber(e, t.DataParser.BLUE_OFFSET, 0)
        };
        i.prototype._parseArray = function(t) {
            t;
            this._intArray.length = 0;
            this._floatArray.length = 0;
            this._frameIntArray.length = 0;
            this._frameFloatArray.length = 0;
            this._frameArray.length = 0;
            this._timelineArray.length = 0
        };
        i.prototype._modifyArray = function() {
            if (this._intArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._intArray.push(0)
            }
            if (this._frameIntArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._frameIntArray.push(0)
            }
            if (this._frameArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._frameArray.push(0)
            }
            if (this._timelineArray.length % Uint16Array.BYTES_PER_ELEMENT !== 0) {
                this._timelineArray.push(0)
            }
            var e = this._intArray.length * Int16Array.BYTES_PER_ELEMENT;
            var a = this._floatArray.length * Float32Array.BYTES_PER_ELEMENT;
            var i = this._frameIntArray.length * Int16Array.BYTES_PER_ELEMENT;
            var r = this._frameFloatArray.length * Float32Array.BYTES_PER_ELEMENT;
            var n = this._frameArray.length * Int16Array.BYTES_PER_ELEMENT;
            var s = this._timelineArray.length * Uint16Array.BYTES_PER_ELEMENT;
            var o = e + a + i + r + n + s;
            if (t.DragonBones.webAssembly) {
                var l = t.webAssemblyModule.HEAP16.buffer;
                var h = t.webAssemblyModule._malloc(o);
                var f = new Int16Array(l, h, this._intArray.length);
                var u = new Float32Array(l, h + e, this._floatArray.length);
                var _ = new Int16Array(l, h + e + a, this._frameIntArray.length);
                var c = new Float32Array(l, h + e + a + i, this._frameFloatArray.length);
                var m = new Int16Array(l, h + e + a + i + r, this._frameArray.length);
                var p = new Uint16Array(l, h + e + a + i + r + n, this._timelineArray.length);
                for (var d = 0,
                         y = this._intArray.length; d < y; ++d) {
                    f[d] = this._intArray[d]
                }
                for (var d = 0,
                         y = this._floatArray.length; d < y; ++d) {
                    u[d] = this._floatArray[d]
                }
                for (var d = 0,
                         y = this._frameIntArray.length; d < y; ++d) {
                    _[d] = this._frameIntArray[d]
                }
                for (var d = 0,
                         y = this._frameFloatArray.length; d < y; ++d) {
                    c[d] = this._frameFloatArray[d]
                }
                for (var d = 0,
                         y = this._frameArray.length; d < y; ++d) {
                    m[d] = this._frameArray[d]
                }
                for (var d = 0,
                         y = this._timelineArray.length; d < y; ++d) {
                    p[d] = this._timelineArray[d]
                }
                t.webAssemblyModule.setDataBinary(this._data, h, e, a, i, r, n, s)
            } else {
                var g = new ArrayBuffer(o);
                var f = new Int16Array(g, 0, this._intArray.length);
                var u = new Float32Array(g, e, this._floatArray.length);
                var _ = new Int16Array(g, e + a, this._frameIntArray.length);
                var c = new Float32Array(g, e + a + i, this._frameFloatArray.length);
                var m = new Int16Array(g, e + a + i + r, this._frameArray.length);
                var p = new Uint16Array(g, e + a + i + r + n, this._timelineArray.length);
                for (var d = 0,
                         y = this._intArray.length; d < y; ++d) {
                    f[d] = this._intArray[d]
                }
                for (var d = 0,
                         y = this._floatArray.length; d < y; ++d) {
                    u[d] = this._floatArray[d]
                }
                for (var d = 0,
                         y = this._frameIntArray.length; d < y; ++d) {
                    _[d] = this._frameIntArray[d]
                }
                for (var d = 0,
                         y = this._frameFloatArray.length; d < y; ++d) {
                    c[d] = this._frameFloatArray[d]
                }
                for (var d = 0,
                         y = this._frameArray.length; d < y; ++d) {
                    m[d] = this._frameArray[d]
                }
                for (var d = 0,
                         y = this._timelineArray.length; d < y; ++d) {
                    p[d] = this._timelineArray[d]
                }
                this._data.binary = g;
                this._data.intArray = f;
                this._data.floatArray = u;
                this._data.frameIntArray = _;
                this._data.frameFloatArray = c;
                this._data.frameArray = m;
                this._data.timelineArray = p
            }
            this._defaultColorOffset = -1
        };
        i.prototype.parseDragonBonesData = function(e, a) {
            if (a === void 0) {
                a = 1
            }
            console.assert(e !== null && e !== undefined, "Data error.");
            var r = i._getString(e, t.DataParser.VERSION, "");
            var n = i._getString(e, t.DataParser.COMPATIBLE_VERSION, "");
            if (t.DataParser.DATA_VERSIONS.indexOf(r) >= 0 || t.DataParser.DATA_VERSIONS.indexOf(n) >= 0) {
                var s = t.BaseObject.borrowObject(t.DragonBonesData);
                s.version = r;
                s.name = i._getString(e, t.DataParser.NAME, "");
                s.frameRate = i._getNumber(e, t.DataParser.FRAME_RATE, 24);
                if (s.frameRate === 0) {
                    s.frameRate = 24
                }
                if (t.DataParser.ARMATURE in e) {
                    this._data = s;
                    this._parseArray(e);
                    var o = e[t.DataParser.ARMATURE];
                    for (var l = 0,
                             h = o; l < h.length; l++) {
                        var f = h[l];
                        s.addArmature(this._parseArmature(f, a))
                    }
                    if (!this._data.binary) {
                        this._modifyArray()
                    }
                    if (t.DataParser.STAGE in e) {
                        s.stage = s.getArmature(i._getString(e, t.DataParser.STAGE, ""))
                    } else if (s.armatureNames.length > 0) {
                        s.stage = s.getArmature(s.armatureNames[0])
                    }
                    this._data = null
                }
                if (t.DataParser.TEXTURE_ATLAS in e) {
                    this._rawTextureAtlases = e[t.DataParser.TEXTURE_ATLAS]
                }
                return s
            } else {
                console.assert(false, "Nonsupport data version: " + r + "\n" + "Please convert DragonBones data to support version.\n" + "Read more: https://github.com/DragonBones/Tools/")
            }
            return null
        };
        i.prototype.parseTextureAtlasData = function(e, a, r) {
            if (r === void 0) {
                r = 1
            }
            console.assert(e !== undefined);
            if (e === null) {
                if (this._rawTextureAtlases === null || this._rawTextureAtlases.length === 0) {
                    return false
                }
                var n = this._rawTextureAtlases[this._rawTextureAtlasIndex++];
                this.parseTextureAtlasData(n, a, r);
                if (this._rawTextureAtlasIndex >= this._rawTextureAtlases.length) {
                    this._rawTextureAtlasIndex = 0;
                    this._rawTextureAtlases = null
                }
                return true
            }
            a.width = i._getNumber(e, t.DataParser.WIDTH, 0);
            a.height = i._getNumber(e, t.DataParser.HEIGHT, 0);
            a.scale = r === 1 ? 1 / i._getNumber(e, t.DataParser.SCALE, 1) : r;
            a.name = i._getString(e, t.DataParser.NAME, "");
            a.imagePath = i._getString(e, t.DataParser.IMAGE_PATH, "");
            if (t.DataParser.SUB_TEXTURE in e) {
                var s = e[t.DataParser.SUB_TEXTURE];
                for (var o = 0,
                         l = s.length; o < l; ++o) {
                    var h = s[o];
                    var f = a.createTexture();
                    f.rotated = i._getBoolean(h, t.DataParser.ROTATED, false);
                    f.name = i._getString(h, t.DataParser.NAME, "");
                    f.region.x = i._getNumber(h, t.DataParser.X, 0);
                    f.region.y = i._getNumber(h, t.DataParser.Y, 0);
                    f.region.width = i._getNumber(h, t.DataParser.WIDTH, 0);
                    f.region.height = i._getNumber(h, t.DataParser.HEIGHT, 0);
                    var u = i._getNumber(h, t.DataParser.FRAME_WIDTH, -1);
                    var _ = i._getNumber(h, t.DataParser.FRAME_HEIGHT, -1);
                    if (u > 0 && _ > 0) {
                        f.frame = t.TextureData.createRectangle();
                        f.frame.x = i._getNumber(h, t.DataParser.FRAME_X, 0);
                        f.frame.y = i._getNumber(h, t.DataParser.FRAME_Y, 0);
                        f.frame.width = u;
                        f.frame.height = _
                    }
                    a.addTexture(f)
                }
            }
            return true
        };
        i.getInstance = function() {
            if (i._objectDataParserInstance === null) {
                i._objectDataParserInstance = new i
            }
            return i._objectDataParserInstance
        };
        i._objectDataParserInstance = null;
        return i
    } (t.DataParser);
    t.ObjectDataParser = e;
    var a = function() {
        function t() {
            this.frameStart = 0;
            this.actions = []
        }
        return t
    } ();
    t.ActionFrame = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        a.prototype._inRange = function(t, e, a) {
            return e <= t && t <= a
        };
        a.prototype._decodeUTF8 = function(t) {
            var e = -1;
            var a = -1;
            var i = 65533;
            var r = 0;
            var n = "";
            var s;
            var o = 0;
            var l = 0;
            var h = 0;
            var f = 0;
            while (t.length > r) {
                var u = t[r++];
                if (u === e) {
                    if (l !== 0) {
                        s = i
                    } else {
                        s = a
                    }
                } else {
                    if (l === 0) {
                        if (this._inRange(u, 0, 127)) {
                            s = u
                        } else {
                            if (this._inRange(u, 194, 223)) {
                                l = 1;
                                f = 128;
                                o = u - 192
                            } else if (this._inRange(u, 224, 239)) {
                                l = 2;
                                f = 2048;
                                o = u - 224
                            } else if (this._inRange(u, 240, 244)) {
                                l = 3;
                                f = 65536;
                                o = u - 240
                            } else {}
                            o = o * Math.pow(64, l);
                            s = null
                        }
                    } else if (!this._inRange(u, 128, 191)) {
                        o = 0;
                        l = 0;
                        h = 0;
                        f = 0;
                        r--;
                        s = u
                    } else {
                        h += 1;
                        o = o + (u - 128) * Math.pow(64, l - h);
                        if (h !== l) {
                            s = null
                        } else {
                            var _ = o;
                            var c = f;
                            o = 0;
                            l = 0;
                            h = 0;
                            f = 0;
                            if (this._inRange(_, c, 1114111) && !this._inRange(_, 55296, 57343)) {
                                s = _
                            } else {
                                s = u
                            }
                        }
                    }
                }
                if (s !== null && s !== a) {
                    if (s <= 65535) {
                        if (s > 0) n += String.fromCharCode(s)
                    } else {
                        s -= 65536;
                        n += String.fromCharCode(55296 + (s >> 10 & 1023));
                        n += String.fromCharCode(56320 + (s & 1023))
                    }
                }
            }
            return n
        };
        a.prototype._getUTF16Key = function(t) {
            for (var e = 0,
                     a = t.length; e < a; ++e) {
                if (t.charCodeAt(e) > 255) {
                    return encodeURI(t)
                }
            }
            return t
        };
        a.prototype._parseBinaryTimeline = function(e, a, i) {
            if (i === void 0) {
                i = null
            }
            var r = i !== null ? i: t.BaseObject.borrowObject(t.TimelineData);
            r.type = e;
            r.offset = a;
            this._timeline = r;
            var n = this._timelineArrayBuffer[r.offset + 2];
            if (n === 1) {
                r.frameIndicesOffset = -1
            } else {
                var s = 0;
                var o = this._animation.frameCount + 1;
                var l = this._data.frameIndices;
                if (t.DragonBones.webAssembly) {
                    s = l.size();
                    l.resize(s + o, 0)
                } else {
                    s = l.length;
                    l.length += o
                }
                r.frameIndicesOffset = s;
                for (var h = 0,
                         f = 0,
                         u = 0,
                         _ = 0; h < o; ++h) {
                    if (u + _ <= h && f < n) {
                        u = this._frameArrayBuffer[this._animation.frameOffset + this._timelineArrayBuffer[r.offset + 5 + f]];
                        if (f === n - 1) {
                            _ = this._animation.frameCount - u
                        } else {
                            _ = this._frameArrayBuffer[this._animation.frameOffset + this._timelineArrayBuffer[r.offset + 5 + f + 1]] - u
                        }
                        f++
                    }
                    if (t.DragonBones.webAssembly) {
                        l.set(s + h, f - 1)
                    } else {
                        l[s + h] = f - 1
                    }
                }
            }
            this._timeline = null;
            return r
        };
        a.prototype._parseMesh = function(e, a) {
            a.offset = e[t.DataParser.OFFSET];
            var i = this._intArrayBuffer[a.offset + 3];
            if (i >= 0) {
                var r = t.BaseObject.borrowObject(t.WeightData);
                var n = this._intArrayBuffer[a.offset + 0];
                var s = this._intArrayBuffer[i + 0];
                r.offset = i;
                for (var o = 0; o < s; ++o) {
                    var l = this._intArrayBuffer[i + 2 + o];
                    r.addBone(this._rawBones[l])
                }
                var h = i + 2 + s;
                var f = 0;
                for (var o = 0,
                         u = n; o < u; ++o) {
                    var _ = this._intArrayBuffer[h++];
                    f += _;
                    h += _
                }
                r.count = f;
                a.weight = r
            }
        };
        a.prototype._parseAnimation = function(e) {
            var a = t.BaseObject.borrowObject(t.AnimationData);
            a.frameCount = Math.max(t.ObjectDataParser._getNumber(e, t.DataParser.DURATION, 1), 1);
            a.playTimes = t.ObjectDataParser._getNumber(e, t.DataParser.PLAY_TIMES, 1);
            a.duration = a.frameCount / this._armature.frameRate;
            a.fadeInTime = t.ObjectDataParser._getNumber(e, t.DataParser.FADE_IN_TIME, 0);
            a.scale = t.ObjectDataParser._getNumber(e, t.DataParser.SCALE, 1);
            a.name = t.ObjectDataParser._getString(e, t.DataParser.NAME, t.DataParser.DEFAULT_NAME);
            if (a.name.length === 0) {
                a.name = t.DataParser.DEFAULT_NAME
            }
            var i = e[t.DataParser.OFFSET];
            a.frameIntOffset = i[0];
            a.frameFloatOffset = i[1];
            a.frameOffset = i[2];
            this._animation = a;
            if (t.DataParser.ACTION in e) {
                a.actionTimeline = this._parseBinaryTimeline(0, e[t.DataParser.ACTION])
            }
            if (t.DataParser.Z_ORDER in e) {
                a.zOrderTimeline = this._parseBinaryTimeline(1, e[t.DataParser.Z_ORDER])
            }
            if (t.DataParser.BONE in e) {
                var r = e[t.DataParser.BONE];
                for (var n in r) {
                    var s = r[n];
                    if (t.DragonBones.webAssembly) {
                        n = this._getUTF16Key(n)
                    }
                    var o = this._armature.getBone(n);
                    if (o === null) {
                        continue
                    }
                    for (var l = 0,
                             h = s.length; l < h; l += 2) {
                        var f = s[l];
                        var u = s[l + 1];
                        var _ = this._parseBinaryTimeline(f, u);
                        this._animation.addBoneTimeline(o, _)
                    }
                }
            }
            if (t.DataParser.SURFACE in e) {
                var r = e[t.DataParser.SURFACE];
                for (var n in r) {
                    var s = r[n];
                    if (t.DragonBones.webAssembly) {
                        n = this._getUTF16Key(n)
                    }
                    var c = this._armature.getBone(n);
                    if (c === null) {
                        continue
                    }
                    for (var l = 0,
                             h = s.length; l < h; l += 2) {
                        var f = s[l];
                        var u = s[l + 1];
                        var _ = this._parseBinaryTimeline(f, u);
                        this._animation.addSurfaceTimeline(c, _)
                    }
                }
            }
            if (t.DataParser.SLOT in e) {
                var r = e[t.DataParser.SLOT];
                for (var n in r) {
                    var s = r[n];
                    if (t.DragonBones.webAssembly) {
                        n = this._getUTF16Key(n)
                    }
                    var m = this._armature.getSlot(n);
                    if (m === null) {
                        continue
                    }
                    for (var l = 0,
                             h = s.length; l < h; l += 2) {
                        var f = s[l];
                        var u = s[l + 1];
                        var _ = this._parseBinaryTimeline(f, u);
                        this._animation.addSlotTimeline(m, _)
                    }
                }
            }
            if (t.DataParser.CONSTRAINT in e) {
                var r = e[t.DataParser.CONSTRAINT];
                for (var n in r) {
                    var s = r[n];
                    if (t.DragonBones.webAssembly) {
                        n = this._getUTF16Key(n)
                    }
                    var p = this._armature.getConstraint(n);
                    if (p === null) {
                        continue
                    }
                    for (var l = 0,
                             h = s.length; l < h; l += 2) {
                        var f = s[l];
                        var u = s[l + 1];
                        var _ = this._parseBinaryTimeline(f, u);
                        this._animation.addConstraintTimeline(p, _)
                    }
                }
            }
            if (t.DataParser.ANIMATION in e) {
                var r = e[t.DataParser.ANIMATION];
                for (var n in r) {
                    var s = r[n];
                    if (t.DragonBones.webAssembly) {
                        n = this._getUTF16Key(n)
                    }
                    for (var l = 0,
                             h = s.length; l < h; l += 2) {
                        var f = s[l];
                        var u = s[l + 1];
                        var _ = this._parseBinaryTimeline(f, u);
                        this._animation.addAnimationTimeline(n, _)
                    }
                }
            }
            this._animation = null;
            return a
        };
        a.prototype._parseArray = function(e) {
            var a = e[t.DataParser.OFFSET];
            var i = a[1];
            var r = a[3];
            var n = a[5];
            var s = a[7];
            var o = a[9];
            var l = a[11];
            var h = new Int16Array(this._binary, this._binaryOffset + a[0], i / Int16Array.BYTES_PER_ELEMENT);
            var f = new Float32Array(this._binary, this._binaryOffset + a[2], r / Float32Array.BYTES_PER_ELEMENT);
            var u = new Int16Array(this._binary, this._binaryOffset + a[4], n / Int16Array.BYTES_PER_ELEMENT);
            var _ = new Float32Array(this._binary, this._binaryOffset + a[6], s / Float32Array.BYTES_PER_ELEMENT);
            var c = new Int16Array(this._binary, this._binaryOffset + a[8], o / Int16Array.BYTES_PER_ELEMENT);
            var m = new Uint16Array(this._binary, this._binaryOffset + a[10], l / Uint16Array.BYTES_PER_ELEMENT);
            if (t.DragonBones.webAssembly) {
                var p = i + r + n + s + o + l;
                var d = t.webAssemblyModule._malloc(p);
                var y = new Uint8Array(this._binary, this._binaryOffset, p / Uint8Array.BYTES_PER_ELEMENT);
                var g = new Uint8Array(t.webAssemblyModule.HEAP16.buffer, d, y.length);
                for (var v = 0,
                         b = y.length; v < b; ++v) {
                    g[v] = y[v]
                }
                t.webAssemblyModule.setDataBinary(this._data, d, i, r, n, s, o, l);
                this._intArrayBuffer = h;
                this._floatArrayBuffer = f;
                this._frameIntArrayBuffer = u;
                this._frameFloatArrayBuffer = _;
                this._frameArrayBuffer = c;
                this._timelineArrayBuffer = m
            } else {
                this._data.binary = this._binary;
                this._data.intArray = this._intArrayBuffer = h;
                this._data.floatArray = this._floatArrayBuffer = f;
                this._data.frameIntArray = this._frameIntArrayBuffer = u;
                this._data.frameFloatArray = this._frameFloatArrayBuffer = _;
                this._data.frameArray = this._frameArrayBuffer = c;
                this._data.timelineArray = this._timelineArrayBuffer = m
            }
        };
        a.prototype.parseDragonBonesData = function(t, a) {
            if (a === void 0) {
                a = 1
            }
            console.assert(t !== null && t !== undefined && t instanceof ArrayBuffer, "Data error.");
            var i = new Uint8Array(t, 0, 8);
            if (i[0] !== "D".charCodeAt(0) || i[1] !== "B".charCodeAt(0) || i[2] !== "D".charCodeAt(0) || i[3] !== "T".charCodeAt(0)) {
                console.assert(false, "Nonsupport data.");
                return null
            }
            var r = new Uint32Array(t, 8, 1)[0];
            var n = new Uint8Array(t, 8 + 4, r);
            var s = this._decodeUTF8(n);
            var o = JSON.parse(s);
            this._binaryOffset = 8 + 4 + r;
            this._binary = t;
            return e.prototype.parseDragonBonesData.call(this, o, a)
        };
        a.getInstance = function() {
            if (a._binaryDataParserInstance === null) {
                a._binaryDataParserInstance = new a
            }
            return a._binaryDataParserInstance
        };
        a._binaryDataParserInstance = null;
        return a
    } (t.ObjectDataParser);
    t.BinaryDataParser = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function() {
        function e(a) {
            if (a === void 0) {
                a = null
            }
            this.autoSearch = false;
            this._dragonBonesDataMap = {};
            this._textureAtlasDataMap = {};
            this._dragonBones = null;
            this._dataParser = null;
            if (e._objectParser === null) {
                e._objectParser = new t.ObjectDataParser
            }
            if (e._binaryParser === null) {
                e._binaryParser = new t.BinaryDataParser
            }
            this._dataParser = a !== null ? a: e._objectParser
        }
        e.prototype._isSupportMesh = function() {
            return true
        };
        e.prototype._getTextureData = function(t, e) {
            if (t in this._textureAtlasDataMap) {
                for (var a = 0,
                         i = this._textureAtlasDataMap[t]; a < i.length; a++) {
                    var r = i[a];
                    var n = r.getTexture(e);
                    if (n !== null) {
                        return n
                    }
                }
            }
            if (this.autoSearch) {
                for (var s in this._textureAtlasDataMap) {
                    for (var o = 0,
                             l = this._textureAtlasDataMap[s]; o < l.length; o++) {
                        var r = l[o];
                        if (r.autoSearch) {
                            var n = r.getTexture(e);
                            if (n !== null) {
                                return n
                            }
                        }
                    }
                }
            }
            return null
        };
        e.prototype._fillBuildArmaturePackage = function(t, e, a, i, r) {
            var n = null;
            var s = null;
            if (e.length > 0) {
                if (e in this._dragonBonesDataMap) {
                    n = this._dragonBonesDataMap[e];
                    s = n.getArmature(a)
                }
            }
            if (s === null && (e.length === 0 || this.autoSearch)) {
                for (var o in this._dragonBonesDataMap) {
                    n = this._dragonBonesDataMap[o];
                    if (e.length === 0 || n.autoSearch) {
                        s = n.getArmature(a);
                        if (s !== null) {
                            e = o;
                            break
                        }
                    }
                }
            }
            if (s !== null) {
                t.dataName = e;
                t.textureAtlasName = r;
                t.data = n;
                t.armature = s;
                t.skin = null;
                if (i.length > 0) {
                    t.skin = s.getSkin(i);
                    if (t.skin === null && this.autoSearch) {
                        for (var o in this._dragonBonesDataMap) {
                            var l = this._dragonBonesDataMap[o];
                            var h = l.getArmature(i);
                            if (h !== null) {
                                t.skin = h.defaultSkin;
                                break
                            }
                        }
                    }
                }
                if (t.skin === null) {
                    t.skin = s.defaultSkin
                }
                return true
            }
            return false
        };
        e.prototype._buildBones = function(e, a) {
            for (var i = 0,
                     r = e.armature.sortedBones; i < r.length; i++) {
                var n = r[i];
                var s = t.BaseObject.borrowObject(n.type === 0 ? t.Bone: t.Surface);
                s.init(n);
                if (n.parent !== null) {
                    a.addBone(s, n.parent.name)
                } else {
                    a.addBone(s, "")
                }
            }
            var o = e.armature.constraints;
            for (var l in o) {
                var h = o[l];
                var f = t.BaseObject.borrowObject(t.IKConstraint);
                f.init(h, a);
                a.addConstraint(f)
            }
        };
        e.prototype._buildSlots = function(e, a) {
            var i = e.skin;
            var r = e.armature.defaultSkin;
            if (i === null || r === null) {
                return
            }
            var n = {};
            for (var s in r.displays) {
                var o = r.getDisplays(s);
                n[s] = o
            }
            if (i !== r) {
                for (var s in i.displays) {
                    var o = i.getDisplays(s);
                    n[s] = o
                }
            }
            for (var l = 0,
                     h = e.armature.sortedSlots; l < h.length; l++) {
                var f = h[l];
                var o = f.name in n ? n[f.name] : null;
                var u = this._buildSlot(e, f, o, a);
                a.addSlot(u, f.parent.name);
                if (o !== null) {
                    var _ = new Array;
                    for (var c = 0,
                             m = t.DragonBones.webAssembly ? o.size() : o.length; c < m; ++c) {
                        var p = t.DragonBones.webAssembly ? o.get(c) : o[c];
                        if (p !== null) {
                            _.push(this._getSlotDisplay(e, p, null, u))
                        } else {
                            _.push(null)
                        }
                    }
                    u._setDisplayList(_)
                }
                u._setDisplayIndex(f.displayIndex, true)
            }
        };
        e.prototype._buildChildArmature = function(t, e, a) {
            e;
            return this.buildArmature(a.path, t !== null ? t.dataName: "", "", t !== null ? t.textureAtlasName: "")
        };
        e.prototype._getSlotDisplay = function(e, a, i, r) {
            var n = e !== null ? e.dataName: a.parent.parent.parent.name;
            var s = null;
            switch (a.type) {
                case 0:
                {
                    var o = a;
                    if (o.texture === null) {
                        o.texture = this._getTextureData(n, a.path)
                    } else if (e !== null && e.textureAtlasName.length > 0) {
                        o.texture = this._getTextureData(e.textureAtlasName, a.path)
                    }
                    if (i !== null && i.type === 2 && this._isSupportMesh()) {
                        s = r.meshDisplay
                    } else {
                        s = r.rawDisplay
                    }
                    break
                }
                case 2:
                {
                    var l = a;
                    if (l.texture === null) {
                        l.texture = this._getTextureData(n, l.path)
                    } else if (e !== null && e.textureAtlasName.length > 0) {
                        l.texture = this._getTextureData(e.textureAtlasName, l.path)
                    }
                    if (this._isSupportMesh()) {
                        s = r.meshDisplay
                    } else {
                        s = r.rawDisplay
                    }
                    break
                }
                case 1:
                {
                    var h = a;
                    var f = this._buildChildArmature(e, r, a);
                    if (f !== null) {
                        f.inheritAnimation = h.inheritAnimation;
                        if (!f.inheritAnimation) {
                            var u = h.actions.length > 0 ? h.actions: f.armatureData.defaultActions;
                            if (u.length > 0) {
                                for (var _ = 0,
                                         c = u; _ < c.length; _++) {
                                    var m = c[_];
                                    var p = t.BaseObject.borrowObject(t.EventObject);
                                    t.EventObject.actionDataToInstance(m, p, r.armature);
                                    p.slot = r;
                                    r.armature._bufferAction(p, false)
                                }
                            } else {
                                f.animation.play()
                            }
                        }
                        h.armature = f.armatureData
                    }
                    s = f;
                    break
                }
                case 3:
                    break;
                default:
                    break
            }
            return s
        };
        e.prototype.parseDragonBonesData = function(t, a, i) {
            if (a === void 0) {
                a = null
            }
            if (i === void 0) {
                i = 1
            }
            var r = t instanceof ArrayBuffer ? e._binaryParser: this._dataParser;
            var n = r.parseDragonBonesData(t, i);
            while (true) {
                var s = this._buildTextureAtlasData(null, null);
                if (r.parseTextureAtlasData(null, s, i)) {
                    this.addTextureAtlasData(s, a)
                } else {
                    s.returnToPool();
                    break
                }
            }
            if (n !== null) {
                this.addDragonBonesData(n, a)
            }
            return n
        };
        e.prototype.parseTextureAtlasData = function(t, e, a, i) {
            if (a === void 0) {
                a = null
            }
            if (i === void 0) {
                i = 1
            }
            var r = this._buildTextureAtlasData(null, null);
            this._dataParser.parseTextureAtlasData(t, r, i);
            this._buildTextureAtlasData(r, e || null);
            this.addTextureAtlasData(r, a);
            return r
        };
        e.prototype.updateTextureAtlasData = function(t, e) {
            var a = this.getTextureAtlasData(t);
            if (a !== null) {
                for (var i = 0,
                         r = a.length; i < r; ++i) {
                    if (i < e.length) {
                        this._buildTextureAtlasData(a[i], e[i])
                    }
                }
            }
        };
        e.prototype.getDragonBonesData = function(t) {
            return t in this._dragonBonesDataMap ? this._dragonBonesDataMap[t] : null
        };
        e.prototype.addDragonBonesData = function(t, e) {
            if (e === void 0) {
                e = null
            }
            e = e !== null ? e: t.name;
            if (e in this._dragonBonesDataMap) {
                if (this._dragonBonesDataMap[e] === t) {
                    return
                }
                console.warn("Can not add same name data: " + e);
                return
            }
            this._dragonBonesDataMap[e] = t
        };
        e.prototype.removeDragonBonesData = function(t, e) {
            if (e === void 0) {
                e = true
            }
            if (t in this._dragonBonesDataMap) {
                if (e) {
                    this._dragonBones.bufferObject(this._dragonBonesDataMap[t])
                }
                delete this._dragonBonesDataMap[t]
            }
        };
        e.prototype.getTextureAtlasData = function(t) {
            return t in this._textureAtlasDataMap ? this._textureAtlasDataMap[t] : null
        };
        e.prototype.addTextureAtlasData = function(t, e) {
            if (e === void 0) {
                e = null
            }
            e = e !== null ? e: t.name;
            var a = e in this._textureAtlasDataMap ? this._textureAtlasDataMap[e] : this._textureAtlasDataMap[e] = [];
            if (a.indexOf(t) < 0) {
                a.push(t)
            }
        };
        e.prototype.removeTextureAtlasData = function(t, e) {
            if (e === void 0) {
                e = true
            }
            if (t in this._textureAtlasDataMap) {
                var a = this._textureAtlasDataMap[t];
                if (e) {
                    for (var i = 0,
                             r = a; i < r.length; i++) {
                        var n = r[i];
                        this._dragonBones.bufferObject(n)
                    }
                }
                delete this._textureAtlasDataMap[t]
            }
        };
        e.prototype.getArmatureData = function(t, e) {
            if (e === void 0) {
                e = ""
            }
            var i = new a;
            if (!this._fillBuildArmaturePackage(i, e, t, "", "")) {
                return null
            }
            return i.armature
        };
        e.prototype.clear = function(t) {
            if (t === void 0) {
                t = true
            }
            for (var e in this._dragonBonesDataMap) {
                if (t) {
                    this._dragonBones.bufferObject(this._dragonBonesDataMap[e])
                }
                delete this._dragonBonesDataMap[e]
            }
            for (var e in this._textureAtlasDataMap) {
                if (t) {
                    var a = this._textureAtlasDataMap[e];
                    for (var i = 0,
                             r = a; i < r.length; i++) {
                        var n = r[i];
                        this._dragonBones.bufferObject(n)
                    }
                }
                delete this._textureAtlasDataMap[e]
            }
        };
        e.prototype.buildArmature = function(t, e, i, r) {
            if (e === void 0) {
                e = ""
            }
            if (i === void 0) {
                i = ""
            }
            if (r === void 0) {
                r = ""
            }
            var n = new a;
            if (!this._fillBuildArmaturePackage(n, e || "", t, i || "", r || "")) {
                console.warn("No armature data: " + t + ", " + (e !== null ? e: ""));
                return null
            }
            var s = this._buildArmature(n);
            this._buildBones(n, s);
            this._buildSlots(n, s);
            s.invalidUpdate(null, true);
            s.advanceTime(0);
            return s
        };
        e.prototype.replaceDisplay = function(e, a, i) {
            if (i === void 0) {
                i = -1
            }
            if (i < 0) {
                i = e.displayIndex
            }
            if (i < 0) {
                i = 0
            }
            e.replaceDisplayData(a, i);
            var r = e.displayList;
            if (r.length <= i) {
                r.length = i + 1;
                for (var n = 0,
                         s = r.length; n < s; ++n) {
                    if (!r[n]) {
                        r[n] = null
                    }
                }
            }
            if (a !== null) {
                var o = e.rawDisplayDatas;
                var l = null;
                if (o) {
                    if (t.DragonBones.webAssembly) {
                        if (i < o.size()) {
                            l = o.get(i)
                        }
                    } else {
                        if (i < o.length) {
                            l = o[i]
                        }
                    }
                }
                r[i] = this._getSlotDisplay(null, a, l, e)
            } else {
                r[i] = null
            }
            e.displayList = r
        };
        e.prototype.replaceSlotDisplay = function(t, e, a, i, r, n) {
            if (n === void 0) {
                n = -1
            }
            var s = this.getArmatureData(e, t || "");
            if (!s || !s.defaultSkin) {
                return false
            }
            var o = s.defaultSkin.getDisplay(a, i);
            if (!o) {
                return false
            }
            this.replaceDisplay(r, o, n);
            return true
        };
        e.prototype.replaceSlotDisplayList = function(e, a, i, r) {
            var n = this.getArmatureData(a, e || "");
            if (!n || !n.defaultSkin) {
                return false
            }
            var s = n.defaultSkin.getDisplays(i);
            if (!s) {
                return false
            }
            var o = 0;
            for (var l = 0,
                     h = t.DragonBones.webAssembly ? s.size() : s.length; l < h; ++l) {
                var f = t.DragonBones.webAssembly ? s.get(l) : s[l];
                this.replaceDisplay(r, f, o++)
            }
            return true
        };
        e.prototype.replaceSkin = function(e, a, i, r) {
            if (i === void 0) {
                i = false
            }
            if (r === void 0) {
                r = null
            }
            var n = false;
            var s = a.parent.defaultSkin;
            for (var o = 0,
                     l = e.getSlots(); o < l.length; o++) {
                var h = l[o];
                if (r !== null && r.indexOf(h.name) >= 0) {
                    continue
                }
                var f = a.getDisplays(h.name);
                if (!f) {
                    if (s !== null && a !== s) {
                        f = s.getDisplays(h.name)
                    }
                    if (!f) {
                        if (i) {
                            h.rawDisplayDatas = null;
                            h.displayList = []
                        }
                        continue
                    }
                }
                var u = t.DragonBones.webAssembly ? f.size() : f.length;
                var _ = h.displayList;
                _.length = u;
                for (var c = 0,
                         m = u; c < m; ++c) {
                    var p = t.DragonBones.webAssembly ? f.get(c) : f[c];
                    if (p !== null) {
                        _[c] = this._getSlotDisplay(null, p, null, h)
                    } else {
                        _[c] = null
                    }
                }
                n = true;
                h.rawDisplayDatas = f;
                h.displayList = _
            }
            return n
        };
        e.prototype.replaceAnimation = function(e, a, i) {
            if (i === void 0) {
                i = true
            }
            var r = a.defaultSkin;
            if (r === null) {
                return false
            }
            if (i) {
                e.animation.animations = a.animations
            } else {
                var n = e.animation.animations;
                var s = {};
                for (var o in n) {
                    s[o] = n[o]
                }
                for (var o in a.animations) {
                    s[o] = a.animations[o]
                }
                e.animation.animations = s
            }
            for (var l = 0,
                     h = e.getSlots(); l < h.length; l++) {
                var f = h[l];
                var u = 0;
                for (var _ = 0,
                         c = f.displayList; _ < c.length; _++) {
                    var m = c[_];
                    if (m instanceof t.Armature) {
                        var p = r.getDisplays(f.name);
                        if (p !== null && u < (t.DragonBones.webAssembly ? p.size() : p.length)) {
                            var d = t.DragonBones.webAssembly ? p.get(u) : p[u];
                            if (d !== null && d.type === 1) {
                                var y = this.getArmatureData(d.path, d.parent.parent.parent.name);
                                if (y) {
                                    this.replaceAnimation(m, y, i)
                                }
                            }
                        }
                    }
                    u++
                }
            }
            return true
        };
        e.prototype.getAllDragonBonesData = function() {
            return this._dragonBonesDataMap
        };
        e.prototype.getAllTextureAtlasData = function() {
            return this._textureAtlasDataMap
        };
        Object.defineProperty(e.prototype, "clock", {
            get: function() {
                return this._dragonBones.clock
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(e.prototype, "dragonBones", {
            get: function() {
                return this._dragonBones
            },
            enumerable: true,
            configurable: true
        });
        e.prototype.changeSkin = function(t, e, a) {
            if (a === void 0) {
                a = null
            }
            return this.replaceSkin(t, e, false, a)
        };
        e.prototype.copyAnimationsToArmature = function(t, e, a, i, r) {
            if (a === void 0) {
                a = ""
            }
            if (i === void 0) {
                i = ""
            }
            if (r === void 0) {
                r = true
            }
            a;
            var n = this.getArmatureData(e, i);
            if (!n) {
                return false
            }
            return this.replaceAnimation(t, n, r)
        };
        e._objectParser = null;
        e._binaryParser = null;
        return e
    } ();
    t.BaseFactory = e;
    var a = function() {
        function t() {
            this.dataName = "";
            this.textureAtlasName = "";
            this.skin = null
        }
        return t
    } ();
    t.BuildArmaturePackage = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(i, e);
        function i() {
            var t = e !== null && e.apply(this, arguments) || this;
            t._renderTexture = null;
            return t
        }
        i.toString = function() {
            return "[class dragonBones.EgretTextureAtlasData]"
        };
        i.prototype._onClear = function() {
            e.prototype._onClear.call(this);
            if (this.disposeEnabled && this._renderTexture !== null) {
                this._renderTexture.dispose()
            }
            this.disposeEnabled = false;
            this._renderTexture = null
        };
        i.prototype.createTexture = function() {
            return t.BaseObject.borrowObject(a)
        };
        Object.defineProperty(i.prototype, "renderTexture", {
            get: function() {
                return this._renderTexture
            },
            set: function(t) {
                if (this._renderTexture === t) {
                    return
                }
                this._renderTexture = t;
                if (this._renderTexture !== null) {
                    var e = this._renderTexture.bitmapData;
                    var a = this.width > 0 ? this.width: e.width;
                    var i = this.height > 0 ? this.height: e.height;
                    for (var r in this.textures) {
                        var n = egret.$TextureScaleFactor;
                        var s = this.textures[r];
                        var o = s.region.width;
                        var l = s.region.height;
                        if (s.renderTexture === null) {
                            s.renderTexture = new egret.Texture
                        }
                        s.renderTexture.bitmapData = e;
                        if (s.rotated) {
                            s.renderTexture.$initData(s.region.x * n, s.region.y * n, l * n, o * n, 0, 0, l * n, o * n, a, i, s.rotated)
                        } else {
                            s.renderTexture.$initData(s.region.x * n, s.region.y * n, o * n, l * n, 0, 0, o * n, l * n, a, i)
                        }
                    }
                } else {
                    for (var r in this.textures) {
                        var s = this.textures[r];
                        s.renderTexture = null
                    }
                }
            },
            enumerable: true,
            configurable: true
        });
        i.prototype.dispose = function() {
            console.warn("已废弃。");
            this.returnToPool()
        };
        Object.defineProperty(i.prototype, "texture", {
            get: function() {
                console.warn("已废弃。");
                return this.renderTexture
            },
            enumerable: true,
            configurable: true
        });
        return i
    } (t.TextureAtlasData);
    t.EgretTextureAtlasData = e;
    var a = function(t) {
        __extends(e, t);
        function e() {
            var e = t !== null && t.apply(this, arguments) || this;
            e.renderTexture = null;
            return e
        }
        e.toString = function() {
            return "[class dragonBones.EgretTextureData]"
        };
        e.prototype._onClear = function() {
            t.prototype._onClear.call(this);
            if (this.renderTexture !== null) {}
            this.renderTexture = null
        };
        return e
    } (t.TextureData);
    t.EgretTextureData = a
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            return e !== null && e.apply(this, arguments) || this
        }
        Object.defineProperty(a.prototype, "eventObject", {
            get: function() {
                return this.data
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animationName", {
            get: function() {
                var t = this.eventObject.animationState;
                return t !== null ? t.name: ""
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "armature", {
            get: function() {
                return this.eventObject.armature
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "bone", {
            get: function() {
                return this.eventObject.bone
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "slot", {
            get: function() {
                return this.eventObject.slot
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "animationState", {
            get: function() {
                return this.eventObject.animationState
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "frameLabel", {
            get: function() {
                return this.eventObject.name
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "sound", {
            get: function() {
                return this.eventObject.name
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a.prototype, "movementID", {
            get: function() {
                return this.animationName
            },
            enumerable: true,
            configurable: true
        });
        a.START = t.EventObject.START;
        a.LOOP_COMPLETE = t.EventObject.LOOP_COMPLETE;
        a.COMPLETE = t.EventObject.COMPLETE;
        a.FADE_IN = t.EventObject.FADE_IN;
        a.FADE_IN_COMPLETE = t.EventObject.FADE_IN_COMPLETE;
        a.FADE_OUT = t.EventObject.FADE_OUT;
        a.FADE_OUT_COMPLETE = t.EventObject.FADE_OUT_COMPLETE;
        a.FRAME_EVENT = t.EventObject.FRAME_EVENT;
        a.SOUND_EVENT = t.EventObject.SOUND_EVENT;
        a.ANIMATION_FRAME_EVENT = t.EventObject.FRAME_EVENT;
        a.BONE_FRAME_EVENT = t.EventObject.FRAME_EVENT;
        a.MOVEMENT_FRAME_EVENT = t.EventObject.FRAME_EVENT;
        a.SOUND = t.EventObject.SOUND_EVENT;
        return a
    } (egret.Event);
    t.EgretEvent = e;
    var a = function(a) {
        __extends(i, a);
        function i() {
            var t = a !== null && a.apply(this, arguments) || this;
            t.debugDraw = false;
            t._batchEnabled = !(global["nativeRender"] || global["bricks"]);
            t._childDirty = true;
            t._debugDraw = false;
            t._armature = null;
            t._bounds = null;
            t._debugDrawer = null;
            return t
        }
        i._cleanBeforeRender = function() {};
        i.prototype.dbInit = function(t) {
            this._armature = t;
            if (this._batchEnabled) {
                this.$renderNode = new egret.sys.GroupNode;
                this.$renderNode.cleanBeforeRender = i._cleanBeforeRender
            }
        };
        i.prototype.dbClear = function() {
            this._armature = null;
            this._bounds = null;
            this._debugDrawer = null
        };
        i.prototype.dbUpdate = function() {
            var e = t.DragonBones.debugDraw || this.debugDraw;
            if (e || this._debugDraw) {
                this._debugDraw = e;
                if (this._debugDraw) {
                    if (this._debugDrawer === null) {
                        this._debugDrawer = new egret.Sprite
                    }
                    if (this._debugDrawer.parent !== this) {
                        this.addChild(this._debugDrawer)
                    }
                    var a = 2;
                    var i = this._debugDrawer.graphics;
                    i.clear();
                    for (var r = 0,
                             n = this._armature.getBones(); r < n.length; r++) {
                        var s = n[r];
                        if (s.boneData.type === 0) {
                            var o = Math.max(s.boneData.length, a);
                            var l = s.globalTransformMatrix.tx;
                            var h = s.globalTransformMatrix.ty;
                            var f = l - s.globalTransformMatrix.a * a;
                            var u = h - s.globalTransformMatrix.b * a;
                            var _ = l + s.globalTransformMatrix.a * o;
                            var c = h + s.globalTransformMatrix.b * o;
                            var m = l + u - h;
                            var p = h + f - l;
                            var d = l - u + h;
                            var y = h - f + l;
                            i.lineStyle(2, 65535, .7);
                            i.moveTo(f, u);
                            i.lineTo(_, c);
                            i.moveTo(m, p);
                            i.lineTo(d, y)
                        } else {
                            var g = s;
                            var v = g._boneData;
                            var b = v.segmentX;
                            var D = v.segmentY;
                            var T = g._vertices;
                            i.lineStyle(2, 16776960, .7);
                            for (var A = 0; A < D; ++A) {
                                for (var x = 0; x < b; ++x) {
                                    var P = (x + A * (b + 1)) * 2;
                                    var O = T[P];
                                    var S = T[P + 1];
                                    i.moveTo(O, S);
                                    i.lineTo(T[P + 2], T[P + 3]);
                                    i.moveTo(O, S);
                                    i.lineTo(T[P + (b + 1) * 2], T[P + (b + 1) * 2 + 1]);
                                    if (x === b - 1) {
                                        i.moveTo(T[P + 2], T[P + 3]);
                                        i.lineTo(T[P + (b + 2) * 2], T[P + (b + 2) * 2 + 1])
                                    }
                                    if (A === D - 1) {
                                        i.moveTo(T[P + (b + 1) * 2], T[P + (b + 1) * 2 + 1]);
                                        i.lineTo(T[P + (b + 2) * 2], T[P + (b + 2) * 2 + 1])
                                    }
                                }
                            }
                        }
                    }
                    for (var E = 0,
                             B = this._armature.getSlots(); E < B.length; E++) {
                        var M = B[E];
                        var w = M.boundingBoxData;
                        if (w !== null) {
                            var C = this._debugDrawer.getChildByName(M.name);
                            if (C === null) {
                                C = new egret.Shape;
                                C.name = M.name;
                                this._debugDrawer.addChild(C)
                            }
                            C.graphics.clear();
                            C.graphics.lineStyle(2, 16711935, .7);
                            switch (w.type) {
                                case 0:
                                    C.graphics.drawRect( - w.width * .5, -w.height * .5, w.width, w.height);
                                    break;
                                case 1:
                                    C.graphics.drawEllipse( - w.width * .5, -w.height * .5, w.width, w.height);
                                    break;
                                case 2:
                                    var T = w.vertices;
                                    for (var I = 0; I < T.length; I += 2) {
                                        var O = T[I];
                                        var S = T[I + 1];
                                        if (I === 0) {
                                            C.graphics.moveTo(O, S)
                                        } else {
                                            C.graphics.lineTo(O, S)
                                        }
                                    }
                                    C.graphics.lineTo(T[0], T[1]);
                                    break;
                                default:
                                    break
                            }
                            M.updateTransformAndMatrix();
                            M.updateGlobalTransform();
                            C.$setMatrix(M.globalTransformMatrix, false)
                        } else {
                            var C = this._debugDrawer.getChildByName(M.name);
                            if (C !== null) {
                                this._debugDrawer.removeChild(C)
                            }
                        }
                    }
                } else if (this._debugDrawer !== null && this._debugDrawer.parent === this) {
                    this.removeChild(this._debugDrawer)
                }
            }
            if (!t.isV5 && this._batchEnabled && this._childDirty) {
                this.$invalidateContentBounds()
            }
        };
        i.prototype.dispose = function(t) {
            if (t === void 0) {
                t = true
            }
            t;
            if (this._armature !== null) {
                this._armature.dispose();
                this._armature = null
            }
        };
        i.prototype.dispatchDBEvent = function(t, i) {
            var r = egret.Event.create(e, t);
            r.data = i;
            a.prototype.dispatchEvent.call(this, r);
            egret.Event.release(r)
        };
        i.prototype.hasDBEventListener = function(t) {
            return this.hasEventListener(t)
        };
        i.prototype.addDBEventListener = function(t, e, a) {
            this.addEventListener(t, e, a)
        };
        i.prototype.removeDBEventListener = function(t, e, a) {
            this.removeEventListener(t, e, a)
        };
        i.prototype.disableBatch = function() {
            if (!this._batchEnabled || !this._armature) {
                return
            }
            for (var t = 0,
                     e = this._armature.getSlots(); t < e.length; t++) {
                var a = e[t];
                var i = a._meshData ? a.meshDisplay: a.rawDisplay;
                if (!a.display && i === a.meshDisplay) {
                    i = a.rawDisplay
                }
                var r = i.$renderNode;
                if (r.matrix) {
                    i.$setMatrix(a.globalTransformMatrix, false)
                }
                this.addChild(i)
            }
            this._batchEnabled = false;
            this.$renderNode.cleanBeforeRender = null;
            this.$renderNode = null;
            this.armature.invalidUpdate(null, true)
        };
        Object.defineProperty(i.prototype, "armature", {
            get: function() {
                return this._armature
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(i.prototype, "animation", {
            get: function() {
                return this._armature.animation
            },
            enumerable: true,
            configurable: true
        });
        i.prototype.$measureContentBounds = function(e) {
            if (this._batchEnabled && this._armature) {
                if (this._childDirty) {
                    this._childDirty = false;
                    var i = true;
                    var r = new egret.Rectangle;
                    for (var n = 0,
                             s = this._armature.getSlots(); n < s.length; n++) {
                        var o = s[n];
                        var l = o.display;
                        if (!l || !l.$renderNode || !l.$renderNode.image) {
                            continue
                        }
                        var h = l.$renderNode.matrix;
                        if (l === o.meshDisplay) {
                            var f = l.$renderNode.vertices;
                            if (f && f.length > 0) {
                                r.setTo(999999, 999999, -999999, -999999);
                                for (var u = 0,
                                         _ = f.length; u < _; u += 2) {
                                    var c = f[u];
                                    var m = f[u + 1];
                                    if (r.x > c) r.x = c;
                                    if (r.width < c) r.width = c;
                                    if (r.y > m) r.y = m;
                                    if (r.height < m) r.height = m
                                }
                                r.width -= r.x;
                                r.height -= r.y
                            } else {
                                continue
                            }
                        } else {
                            var p = o._displayDatas[o.displayIndex];
                            if (p && p instanceof t.ImageDisplayData && p.texture) {
                                var d = p.texture.parent.scale;
                                r.x = 0;
                                r.y = 0;
                                r.width = p.texture.region.width * d;
                                r.height = p.texture.region.height * d
                            } else {
                                continue
                            }
                        }
                        h.$transformBounds(r);
                        var y = r.x;
                        var g = r.y;
                        var v = r.x + r.width;
                        var b = r.y + r.height;
                        if (i) {
                            i = false;
                            e.x = y;
                            e.y = g;
                            e.width = v;
                            e.height = b
                        } else {
                            if (y < e.x) {
                                e.x = y
                            }
                            if (g < e.y) {
                                e.y = g
                            }
                            if (v > e.width) {
                                e.width = v
                            }
                            if (b > e.height) {
                                e.height = b
                            }
                        }
                    }
                    e.width -= e.x;
                    e.height -= e.y;
                    if (t.isV5) {
                        if (this._bounds === null) {
                            this._bounds = new egret.Rectangle
                        }
                        this._bounds.copyFrom(e)
                    }
                } else if (t.isV5) {
                    if (this._bounds === null) {
                        this._bounds = new egret.Rectangle
                    }
                    e.copyFrom(this._bounds)
                }
                return e
            }
            return a.prototype.$measureContentBounds.call(this, e)
        };
        i.prototype.hasEvent = function(t) {
            return this.hasDBEventListener(t)
        };
        i.prototype.addEvent = function(t, e, a) {
            this.addDBEventListener(t, e, a)
        };
        i.prototype.removeEvent = function(t, e, a) {
            this.removeDBEventListener(t, e, a)
        };
        i.prototype.advanceTimeBySelf = function(e) {
            if (e) {
                this._armature.clock = t.EgretFactory.factory.clock
            } else {
                this._armature.clock = null
            }
        };
        return i
    } (egret.DisplayObjectContainer);
    t.EgretArmatureDisplay = a;
    var i = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        return e
    } (e);
    t.Event = i;
    var r = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        return e
    } (e);
    t.ArmatureEvent = r;
    var n = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        return e
    } (e);
    t.AnimationEvent = n;
    var s = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        return e
    } (e);
    t.FrameEvent = s;
    var o = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        return e
    } (e);
    t.SoundEvent = o;
    var l = function(e) {
        __extends(a, e);
        function a(a, i, r) {
            if (r === void 0) {
                r = 1
            }
            var n = e.call(this) || this;
            console.warn("已废弃");
            n._onClear();
            t.ObjectDataParser.getInstance().parseTextureAtlasData(i, n, r);
            n.renderTexture = a;
            return n
        }
        a.toString = function() {
            return "[class dragonBones.EgretTextureAtlas]"
        };
        return a
    } (t.EgretTextureAtlasData);
    t.EgretTextureAtlas = l;
    var h = function(t) {
        __extends(e, t);
        function e() {
            return t !== null && t.apply(this, arguments) || this
        }
        return e
    } (l);
    t.EgretSheetAtlas = h;
    var f = function() {
        function e() {}
        e.getInstance = function() {
            console.warn("已废弃");
            return t.EgretFactory.factory.soundEventManager
        };
        return e
    } ();
    t.SoundEventManager = f;
    var u = function() {
        function t() {
            console.warn("已废弃")
        }
        return t
    } ();
    t.AnimationCacheManager = u
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = function(e) {
        __extends(a, e);
        function a() {
            var t = e !== null && e.apply(this, arguments) || this;
            t.transformUpdateEnabled = false;
            t._armatureDisplay = null;
            t._renderDisplay = null;
            t._colorFilter = null;
            return t
        }
        a.toString = function() {
            return "[class dragonBones.EgretSlot]"
        };
        a.prototype.init = function(a, i, r, n) {
            e.prototype.init.call(this, a, i, r, n);
            if (t.isV5) {
                this._updateTransform = this._updateTransformV5
            } else {
                this._updateTransform = this._updateTransformV4
            }
        };
        a.prototype._onClear = function() {
            e.prototype._onClear.call(this);
            this._armatureDisplay = null;
            this._renderDisplay = null;
            this._colorFilter = null
        };
        a.prototype._initDisplay = function(t, e) {
            t;
            e
        };
        a.prototype._disposeDisplay = function(t, e) {
            t;
            e
        };
        a.prototype._onUpdateDisplay = function() {
            this._armatureDisplay = this._armature.display;
            this._renderDisplay = this._display !== null ? this._display: this._rawDisplay;
            if (t.isV5 && this._armatureDisplay._batchEnabled) {
                if (this._renderDisplay === this._rawDisplay && !(this._renderDisplay.$renderNode instanceof egret.sys.BitmapNode)) {
                    this._renderDisplay.$renderNode = new egret.sys.BitmapNode
                }
            }
            if (this._armatureDisplay._batchEnabled) {
                if (this._renderDisplay !== this._rawDisplay && this._renderDisplay !== this._meshDisplay) {
                    this._armatureDisplay.disableBatch()
                } else {
                    var e = this._renderDisplay.$renderNode;
                    if (!e.matrix) {
                        e.matrix = new egret.Matrix
                    }
                }
            }
        };
        a.prototype._addDisplay = function() {
            if (this._armatureDisplay._batchEnabled) {
                this._armatureDisplay.$renderNode.addNode(this._renderDisplay.$renderNode)
            } else {
                this._armatureDisplay.addChild(this._renderDisplay)
            }
        };
        a.prototype._replaceDisplay = function(t) {
            var e = t;
            if (this._armatureDisplay._batchEnabled) {
                var a = this._armatureDisplay.$renderNode.drawData;
                a[a.indexOf(e.$renderNode)] = this._renderDisplay.$renderNode
            } else {
                this._armatureDisplay.addChild(this._renderDisplay);
                this._armatureDisplay.swapChildren(this._renderDisplay, e);
                this._armatureDisplay.removeChild(e)
            }
        };
        a.prototype._removeDisplay = function() {
            if (this._armatureDisplay._batchEnabled) {
                var t = this._armatureDisplay.$renderNode.drawData;
                t.splice(t.indexOf(this._renderDisplay.$renderNode), 1)
            } else {
                this._renderDisplay.parent.removeChild(this._renderDisplay)
            }
        };
        a.prototype._updateZOrder = function() {
            if (this._armatureDisplay._batchEnabled) {
                var t = this._armatureDisplay.$renderNode.drawData;
                t[this._zOrder] = this._renderDisplay.$renderNode
            } else {
                var e = this._armatureDisplay.getChildIndex(this._renderDisplay);
                if (e === this._zOrder) {
                    return
                }
                this._armatureDisplay.addChildAt(this._renderDisplay, this._zOrder)
            }
        };
        a.prototype._updateVisible = function() {
            var t = this._parent.visible && this._visible;
            if (this._armatureDisplay._batchEnabled) {
                var e = this._renderDisplay.$renderNode;
                e.alpha = t ? 1 : 0
            } else {
                this._renderDisplay.visible = t
            }
        };
        a.prototype._updateBlendMode = function() {
            switch (this._blendMode) {
                case 0:
                    this._renderDisplay.blendMode = egret.BlendMode.NORMAL;
                    break;
                case 1:
                    this._renderDisplay.blendMode = egret.BlendMode.ADD;
                    break;
                case 5:
                    this._renderDisplay.blendMode = egret.BlendMode.ERASE;
                    break;
                default:
                    break
            }
            if (this._armatureDisplay._batchEnabled) {
                var t = this._renderDisplay.$renderNode;
                t.blendMode = egret.sys.blendModeToNumber(this._renderDisplay.blendMode)
            }
        };
        a.prototype._updateColor = function() {
            if (this._colorTransform.redMultiplier !== 1 || this._colorTransform.greenMultiplier !== 1 || this._colorTransform.blueMultiplier !== 1 || this._colorTransform.redOffset !== 0 || this._colorTransform.greenOffset !== 0 || this._colorTransform.blueOffset !== 0 || this._colorTransform.alphaOffset !== 0) {
                if (this._colorFilter === null) {
                    this._colorFilter = new egret.ColorMatrixFilter
                }
                var t = this._colorFilter.matrix;
                t[0] = this._colorTransform.redMultiplier;
                t[6] = this._colorTransform.greenMultiplier;
                t[12] = this._colorTransform.blueMultiplier;
                t[18] = this._colorTransform.alphaMultiplier;
                t[4] = this._colorTransform.redOffset;
                t[9] = this._colorTransform.greenOffset;
                t[14] = this._colorTransform.blueOffset;
                t[19] = this._colorTransform.alphaOffset;
                this._colorFilter.matrix = t;
                if (this._armatureDisplay._batchEnabled) {
                    var e = this._renderDisplay.$renderNode;
                    e.filter = this._colorFilter;
                    e.alpha = 1
                }
                var a = this._renderDisplay.filters;
                if (!a) {
                    a = []
                }
                if (a.indexOf(this._colorFilter) < 0) {
                    a.push(this._colorFilter)
                }
                this._renderDisplay.filters = a;
                this._renderDisplay.alpha = 1
            } else {
                if (this._armatureDisplay._batchEnabled) {
                    var e = this._renderDisplay.$renderNode;
                    e.filter = null;
                    e.alpha = this._colorTransform.alphaMultiplier
                }
                this._renderDisplay.filters = null;
                this._renderDisplay.alpha = this._colorTransform.alphaMultiplier
            }
        };
        a.prototype._updateFrame = function() {
            var e = this._display === this._meshDisplay ? this._meshData: null;
            var a = this._textureData;
            if (this._displayIndex >= 0 && this._display !== null && a !== null) {
                if (this._armature.replacedTexture !== null && this._rawDisplayDatas !== null && this._rawDisplayDatas.indexOf(this._displayData) >= 0) {
                    var i = a.parent;
                    if (this._armature._replaceTextureAtlasData === null) {
                        i = t.BaseObject.borrowObject(t.EgretTextureAtlasData);
                        i.copyFrom(a.parent);
                        i.renderTexture = this._armature.replacedTexture;
                        this._armature._replaceTextureAtlasData = i
                    } else {
                        i = this._armature._replaceTextureAtlasData
                    }
                    a = i.getTexture(a.name)
                }
                if (a.renderTexture !== null) {
                    if (e !== null) {
                        var r = e.parent.parent.parent;
                        var n = r.intArray;
                        var s = r.floatArray;
                        var o = n[e.offset + 0];
                        var l = n[e.offset + 1];
                        var h = n[e.offset + 2];
                        if (h < 0) {
                            h += 65536
                        }
                        var f = h + o * 2;
                        var u = this._armature._armatureData.scale;
                        var _ = this._renderDisplay;
                        var c = _.$renderNode;
                        c.uvs.length = o * 2;
                        c.vertices.length = o * 2;
                        c.indices.length = l * 3;
                        for (var m = 0,
                                 p = o * 2; m < p; ++m) {
                            c.vertices[m] = s[h + m] * u;
                            c.uvs[m] = s[f + m]
                        }
                        for (var m = 0; m < l * 3; ++m) {
                            c.indices[m] = n[e.offset + 4 + m]
                        }
                        if (this._armatureDisplay._batchEnabled) {
                            var d = a.renderTexture;
                            var y = this._renderDisplay.$renderNode;
                            egret.sys.RenderNode.prototype.cleanBeforeRender.call(y);
                            y.image = d.bitmapData;
                            if (t.isV5) {
                                y.drawMesh(d.$bitmapX, d.$bitmapY, d.$bitmapWidth, d.$bitmapHeight, d.$offsetX, d.$offsetY, d.textureWidth, d.textureHeight);
                                y.imageWidth = d.$sourceWidth;
                                y.imageHeight = d.$sourceHeight
                            } else {
                                y.drawMesh(d._bitmapX, d._bitmapY, d._bitmapWidth, d._bitmapHeight, d._offsetX, d._offsetY, d.textureWidth, d.textureHeight);
                                y.imageWidth = d._sourceWidth;
                                y.imageHeight = d._sourceHeight
                            }
                            this._blendModeDirty = true;
                            this._colorDirty = true
                        }
                        _.texture = a.renderTexture;
                        _.anchorOffsetX = this._pivotX;
                        _.anchorOffsetY = this._pivotY;
                        _.$updateVertices();
                        if (!t.isV5) {
                            _.$invalidateTransform()
                        }
                        var g = this._meshData.weight !== null;
                        var v = this._parent._boneData.type !== 0;
                        if (g || v) {
                            this._identityTransform()
                        }
                    } else {
                        var u = a.parent.scale * this._armature._armatureData.scale;
                        var b = (a.rotated ? a.region.height: a.region.width) * u;
                        var D = (a.rotated ? a.region.width: a.region.height) * u;
                        var T = this._renderDisplay;
                        var d = a.renderTexture;
                        T.texture = d;
                        if (this._armatureDisplay._batchEnabled) {
                            var y = this._renderDisplay.$renderNode;
                            egret.sys.RenderNode.prototype.cleanBeforeRender.call(y);
                            y.image = d.bitmapData;
                            if (t.isV5) {
                                y.drawImage(d.$bitmapX, d.$bitmapY, d.$bitmapWidth, d.$bitmapHeight, d.$offsetX, d.$offsetY, b, D);
                                y.imageWidth = d.$sourceWidth;
                                y.imageHeight = d.$sourceHeight
                            } else {
                                y.drawImage(d._bitmapX, d._bitmapY, d._bitmapWidth, d._bitmapHeight, d._offsetX, d._offsetY, b, D);
                                y.imageWidth = d._sourceWidth;
                                y.imageHeight = d._sourceHeight
                            }
                            this._blendModeDirty = true;
                            this._colorDirty = true
                        } else {
                            T.width = b;
                            T.height = D
                        }
                        T.anchorOffsetX = this._pivotX;
                        T.anchorOffsetY = this._pivotY
                    }
                    this._visibleDirty = true;
                    return
                }
            }
            if (this._armatureDisplay._batchEnabled) {
                this._renderDisplay.$renderNode.image = null
            }
            var A = this._renderDisplay;
            A.texture = null;
            A.x = 0;
            A.y = 0;
            A.visible = false
        };
        a.prototype._updateMesh = function() {
            var e = this._armature._armatureData.scale;
            var a = this._meshData;
            var i = this._deformVertices.length > 0 && a.inheritDeform;
            var r = a.weight;
            var n = this._renderDisplay;
            var s = n.$renderNode;
            if (r !== null) {
                var o = a.parent.parent.parent;
                var l = o.intArray;
                var h = o.floatArray;
                var f = l[a.offset + 0];
                var u = l[r.offset + 1];
                if (u < 0) {
                    u += 65536
                }
                for (var _ = 0,
                         c = 0,
                         m = r.offset + 2 + r.bones.length,
                         p = u,
                         d = 0; _ < f; ++_) {
                    var y = l[m++];
                    var g = 0,
                        v = 0;
                    for (var b = 0; b < y; ++b) {
                        var D = l[m++];
                        var T = this._meshBones[D];
                        if (T !== null) {
                            var A = T.globalTransformMatrix;
                            var x = h[p++];
                            var P = h[p++] * e;
                            var O = h[p++] * e;
                            if (i) {
                                P += this._deformVertices[d++];
                                O += this._deformVertices[d++]
                            }
                            g += (A.a * P + A.c * O + A.tx) * x;
                            v += (A.b * P + A.d * O + A.ty) * x
                        }
                    }
                    s.vertices[c++] = g;
                    s.vertices[c++] = v
                }
                n.$updateVertices();
                if (!t.isV5) {
                    n.$invalidateTransform()
                }
            } else if (i) {
                var S = this._parent._boneData.type !== 0;
                var o = a.parent.parent.parent;
                var l = o.intArray;
                var h = o.floatArray;
                var f = l[a.offset + 0];
                var E = l[a.offset + 2];
                if (E < 0) {
                    E += 65536
                }
                for (var _ = 0,
                         B = f * 2; _ < B; _ += 2) {
                    var M = h[E + _] * e + this._deformVertices[_];
                    var w = h[E + _ + 1] * e + this._deformVertices[_ + 1];
                    if (S) {
                        var A = this._parent._getGlobalTransformMatrix(M, w);
                        s.vertices[_] = A.a * M + A.c * w + A.tx;
                        s.vertices[_ + 1] = A.b * M + A.d * w + A.ty
                    } else {
                        s.vertices[_] = M;
                        s.vertices[_ + 1] = w
                    }
                }
                n.$updateVertices();
                if (!t.isV5) {
                    n.$invalidateTransform()
                }
            }
            if (this._armatureDisplay._batchEnabled) {
                this._armatureDisplay._childDirty = true
            }
        };
        a.prototype._updateGlueMesh = function() {
            var e = this._meshData.glue;
            var a = e.weights;
            var i = e.meshes;
            var r = this._renderDisplay;
            var n = r.$renderNode.vertices;
            for (var s = 0,
                     o = a.length; s < o; ++s) {
                var l = a[s];
                var h = a[s++];
                var f = 1;
                var u = 0;
                var _ = 0;
                for (var c = 0; c < h; ++c) {
                    var m = a[s++];
                    var p = a[s++] * 2;
                    var d = a[s++];
                    var y = this._meshSlots[m];
                    var g = i[m];
                    f -= d;
                    if (y !== null && g !== null && y._meshData !== null && y._meshData.offset === g.offset) {
                        var v = y.display.$renderNode.vertices;
                        u += v[p] * d;
                        _ += v[p + 1] * d
                    }
                }
                n[l] = u + n[l] * f;
                n[l + 1] = _ + n[l + 1] * f
            }
            r.$updateVertices();
            if (!t.isV5) {
                r.$invalidateTransform()
            }
        };
        a.prototype._updateTransform = function() {
            throw new Error
        };
        a.prototype._identityTransform = function() {
            if (this._armatureDisplay._batchEnabled) {
                this._armatureDisplay._childDirty = true;
                var t = this._renderDisplay.$renderNode.matrix;
                t.a = 1;
                t.b = 0;
                t.c = 0;
                t.d = 1;
                t.tx = 0;
                t.ty = 0
            } else {
                egret.$TempMatrix.identity();
                this._renderDisplay.$setMatrix(egret.$TempMatrix, this.transformUpdateEnabled)
            }
        };
        a.prototype._updateTransformV4 = function() {
            var t = this.globalTransformMatrix;
            if (this._armatureDisplay._batchEnabled) {
                this._armatureDisplay._childDirty = true;
                var e = this._renderDisplay.$renderNode.matrix;
                e.a = t.a;
                e.b = t.b;
                e.c = t.c;
                e.d = t.d;
                e.tx = this.globalTransformMatrix.tx - (this.globalTransformMatrix.a * this._pivotX + this.globalTransformMatrix.c * this._pivotY);
                e.ty = this.globalTransformMatrix.ty - (this.globalTransformMatrix.b * this._pivotX + this.globalTransformMatrix.d * this._pivotY)
            } else if (this.transformUpdateEnabled) {
                this._renderDisplay.$setMatrix(t, true)
            } else {
                var a = this._renderDisplay.$DisplayObject;
                var e = a[6];
                e.a = this.globalTransformMatrix.a;
                e.b = this.globalTransformMatrix.b;
                e.c = this.globalTransformMatrix.c;
                e.d = this.globalTransformMatrix.d;
                e.tx = this.globalTransformMatrix.tx;
                e.ty = this.globalTransformMatrix.ty;
                this._renderDisplay.$removeFlags(8);
                this._renderDisplay.$invalidatePosition()
            }
        };
        a.prototype._updateTransformV5 = function() {
            var t = this.globalTransformMatrix;
            if (this._armatureDisplay._batchEnabled) {
                this._armatureDisplay._childDirty = true;
                var e = this._renderDisplay.$renderNode.matrix;
                e.a = t.a;
                e.b = t.b;
                e.c = t.c;
                e.d = t.d;
                e.tx = this.globalTransformMatrix.tx - (this.globalTransformMatrix.a * this._pivotX + this.globalTransformMatrix.c * this._pivotY);
                e.ty = this.globalTransformMatrix.ty - (this.globalTransformMatrix.b * this._pivotX + this.globalTransformMatrix.d * this._pivotY)
            } else {
                this._renderDisplay.$setMatrix(t, this.transformUpdateEnabled)
            }
        };
        return a
    } (t.Slot);
    t.EgretSlot = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    t.isV5 = Number(egret.Capabilities.engineVersion.substr(0, 3)) >= 5.1;
    var e = function(e) {
        __extends(a, e);
        function a(i) {
            if (i === void 0) {
                i = null
            }
            var r = e.call(this, i) || this;
            if (a._dragonBonesInstance === null) {
                var n = new t.EgretArmatureDisplay;
                a._dragonBonesInstance = new t.DragonBones(n);
                a._time = egret.getTimer() * .001;
                egret.startTick(a._clockHandler, a)
            }
            r._dragonBones = a._dragonBonesInstance;
            return r
        }
        a._clockHandler = function(t) {
            t *= .001;
            var e = t - this._time;
            a._dragonBonesInstance.advanceTime(e);
            this._time = t;
            return false
        };
        Object.defineProperty(a, "factory", {
            get: function() {
                if (a._factory === null) {
                    a._factory = new a
                }
                return a._factory
            },
            enumerable: true,
            configurable: true
        });
        a.prototype._isSupportMesh = function() {
            if (egret.Capabilities.renderMode === "webgl" || egret.Capabilities.runtimeType === egret.RuntimeType.NATIVE) {
                return true
            }
            console.warn("Canvas can not support mesh, please change renderMode to webgl.");
            return false
        };
        a.prototype._buildTextureAtlasData = function(e, a) {
            if (e !== null) {
                if (a instanceof egret.Texture) {
                    e.renderTexture = a
                } else {
                    var i = new egret.Texture;
                    i.bitmapData = new egret.BitmapData(a);
                    e.disposeEnabled = true;
                    e.renderTexture = i
                }
            } else {
                e = t.BaseObject.borrowObject(t.EgretTextureAtlasData)
            }
            return e
        };
        a.prototype._buildArmature = function(e) {
            var a = t.BaseObject.borrowObject(t.Armature);
            var i = new t.EgretArmatureDisplay;
            a.init(e.armature, i, i, this._dragonBones);
            return a
        };
        a.prototype._buildSlot = function(e, a, i, r) {
            e;
            r;
            var n = t.BaseObject.borrowObject(t.EgretSlot);
            n.init(a, i, new egret.Bitmap, new egret.Mesh);
            return n
        };
        a.prototype.buildArmatureDisplay = function(t, e, a, i) {
            if (e === void 0) {
                e = ""
            }
            if (a === void 0) {
                a = ""
            }
            if (i === void 0) {
                i = ""
            }
            var r = this.buildArmature(t, e || "", a || "", i || "");
            if (r !== null) {
                this._dragonBones.clock.add(r);
                return r.display
            }
            return null
        };
        a.prototype.getTextureDisplay = function(t, e) {
            if (e === void 0) {
                e = null
            }
            var a = this._getTextureData(e !== null ? e: "", t);
            if (a !== null && a.renderTexture !== null) {
                var i = a.renderTexture;
                var r = new egret.Bitmap(i);
                r.width = i.textureWidth * a.parent.scale;
                r.height = i.textureHeight * a.parent.scale;
                return r
            }
            return null
        };
        Object.defineProperty(a.prototype, "soundEventManager", {
            get: function() {
                return this._dragonBones.eventManager
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(a, "clock", {
            get: function() {
                return a.factory.clock
            },
            enumerable: true,
            configurable: true
        });
        a.prototype.addSkeletonData = function(t, e) {
            if (e === void 0) {
                e = null
            }
            console.warn("已废弃");
            this.addDragonBonesData(t, e)
        };
        a.prototype.getSkeletonData = function(t) {
            console.warn("已废弃");
            return this.getDragonBonesData(t)
        };
        a.prototype.removeSkeletonData = function(t) {
            console.warn("已废弃");
            this.removeDragonBonesData(t)
        };
        a.prototype.addTextureAtlas = function(t, e) {
            if (e === void 0) {
                e = null
            }
            console.warn("已废弃");
            this.addTextureAtlasData(t, e)
        };
        a.prototype.getTextureAtlas = function(t) {
            console.warn("已废弃");
            return this.getTextureAtlasData(t)
        };
        a.prototype.removeTextureAtlas = function(t) {
            console.warn("已废弃");
            this.removeTextureAtlasData(t)
        };
        a.prototype.buildFastArmature = function(t, e, a) {
            if (e === void 0) {
                e = ""
            }
            if (a === void 0) {
                a = ""
            }
            console.warn("已废弃");
            return this.buildArmature(t, e || "", a || "")
        };
        a.prototype.dispose = function() {
            console.warn("已废弃");
            this.clear()
        };
        a._time = 0;
        a._dragonBonesInstance = null;
        a._factory = null;
        return a
    } (t.BaseFactory);
    t.EgretFactory = e
})(dragonBones || (dragonBones = {}));
var dragonBones; (function(t) {
    var e = new egret.Rectangle;
    var a = new egret.Matrix;
    var i = {};
    function r(t, e) {
        for (var a = 0,
                 i = t.length; a < i; ++a) {
            var r = t[a];
            if (r.name === e) {
                return r
            }
        }
        return null
    }
    function n(t) {
        if (t.groupName) {
            var e = i[t.groupName];
            if (e) {
                var a = r(e.movie || e.animation, t.movieName);
                if (a) {
                    t.groupConfig = e;
                    t.movieConfig = a;
                    return true
                }
            }
        }
        if (!t.groupName) {
            for (var n in i) {
                var e = i[n];
                if (!t.groupName) {
                    var a = r(e.movie || e.animation, t.movieName);
                    if (a) {
                        t.groupName = n;
                        t.groupConfig = e;
                        t.movieConfig = a;
                        return true
                    }
                }
            }
        }
        return false
    }
    function s(t) {
        return t in i
    }
    t.hasMovieGroup = s;
    function o(t, e, a) {
        if (a === void 0) {
            a = null
        }
        if (t) {
            var r = new egret.ByteArray(t);
            r.endian = egret.Endian.LITTLE_ENDIAN;
            r.position = 8;
            var n = JSON.parse(r.readUTF());
            n.offset = r.position;
            n.arrayBuffer = t;
            n.textures = [];
            var s = n.offset % 4;
            if (s) {
                n.offset += 4 - s
            }
            for (var o = 0,
                     l = n.position.length; o < l; o += 3) {
                switch (o / 3) {
                    case 1:
                        n.displayFrameArray = new Int16Array(n.arrayBuffer, n.offset + n.position[o], n.position[o + 1] / n.position[o + 2]);
                        break;
                    case 2:
                        n.rectangleArray = new Float32Array(n.arrayBuffer, n.offset + n.position[o], n.position[o + 1] / n.position[o + 2]);
                        break;
                    case 3:
                        n.transformArray = new Float32Array(n.arrayBuffer, n.offset + n.position[o], n.position[o + 1] / n.position[o + 2]);
                        break;
                    case 4:
                        n.colorArray = new Int16Array(n.arrayBuffer, n.offset + n.position[o], n.position[o + 1] / n.position[o + 2]);
                        break
                }
            }
            a = a || n.name;
            if (i[a]) {
                console.warn("Replace group: " + a)
            }
            i[a] = n;
            if (e instanceof Array) {
                for (var o = 0,
                         l = e.length; o < l; ++o) {
                    var h = e[o];
                    n.textures.push(h)
                }
            } else {
                n.textures.push(e)
            }
        } else {
            throw new Error
        }
    }
    t.addMovieGroup = o;
    function l(t) {
        var e = i[t];
        if (e) {
            delete i[t]
        }
    }
    t.removeMovieGroup = l;
    function h() {
        for (var t in i) {
            delete i[t]
        }
    }
    t.removeAllMovieGroup = h;
    function f(e, a) {
        if (a === void 0) {
            a = null
        }
        var i = {
            movieName: e,
            groupName: a
        };
        if (n(i)) {
            var r = new m(i);
            r.clock = t.EgretFactory.factory.clock;
            return r
        } else {
            console.warn("No movie named: " + e)
        }
        return null
    }
    t.buildMovie = f;
    function u(t) {
        var e = i[t];
        if (e) {
            var a = [];
            var r = e.movie || e.animation;
            for (var n = 0,
                     s = r.length; n < s; ++n) {
                a.push(r[n].name)
            }
            return a
        } else {
            console.warn("No group named: " + t)
        }
        return null
    }
    t.getMovieNames = u;
    var _ = function(t) {
        __extends(e, t);
        function e(e) {
            var a = t.call(this, e) || this;
            a.name = "";
            a.slotName = "";
            a.clipName = "";
            return a
        }
        Object.defineProperty(e.prototype, "armature", {
            get: function() {
                return this.movie
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(e.prototype, "bone", {
            get: function() {
                return null
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(e.prototype, "animationState", {
            get: function() {
                return {
                    name: this.clipName
                }
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(e.prototype, "frameLabel", {
            get: function() {
                return this.name
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(e.prototype, "movementID", {
            get: function() {
                return this.clipName
            },
            enumerable: true,
            configurable: true
        });
        e.START = "start";
        e.LOOP_COMPLETE = "loopComplete";
        e.COMPLETE = "complete";
        e.FRAME_EVENT = "frameEvent";
        e.SOUND_EVENT = "soundEvent";
        return e
    } (egret.Event);
    t.MovieEvent = _;
    var c = function(t) {
        __extends(e, t);
        function e(e) {
            var a = t.call(this) || this;
            a.displayIndex = -1;
            a.colorIndex = -1;
            a.transformIndex = -1;
            a.rawDisplay = new egret.Bitmap;
            a.childMovies = {};
            a.displayConfig = null;
            a.childMovie = null;
            a.colorFilter = null;
            a.display = a.rawDisplay;
            a.config = e;
            a.rawDisplay.name = a.config.name;
            if (!a.config.blendMode) {
                a.config.blendMode = 0
            }
            return a
        }
        e.prototype.dispose = function() {
            this.rawDisplay = null;
            this.childMovies = null;
            this.config = null;
            this.displayConfig = null;
            this.display = null;
            this.childMovie = null;
            this.colorFilter = null
        };
        return e
    } (egret.HashObject);
    var m = function(i) {
        __extends(r, i);
        function r(t) {
            var e = i.call(this) || this;
            e.timeScale = 1;
            e.clipTimeScale = 1;
            e._batchEnabled = true;
            e._isLockDispose = false;
            e._isDelayDispose = false;
            e._isStarted = false;
            e._isPlaying = false;
            e._isReversing = false;
            e._isCompleted = false;
            e._playTimes = 0;
            e._time = 0;
            e._currentTime = 0;
            e._currentPlayTimes = 0;
            e._cacheFrameIndex = 0;
            e._frameSize = 0;
            e._cacheRectangle = null;
            e._clock = null;
            e._currentFrameConfig = null;
            e._clipNames = [];
            e._slots = [];
            e._childMovies = [];
            e._groupConfig = t.groupConfig;
            e._config = t.movieConfig;
            e._batchEnabled = !(e._config.isNested || e._config.hasChildAnimation);
            if (e._batchEnabled) {
                e.$renderNode = new egret.sys.GroupNode;
                e.$renderNode.cleanBeforeRender = r._cleanBeforeRender
            }
            e._clipNames.length = 0;
            for (var a = 0,
                     n = e._config.clip.length; a < n; ++a) {
                e._clipNames.push(e._config.clip[a].name)
            }
            for (var a = 0,
                     n = e._config.slot.length; a < n; ++a) {
                var s = new c(e._config.slot[a]);
                e._updateSlotBlendMode(s);
                e._slots.push(s);
                if (e._batchEnabled) {
                    e.$renderNode.addNode(s.rawDisplay.$renderNode)
                } else {
                    e.addChild(s.rawDisplay)
                }
            }
            e._frameSize = (1 + 1) * e._slots.length;
            e.name = e._config.name;
            e.play();
            e.advanceTime(1e-6);
            e.stop();
            return e
        }
        r._cleanBeforeRender = function() {};
        r.prototype._configToEvent = function(t, e) {
            e.movie = this;
            e.clipName = this._clipConfig.name;
            e.name = t.name;
            e.slotName = t.slot || ""
        };
        r.prototype._onCrossFrame = function(e) {
            for (var a = 0,
                     i = e.actionAndEvent.length; a < i; ++a) {
                var r = e.actionAndEvent[a];
                if (r) {
                    switch (r.type) {
                        case 11:
                            if (t.EgretFactory.factory.soundEventManager.hasEventListener(_.SOUND_EVENT)) {
                                var n = egret.Event.create(_, _.SOUND_EVENT);
                                this._configToEvent(r, n);
                                t.EgretFactory.factory.soundEventManager.dispatchEvent(n);
                                egret.Event.release(n)
                            }
                            break;
                        case 10:
                            if (this.hasEventListener(_.FRAME_EVENT)) {
                                var s = egret.Event.create(_, _.FRAME_EVENT);
                                this._configToEvent(r, s);
                                this.dispatchEvent(s);
                                egret.Event.release(s)
                            }
                            break;
                        case 0:
                            if (r.slot) {
                                var o = this._getSlot(r.slot);
                                if (o && o.childMovie) {
                                    o.childMovie.play(r.name)
                                }
                            } else {
                                this.play(r.name)
                            }
                            break
                    }
                }
            }
        };
        r.prototype._updateSlotBlendMode = function(t) {
            var e = "";
            switch (t.config.blendMode) {
                case 0:
                    e = egret.BlendMode.NORMAL;
                    break;
                case 1:
                    e = egret.BlendMode.ADD;
                    break;
                case 5:
                    e = egret.BlendMode.ERASE;
                    break;
                default:
                    break
            }
            if (e) {
                if (this._batchEnabled) {
                    t.display.$renderNode.blendMode = egret.sys.blendModeToNumber(e)
                } else {
                    t.display.blendMode = e
                }
            }
        };
        r.prototype._updateSlotColor = function(t, e, a, i, r, n, s, o, l) {
            if (a !== 1 || i !== 1 || r !== 1 || s !== 0 || o !== 0 || l !== 0 || n !== 0) {
                if (!t.colorFilter) {
                    t.colorFilter = new egret.ColorMatrixFilter
                }
                var h = t.colorFilter.matrix;
                h[0] = a;
                h[6] = i;
                h[12] = r;
                h[18] = e;
                h[4] = s;
                h[9] = o;
                h[14] = l;
                h[19] = n;
                t.colorFilter.matrix = h;
                if (this._batchEnabled) {
                    t.display.$renderNode.filter = t.colorFilter;
                    t.display.$renderNode.alpha = 1
                } else {
                    var f = t.display.filters;
                    if (!f) {
                        f = []
                    }
                    if (f.indexOf(t.colorFilter) < 0) {
                        f.push(t.colorFilter)
                    }
                    t.display.filters = f;
                    t.display.$setAlpha(1)
                }
            } else {
                if (t.colorFilter) {
                    t.colorFilter = null
                }
                if (this._batchEnabled) {
                    t.display.$renderNode.filter = null;
                    t.display.$renderNode.alpha = e
                } else {
                    t.display.filters = null;
                    t.display.$setAlpha(e)
                }
            }
        };
        r.prototype._updateSlotDisplay = function(e) {
            var a = e.display || e.rawDisplay;
            var i = e.childMovie;
            if (e.displayIndex >= 0) {
                e.displayConfig = this._groupConfig.display[e.displayIndex];
                if (e.displayConfig.type === 1) {
                    var r = e.displayConfig.name in e.childMovies ? e.childMovies[e.displayConfig.name] : null;
                    if (!r) {
                        r = f(e.displayConfig.name, this._groupConfig.name);
                        if (r) {
                            e.childMovies[e.displayConfig.name] = r
                        }
                    }
                    if (r) {
                        e.display = r;
                        e.childMovie = r
                    } else {
                        e.display = e.rawDisplay;
                        e.childMovie = null
                    }
                } else {
                    e.display = e.rawDisplay;
                    e.childMovie = null
                }
            } else {
                e.displayConfig = null;
                e.display = e.rawDisplay;
                e.childMovie = null
            }
            if (e.display !== a) {
                if (a) {
                    this.addChild(e.display);
                    this.swapChildren(e.display, a);
                    this.removeChild(a)
                }
                this._updateSlotBlendMode(e)
            }
            if (e.display === e.rawDisplay) {
                if (e.displayConfig && e.displayConfig.regionIndex !== null && e.displayConfig.regionIndex !== undefined) {
                    if (!e.displayConfig.texture) {
                        var n = this._groupConfig.textures[e.displayConfig.textureIndex || 0];
                        var s = e.displayConfig.regionIndex * 4;
                        var o = this._groupConfig.rectangleArray[s];
                        var l = this._groupConfig.rectangleArray[s + 1];
                        var h = this._groupConfig.rectangleArray[s + 2];
                        var u = this._groupConfig.rectangleArray[s + 3];
                        e.displayConfig.texture = new egret.Texture;
                        e.displayConfig.texture.bitmapData = n._bitmapData;
                        e.displayConfig.texture.$initData(o, l, Math.min(h, n.textureWidth - o), Math.min(u, n.textureHeight - l), 0, 0, Math.min(h, n.textureWidth - o), Math.min(u, n.textureHeight - l), n.textureWidth, n.textureHeight)
                    }
                    if (this._batchEnabled) {
                        var _ = e.displayConfig.texture;
                        var c = e.rawDisplay.$renderNode;
                        egret.sys.RenderNode.prototype.cleanBeforeRender.call(e.rawDisplay.$renderNode);
                        c.image = _.bitmapData;
                        if (t.isV5) {
                            c.drawImage(_.$bitmapX, _.$bitmapY, _.$bitmapWidth, _.$bitmapHeight, _.$offsetX, _.$offsetY, _.textureWidth, _.textureHeight);
                            c.imageWidth = _._sourceWidth;
                            c.imageHeight = _._sourceHeight
                        } else {
                            c.drawImage(_._bitmapX, _._bitmapY, _._bitmapWidth, _._bitmapHeight, _._offsetX, _._offsetY, _.textureWidth, _.textureHeight);
                            c.imageWidth = _._sourceWidth;
                            c.imageHeight = _._sourceHeight
                        }
                    } else {
                        e.rawDisplay.visible = true;
                        e.rawDisplay.$setBitmapData(e.displayConfig.texture)
                    }
                } else {
                    if (this._batchEnabled) {
                        e.rawDisplay.$renderNode.image = null
                    } else {
                        e.rawDisplay.visible = false;
                        e.rawDisplay.$setBitmapData(null)
                    }
                }
            }
            if (e.childMovie !== i) {
                if (i) {
                    i.stop();
                    this._childMovies.slice(this._childMovies.indexOf(i), 1)
                }
                if (e.childMovie) {
                    if (this._childMovies.indexOf(e.childMovie) < 0) {
                        this._childMovies.push(e.childMovie)
                    }
                    if (e.config.action) {
                        e.childMovie.play(e.config.action)
                    } else {
                        e.childMovie.play(e.childMovie._config.action)
                    }
                }
            }
        };
        r.prototype._getSlot = function(t) {
            for (var e = 0,
                     a = this._slots.length; e < a; ++e) {
                var i = this._slots[e];
                if (i.config.name === t) {
                    return i
                }
            }
            return null
        };
        r.prototype.$render = function() {
            if (this._batchEnabled) {} else {
                i.prototype.$render.call(this)
            }
        };
        r.prototype.$measureContentBounds = function(t) {
            if (this._batchEnabled && this._cacheRectangle) {
                t.setTo(this._cacheRectangle.x, this._cacheRectangle.y, this._cacheRectangle.width - this._cacheRectangle.x, this._cacheRectangle.height - this._cacheRectangle.y)
            } else {
                i.prototype.$measureContentBounds.call(this, t)
            }
        };
        r.prototype.$doAddChild = function(t, e, a) {
            if (this._batchEnabled) {
                console.warn("Can not add child.");
                return null
            }
            return i.prototype.$doAddChild.call(this, t, e, a)
        };
        r.prototype.$doRemoveChild = function(t, e) {
            if (this._batchEnabled) {
                console.warn("Can not remove child.");
                return null
            }
            return i.prototype.$doRemoveChild.call(this, t, e)
        };
        r.prototype.dispose = function() {
            if (this._isLockDispose) {
                this._isDelayDispose = true
            } else {
                if (this._clock) {
                    this._clock.remove(this)
                }
                if (this._slots) {
                    for (var t = 0,
                             e = this._slots.length; t < e; ++t) {
                        this._slots[t].dispose()
                    }
                }
                this._isPlaying = false;
                this._cacheRectangle = null;
                this._clock = null;
                this._groupConfig = null;
                this._config = null;
                this._clipConfig = null;
                this._currentFrameConfig = null;
                this._clipArray = null;
                this._clipNames = null;
                this._slots = null;
                this._childMovies = null
            }
        };
        r.prototype.advanceTime = function(i) {
            if (this._isPlaying) {
                this._isLockDispose = true;
                if (i < 0) {
                    i = -i
                }
                i *= this.timeScale;
                this._time += i * this.clipTimeScale;
                var r = this._clipConfig.duration;
                var n = r * this._playTimes;
                var s = this._time;
                var o = this._currentPlayTimes;
                if (this._playTimes > 0 && (s >= n || s <= -n)) {
                    this._isCompleted = true;
                    o = this._playTimes;
                    if (s < 0) {
                        s = 0
                    } else {
                        s = r
                    }
                } else {
                    this._isCompleted = false;
                    if (s < 0) {
                        o = Math.floor( - s / r);
                        s = r - -s % r
                    } else {
                        o = Math.floor(s / r);
                        s %= r
                    }
                    if (this._playTimes > 0 && o > this._playTimes) {
                        o = this._playTimes
                    }
                }
                if (this._currentTime === s) {
                    return
                }
                var l = Math.floor(s * this._clipConfig.cacheTimeToFrameScale);
                if (this._cacheFrameIndex !== l) {
                    this._cacheFrameIndex = l;
                    var h = this._groupConfig.displayFrameArray;
                    var f = this._groupConfig.transformArray;
                    var u = this._groupConfig.colorArray;
                    var c = true;
                    var m = false;
                    var p = false;
                    var d = this._cacheRectangle;
                    this._cacheRectangle = this._clipConfig.cacheRectangles[this._cacheFrameIndex];
                    if (this._batchEnabled && !this._cacheRectangle) {
                        p = true;
                        this._cacheRectangle = new egret.Rectangle;
                        this._clipConfig.cacheRectangles[this._cacheFrameIndex] = this._cacheRectangle
                    }
                    for (var y = 0,
                             g = this._slots.length; y < g; ++y) {
                        var v = this._slots[y];
                        var b = this._frameSize * this._cacheFrameIndex + y * 2;
                        if (b >= this._clipArray.length) {
                            b = this._frameSize * (this._cacheFrameIndex - 1) + y * 2
                        }
                        var D = this._clipArray[b] * 2;
                        if (D >= 0) {
                            var T = h[D];
                            var A = h[D + 1] * 8;
                            var x = this._clipArray[b + 1] * 6;
                            var P = false;
                            if (v.displayIndex !== T) {
                                v.displayIndex = T;
                                P = true;
                                this._updateSlotDisplay(v)
                            }
                            if (v.colorIndex !== A || P) {
                                v.colorIndex = A;
                                if (v.colorIndex >= 0) {
                                    this._updateSlotColor(v, u[A] * .01, u[A + 1] * .01, u[A + 2] * .01, u[A + 3] * .01, u[A + 4], u[A + 5], u[A + 6], u[A + 7])
                                } else {
                                    this._updateSlotColor(v, 1, 1, 1, 1, 0, 0, 0, 0)
                                }
                            }
                            m = true;
                            if (v.transformIndex !== x) {
                                v.transformIndex = x;
                                if (this._batchEnabled) {
                                    var O = v.display.$renderNode.matrix;
                                    if (!O) {
                                        O = v.display.$renderNode.matrix = new egret.Matrix
                                    }
                                    O.a = f[x];
                                    O.b = f[x + 1];
                                    O.c = f[x + 2];
                                    O.d = f[x + 3];
                                    O.tx = f[x + 4];
                                    O.ty = f[x + 5]
                                } else {
                                    a.a = f[x];
                                    a.b = f[x + 1];
                                    a.c = f[x + 2];
                                    a.d = f[x + 3];
                                    a.tx = f[x + 4];
                                    a.ty = f[x + 5];
                                    v.display.$setMatrix(a)
                                }
                            }
                            if (this._batchEnabled && p && v.displayConfig) {
                                var O = v.display.$renderNode.matrix;
                                e.x = 0;
                                e.y = 0;
                                e.width = v.displayConfig.texture.textureWidth;
                                e.height = v.displayConfig.texture.textureHeight;
                                O.$transformBounds(e);
                                if (c) {
                                    c = false;
                                    this._cacheRectangle.x = e.x;
                                    this._cacheRectangle.width = e.x + e.width;
                                    this._cacheRectangle.y = e.y;
                                    this._cacheRectangle.height = e.y + e.height
                                } else {
                                    this._cacheRectangle.x = Math.min(this._cacheRectangle.x, e.x);
                                    this._cacheRectangle.width = Math.max(this._cacheRectangle.width, e.x + e.width);
                                    this._cacheRectangle.y = Math.min(this._cacheRectangle.y, e.y);
                                    this._cacheRectangle.height = Math.max(this._cacheRectangle.height, e.y + e.height)
                                }
                            }
                        } else if (v.displayIndex !== -1) {
                            v.displayIndex = -1;
                            this._updateSlotDisplay(v)
                        }
                    }
                    if (this._cacheRectangle) {
                        if (m && p && c && d) {
                            this._cacheRectangle.x = d.x;
                            this._cacheRectangle.y = d.y;
                            this._cacheRectangle.width = d.width;
                            this._cacheRectangle.height = d.height
                        }
                        if (!t.isV5) {
                            this.$invalidateContentBounds()
                        }
                    }
                }
                if (this._isCompleted) {
                    this._isPlaying = false
                }
                if (!this._isStarted) {
                    this._isStarted = true;
                    if (this.hasEventListener(_.START)) {
                        var S = egret.Event.create(_, _.START);
                        S.movie = this;
                        S.clipName = this._clipConfig.name;
                        S.name = "";
                        S.slotName = "";
                        this.dispatchEvent(S)
                    }
                }
                this._isReversing = this._currentTime > s && this._currentPlayTimes === o;
                this._currentTime = s;
                var E = this._clipConfig.frame ? this._clipConfig.frame.length: 0;
                if (E > 0) {
                    var B = Math.floor(this._currentTime * this._config.frameRate);
                    var M = this._groupConfig.frame[this._clipConfig.frame[B]];
                    if (this._currentFrameConfig !== M) {
                        if (E > 1) {
                            var w = this._currentFrameConfig;
                            this._currentFrameConfig = M;
                            if (!w) {
                                var C = Math.floor(this._currentTime * this._config.frameRate);
                                w = this._groupConfig.frame[this._clipConfig.frame[C]];
                                if (this._isReversing) {} else {
                                    if (this._currentTime <= w.position || this._currentPlayTimes !== o) {
                                        w = this._groupConfig.frame[w.prev]
                                    }
                                }
                            }
                            if (this._isReversing) {
                                while (w !== M) {
                                    this._onCrossFrame(w);
                                    w = this._groupConfig.frame[w.prev]
                                }
                            } else {
                                while (w !== M) {
                                    w = this._groupConfig.frame[w.next];
                                    this._onCrossFrame(w)
                                }
                            }
                        } else {
                            this._currentFrameConfig = M;
                            if (this._currentFrameConfig) {
                                this._onCrossFrame(this._currentFrameConfig)
                            }
                        }
                    }
                }
                if (this._currentPlayTimes !== o) {
                    this._currentPlayTimes = o;
                    if (this.hasEventListener(_.LOOP_COMPLETE)) {
                        var I = egret.Event.create(_, _.LOOP_COMPLETE);
                        I.movie = this;
                        I.clipName = this._clipConfig.name;
                        I.name = "";
                        I.slotName = "";
                        this.dispatchEvent(I);
                        egret.Event.release(I)
                    }
                    if (this._isCompleted && this.hasEventListener(_.COMPLETE)) {
                        var F = egret.Event.create(_, _.COMPLETE);
                        F.movie = this;
                        F.clipName = this._clipConfig.name;
                        F.name = "";
                        F.slotName = "";
                        this.dispatchEvent(F);
                        egret.Event.release(F)
                    }
                }
            }
            this._isLockDispose = false;
            if (this._isDelayDispose) {
                this.dispose()
            }
        };
        r.prototype.play = function(t, e) {
            if (t === void 0) {
                t = null
            }
            if (e === void 0) {
                e = -1
            }
            if (t) {
                var a = null;
                for (var i = 0,
                         r = this._config.clip.length; i < r; ++i) {
                    var n = this._config.clip[i];
                    if (n.name === t) {
                        a = n
                    }
                }
                if (a) {
                    this._clipConfig = a;
                    this._clipArray = new Int16Array(this._groupConfig.arrayBuffer, this._groupConfig.offset + this._groupConfig.position[0] + this._clipConfig.p, this._clipConfig.s / this._groupConfig.position[2]);
                    if (!this._clipConfig.cacheRectangles) {
                        this._clipConfig.cacheRectangles = []
                    }
                    this._isPlaying = true;
                    this._isStarted = false;
                    this._isCompleted = false;
                    if (e < 0 || e !== e) {
                        this._playTimes = this._clipConfig.playTimes
                    } else {
                        this._playTimes = e
                    }
                    this._time = 0;
                    this._currentTime = 0;
                    this._currentPlayTimes = 0;
                    this._cacheFrameIndex = -1;
                    this._currentFrameConfig = null;
                    this._cacheRectangle = null;
                    this.clipTimeScale = 1 / this._clipConfig.scale
                } else {
                    console.warn("No clip in movie.", this._config.name, t)
                }
            } else if (this._clipConfig) {
                if (this._isPlaying || this._isCompleted) {
                    this.play(this._clipConfig.name, this._playTimes)
                } else {
                    this._isPlaying = true
                }
            } else if (this._config.action) {
                this.play(this._config.action, e)
            }
        };
        r.prototype.stop = function() {
            this._isPlaying = false
        };
        r.prototype.gotoAndPlay = function(t, e, a) {
            if (t === void 0) {
                t = null
            }
            if (a === void 0) {
                a = -1
            }
            e %= this._clipConfig.duration;
            if (e < 0) {
                e += this._clipConfig.duration
            }
            this.play(t, a);
            this._time = e;
            this._currentTime = e
        };
        r.prototype.gotoAndStop = function(t, e) {
            if (t === void 0) {
                t = null
            }
            e %= this._clipConfig.duration;
            if (e < 0) {
                e += this._clipConfig.duration
            }
            this.play(t, 1);
            this._time = e;
            this._currentTime = e;
            this.advanceTime(.001);
            this.stop()
        };
        r.prototype.hasClip = function(t) {
            for (var e = 0,
                     a = this._config.clip.length; e < a; ++e) {
                var i = this._config.clip[e];
                if (i.name === t) {
                    return true
                }
            }
            return false
        };
        Object.defineProperty(r.prototype, "isPlaying", {
            get: function() {
                return this._isPlaying && !this._isCompleted
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "isComplete", {
            get: function() {
                return this._isCompleted
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "currentTime", {
            get: function() {
                return this._currentTime
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "totalTime", {
            get: function() {
                return this._clipConfig ? this._clipConfig.duration: 0
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "currentPlayTimes", {
            get: function() {
                return this._currentPlayTimes
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "playTimes", {
            get: function() {
                return this._playTimes
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "groupName", {
            get: function() {
                return this._groupConfig.name
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "clipName", {
            get: function() {
                return this._clipConfig ? this._clipConfig.name: ""
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "clipNames", {
            get: function() {
                return this._clipNames
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "clock", {
            get: function() {
                return this._clock
            },
            set: function(t) {
                if (this._clock === t) {
                    return
                }
                var e = this._clock;
                if (e) {
                    e.remove(this)
                }
                this._clock = t;
                if (this._clock) {
                    this._clock.add(this)
                }
            },
            enumerable: true,
            configurable: true
        });
        r.prototype.advanceTimeBySelf = function(e) {
            if (e) {
                this.clock = t.EgretFactory.clock
            } else {
                this.clock = null
            }
        };
        Object.defineProperty(r.prototype, "display", {
            get: function() {
                return this
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "animation", {
            get: function() {
                return this
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "armature", {
            get: function() {
                return this
            },
            enumerable: true,
            configurable: true
        });
        r.prototype.getAnimation = function() {
            return this
        };
        r.prototype.getArmature = function() {
            return this
        };
        r.prototype.getDisplay = function() {
            return this
        };
        r.prototype.hasAnimation = function(t) {
            return this.hasClip(t)
        };
        r.prototype.invalidUpdate = function() {
            var t = [];
            for (var e = 0; e < arguments.length; e++) {
                t[e] = arguments[e]
            }
            t
        };
        Object.defineProperty(r.prototype, "lastAnimationName", {
            get: function() {
                return this.clipName
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "animationNames", {
            get: function() {
                return this.clipNames
            },
            enumerable: true,
            configurable: true
        });
        Object.defineProperty(r.prototype, "animationList", {
            get: function() {
                return this.clipNames
            },
            enumerable: true,
            configurable: true
        });
        return r
    } (egret.DisplayObjectContainer);
    t.Movie = m
})(dragonBones || (dragonBones = {}));