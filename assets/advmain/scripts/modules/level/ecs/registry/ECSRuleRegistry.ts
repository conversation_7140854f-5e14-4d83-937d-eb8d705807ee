import { ClearEffectECSRule } from '../rules/ClearEffectECSRule';
import { ConfigECSRule } from '../rules/ConfigECSRule';
import { ECSRuleBase } from '../rules/core/ECSRuleBase';
import { InitBoardSceneRule } from '../rules/InitBoardSceneRule';
import { InitBoardRule } from '../rules/InitBoardRule';
import { SimpleBlockAlgorithmRule } from '../rules/blockAlgorithm/SimpleBlockAlgorithmRule';
import { CheckRemainShapeRule } from '../rules/CheckRemainShapeRule';
import { ProduceBlockEndRule } from '../rules/ProduceBlockEndRule';
import InitTargetRule from '../rules/InitTargetRule';
import { ComboPlaySoundRule } from '../rules/combo/ComboPlaySoundRule';
import { InitMultiBoardRule } from '../gamePlay/MultiBoard/rule/InitMultiBoardRule';
import { ShowMultiClearWordRule } from '../rules/ShowMultiClearWordRule';
import { ShowTargetInfoRule } from '../rules/ShowTargetInfoRule';
import SingleScoreEffectECSRule from '../rules/SingleScoreEffectECSRule';
import { ReviveRule } from '../rules/revive/ReviveRule';
import { RevivePreviewBlockRule } from '../rules/revive/RevivePreviewBlocksRule';
import { ResultRule } from '../rules/result/ResultRule';
import { EliminationScoreCalculatorRule } from '../rules/EliminationScoreCalculatorRule';
import { PutBlockScoreCalculatorRule } from '../rules/PutBlockScoreCalculatorRule';
import { SmartProduceBlockRule } from '../rules/SmartProduceBlockRule';
import { CellRemovedRule } from '../rules/CellRemovedRule';
import { ExpandHotAreaRule } from '../rules/ExpandHotAreaRule';
import { WinStreak001Rule } from '../rules/winStreak/WinStreak001Rule';
import { WinStreakAddRule } from '../rules/winStreak/WinStreakAddRule';
import { WinStreakClearRule } from '../rules/winStreak/WinStreakClearRule';
import { DragEffectAudioRule } from '../rules/audiosRule/DragEffectAudioRule';
import { ComboEffectAudioRule } from '../rules/audiosRule/ComboEffectAudioRule';
import { MotivationalEffectAudioRule } from '../rules/audiosRule/MotivationalEffectAudioRule';
import { PutEffectAudioRule } from '../rules/audiosRule/PutEffectAudioRule';
import { AdaptivePreviewRule } from '../rules/AdaptivePreviewRule';
import { MultiStageProgressBlockAlgorithmRule } from '../rules/blockAlgorithm/MultiStageProgressAlgorithmRule';
import { InitBoardSceneColorsRule } from '../rules/InitBoardSceneColorsRule';
import { DecreaseTargetRule } from '../rules/DecreaseTargetRule';
import { VibrationAndroidNativeRule } from '../rules/VibrationAndroidNativeRule';
import { InitMultiEliminationSpecialEffectRule } from '../rules/InitMultiEliminationSpecialEffectRule';
import { BoardRefurbishEffectRule } from '../rules/BoardRefurbishEffectRule';
import { LowPerformanceDeviceResAdaptRule } from '../rules/LowPerformanceDeviceResAdaptRule';
import { ProduceBlockShapeRule } from '../rules/produceBlock/shape/ProduceBlockShapeRule';
import { ProduceBlockColorRule } from '../rules/produceBlock/color/ProduceBlockColorRule';
import { ProduceBlockCollectibleRule } from '../rules/produceBlock/collectible/ProduceBlockCollectibleRule';
import { ProduceBlockCreateEntityRule } from '../rules/produceBlock/ProduceBlockCreateEntityRule';
import { ReviveStartPreviewBlocksRule } from '../rules/revive/ReviveStartPreviewBlocksRule';

export const enum ECSRuleConst {
    /**
     * 配置规则
     */
    ConfigECSRule = 'ConfigECSRule',
    /**
     * 清除特效规则
     */
    ClearEffectECSRule = 'ClearEffectECSRule',
    /**初始化场景规则*/
    InitBoardSceneRule = 'InitBoardSceneRule',
    /**初始化棋盘规则*/
    InitBoardRule = 'InitBoardRule',
    /**生成块算法规则，根据配置的算法algorithmId生成对应的块*/
    ProduceBlockAlgorithmRule = 'ProduceBlockAlgorithmRule',
    /**生成块结束规则*/
    ProduceBlockEndRule = 'ProduceBlockEndRule',
    /**检查剩余形状可放置*/
    CheckRemainShapeRule = 'CheckRemainShapeRule',
    /**连击播放音效规则*/
    ComboPlaySoundRule = 'ComboPlaySoundRule',
    /**初始化目标 */
    InitTargetRule = 'InitTargetRule',
    /**初始化层层叠叠盘面*/
    InitMultiBoardRule = 'InitMultiBoardRule',
    /**多消激励词展示规则 */
    ShowMultiClearWordRule = 'ShowMultiClearWordRule',
    /**关卡目标信息显示 */
    ShowTargetInfoRule = 'ShowTargetInfoRule',
    /**分数展示特效规则 */
    ScoreEffectECSRule = 'ScoreEffectECSRule',
    /**复活规则*/
    ReviveRule = 'ReviveRule',
    /**复活预览块规则*/
    RevivePreviewBlocksRule = 'RevivePreviewBlocksRule',
    /**复活显示块规则*/
    ReviveShowBlocksRule = 'ReviveShowBlocksRule',
    /**复活预览块规则*/
    ReviveStartPreviewBlocksRule = 'ReviveStartPreviewBlocksRule',
    /**游戏结算规则*/
    ResultRule = 'ResultRule',
    /**消除分数计算规则*/
    EliminationScoreCalculatorRule = 'EliminationScoreCalculatorRule',
    /**放置块分数计算规则*/
    PutBlockScoreCalculatorRule = 'PutBlockScoreCalculatorRule',
    /**智能生成块规则*/
    SmartProduceBlockRule = 'SmartProduceBlockRule',
    /** 单元格移除规则 */
    CellRemovedRule = 'CellRemovedRule',
    /** 拖动倍率规则 */
    DragMultiplierRule = 'DragMultiplierRule',
    /** 扩大热区规则 */
    ExpandHotAreaRule = 'ExpandHotAreaRule',
    /**连胜001规则*/
    WinStreak001Rule = 'WinStreak001Rule',
    /** 连胜增加规则 */
    WinStreakAddRule = 'WinStreakAddRule',
    /** 连胜清除规则 */
    WinStreakClearRule = 'WinStreakClearRule',
    /** 拖动音效播放规则 */
    DragEffectAudioPlayRule = 'DragEffectAudioPlayRule',
    /** 消除音效播放规则 */
    ComboEffectAudioPlayRule = 'ComboEffectAudioPlayRule',
    /** 激励语音播放规则 */
    MotivationalEffectAudioPlayRule = 'MotivationalEffectAudioPlayRule',
    /** 放置音效播放规则 */
    PutEffectAudioPlayRule = 'PutEffectAudioPlayRule',
    /** 自适应预览规则 */
    AdaptivePreviewRule = 'AdaptivePreviewRule',
    /** 多阶段进度算法规则 */
    MultiStageProgressAlgorithmRule = 'MultiStageProgressAlgorithmRule',
    /** 初始化方块颜色规则 */
    InitBoardSceneColorsRule = 'InitBoardSceneColorsRule',
    /** 减少目标规则 */
    DecreaseTargetRule = 'DecreaseTargetRule',
    /** Android原生震动规则 */
    VibrationAndroidNativeRule = 'VibrationAndroidNativeRule',
    /** 初始化多消特效规则 */
    InitMultiEliminationSpecialEffectRule = 'InitMultiEliminationSpecialEffectRule',
    /** 生成块形状规则 */
    ProduceBlockShapeRule = 'ProduceBlockShapeRule',
    /** 生成块颜色规则 */
    ProduceBlockColorRule = 'ProduceBlockColorRule',
    /** 生成块收集物规则 */
    ProduceBlockCollectibleRule = 'ProduceBlockCollectibleRule',
    /** 生成块创建实体规则 */
    ProduceBlockCreateEntityRule = 'ProduceBlockCreateEntityRule',
    /** 方块刷新动画规则 */
    BoardRefurbishEffectRule = 'BoardRefurbishEffectRule',
    /** 低性能设备资源适配规则 */
    LowPerformanceDeviceResAdaptRule = 'LowPerformanceDeviceResAdaptRule',
}
export const ECSRuleRegistry: Record<string, clzz<ECSRuleBase>> = {
    [ECSRuleConst.ConfigECSRule]: ConfigECSRule,
    [ECSRuleConst.ClearEffectECSRule]: ClearEffectECSRule,
    [ECSRuleConst.InitBoardSceneRule]: InitBoardSceneRule,
    [ECSRuleConst.InitBoardRule]: InitBoardRule,
    [ECSRuleConst.ProduceBlockAlgorithmRule]: SimpleBlockAlgorithmRule,
    [ECSRuleConst.ProduceBlockEndRule]: ProduceBlockEndRule,
    [ECSRuleConst.CheckRemainShapeRule]: CheckRemainShapeRule,
    [ECSRuleConst.ComboPlaySoundRule]: ComboPlaySoundRule,
    [ECSRuleConst.InitTargetRule]: InitTargetRule,
    [ECSRuleConst.InitMultiBoardRule]: InitMultiBoardRule,
    [ECSRuleConst.ShowMultiClearWordRule]: ShowMultiClearWordRule,
    [ECSRuleConst.ShowTargetInfoRule]: ShowTargetInfoRule,
    [ECSRuleConst.ScoreEffectECSRule]: SingleScoreEffectECSRule,
    [ECSRuleConst.ReviveRule]: ReviveRule,
    [ECSRuleConst.RevivePreviewBlocksRule]: RevivePreviewBlockRule,
    // [ECSRuleConst.ReviveShowBlocksRule]: ReviveShowBlocksRule,
    [ECSRuleConst.ReviveStartPreviewBlocksRule]: ReviveStartPreviewBlocksRule,
    [ECSRuleConst.ResultRule]: ResultRule,
    [ECSRuleConst.PutBlockScoreCalculatorRule]: PutBlockScoreCalculatorRule,
    [ECSRuleConst.EliminationScoreCalculatorRule]: EliminationScoreCalculatorRule,
    [ECSRuleConst.SmartProduceBlockRule]: SmartProduceBlockRule,
    [ECSRuleConst.CellRemovedRule]: CellRemovedRule,
    [ECSRuleConst.ExpandHotAreaRule]: ExpandHotAreaRule,
    [ECSRuleConst.WinStreak001Rule]: WinStreak001Rule,
    [ECSRuleConst.WinStreakAddRule]: WinStreakAddRule,
    [ECSRuleConst.WinStreakClearRule]: WinStreakClearRule,
    [ECSRuleConst.DragEffectAudioPlayRule]: DragEffectAudioRule,
    [ECSRuleConst.ComboEffectAudioPlayRule]: ComboEffectAudioRule,
    [ECSRuleConst.MotivationalEffectAudioPlayRule]: MotivationalEffectAudioRule,
    [ECSRuleConst.PutEffectAudioPlayRule]: PutEffectAudioRule,
    [ECSRuleConst.AdaptivePreviewRule]: AdaptivePreviewRule,
    [ECSRuleConst.MultiStageProgressAlgorithmRule]: MultiStageProgressBlockAlgorithmRule,
    [ECSRuleConst.InitBoardSceneColorsRule]: InitBoardSceneColorsRule,
    [ECSRuleConst.DecreaseTargetRule]: DecreaseTargetRule,
    [ECSRuleConst.VibrationAndroidNativeRule]: VibrationAndroidNativeRule,
    [ECSRuleConst.InitMultiEliminationSpecialEffectRule]: InitMultiEliminationSpecialEffectRule,
    [ECSRuleConst.ProduceBlockShapeRule]: ProduceBlockShapeRule,
    [ECSRuleConst.ProduceBlockColorRule]: ProduceBlockColorRule,
    [ECSRuleConst.ProduceBlockCollectibleRule]: ProduceBlockCollectibleRule,
    [ECSRuleConst.ProduceBlockCreateEntityRule]: ProduceBlockCreateEntityRule,
    [ECSRuleConst.BoardRefurbishEffectRule]: BoardRefurbishEffectRule,
    [ECSRuleConst.LowPerformanceDeviceResAdaptRule]: LowPerformanceDeviceResAdaptRule,
};
