import { BoardECSView } from '../renderWorld/view/board/BoardECSView';
import CellECSView from '../renderWorld/view/board/CellECSView';
import CollectCellECSView from '../renderWorld/view/board/CollectCellECSView';
import ShapeECSView from '../renderWorld/view/board/ShapeECSView';
import SlotECSView from '../renderWorld/view/board/SlotECSView';
import { CollectIconECSView } from '../renderWorld/view/CollectIconECSView';
import { ECSViewBase } from '../renderWorld/ECSViewBase';
import TargetECSView from '../renderWorld/view/TargetECSView';
import TargetInfoECSView from '../renderWorld/view/TargetInfoECSView';
import PlaneECSView from '../renderWorld/view/cell/PlaneECSView';
import { SimpleEffectECSView } from '../renderWorld/view/common/SimpleEffectECSView';
import { StaticECSView } from '../renderWorld/view/common/StaticECSView';
import { SceneECSView } from '../renderWorld/view/common/SceneECSView';
import { ComplexEffectECSView } from '../renderWorld/view/common/ComplexEffectECSView';
import { PlaneSpineECSView } from '../renderWorld/view/cell/PlaneSpineECSView';
import { SingleScoreEffectView } from '../renderWorld/view/common/SingleScoreEffectView';
import { ReviveECSView } from '../renderWorld/view/ReviveECSView';
import { CollectFailECSView } from '../renderWorld/view/result/CollectFailECSView';
import { ScoreFailECSView } from '../renderWorld/view/result/ScoreFailECSView';
import { ScoreWinECSView } from '../renderWorld/view/result/ScoreWinECSView';
import { CollectWinECSView } from '../renderWorld/view/result/CollectWinECSView';
import { WinStreak001ECSView } from '../renderWorld/view/winStreak/WinStreak001ECSView';
import GlassECSView from '../renderWorld/view/cell/GlassECSView';
import { MultiEliminationSpecialEffectECSView } from '../renderWorld/view/common/MultiEliminationSpecialEffectECSView';
import { BoardRefurbishEffectView } from '../renderWorld/view/board/BoardRefurbishEffectView';
import DiamondECSView from '../renderWorld/view/cell/DiamondECSView';

export const enum ECSViewConst {
    /**静态视图 */
    StaticECSView = 'StaticECSView',
    /**场景视图 */
    SceneECSView = 'SceneECSView',
    /**复杂可配置-支持音效 特效视图 */
    ComplexEffectECSView = 'ComplexEffectECSView',
    /**播放特效视图-简单特性播放可复用 */
    SimpleEffectECSView = 'SimpleEffectECSView',
    /**棋盘视图 */
    BoardECSView = 'BoardECSView',
    /**棋盘全屏动画视图 */
    BoardFullSceneEffectECSView = 'BoardFullSceneEffectECSView',
    /**收集物Icon */
    CollectIconECSView = 'CollectIconECSView',
    /**格子视图 */
    SlotECSView = 'SlotECSView',
    /**形状视图 */
    ShapeECSView = 'ShapeECSView',
    /**块视图 */
    CellECSView = 'CellECSView',
    /**收集元素视图 */
    CollectCellECSView = 'CollectCellECSView',
    /**目标视图 */
    TargetECSView = 'TargetECSView',
    /**目标信息显示视图 */
    TargetInfoECSView = 'TargetInfoECSView',

    /**钻石块视图 */
    DiamondECSView = 'DiamondECSView',
    /**玻璃块视图 */
    GlassECSView = 'GlassECSView',
    /**飞机块视图 */
    PlaneECSView = 'PlaneECSView',
    /**飞机特效视图 */
    PlaneSpineECSView = 'PlaneSpineECSView',
    /**单次分数展示特效视图 */
    SingleScoreEffectView = 'SingleScoreEffectView',

    /**复活视图 */
    ReviveECSView = 'ReviveECSView',

    /**收集物关卡失败视图 */
    CollectFailECSView = 'CollectFailECSView',

    /**分数关卡失败视图 */
    ScoreFailECSView = 'ScoreFailECSView',

    /**分数关卡成功视图 */
    ScoreWinECSView = 'ScoreWinECSView',

    /**收集物关卡成功视图 */
    CollectWinECSView = 'CollectWinECSView',

    /**连胜视图 */
    WinStreak001ECSView = 'WinStreak001ECSView',

    /**多消特效视图 增加爽感*/
    MultiEliminationEffectSpecialECSView = 'MultiClearEffectSpecialECSView',
    /**方块刷新动画视图 */
    BoardRefurbishEffectECSView = 'BoardRefurbishEffectECSView',
    /**复活块特效视图 */
    ReviveBlockEffectECSView = 'ReviveBlockEffectECSView',
}
export const ECSViewRegistry: Record<string, clzz<ECSViewBase>> = {
    [ECSViewConst.ComplexEffectECSView]: ComplexEffectECSView,
    [ECSViewConst.BoardECSView]: BoardECSView,
    [ECSViewConst.CollectIconECSView]: CollectIconECSView,
    [ECSViewConst.SlotECSView]: SlotECSView,
    [ECSViewConst.ShapeECSView]: ShapeECSView,
    [ECSViewConst.CellECSView]: CellECSView,
    [ECSViewConst.StaticECSView]: StaticECSView,
    [ECSViewConst.CollectCellECSView]: CollectCellECSView,
    [ECSViewConst.TargetECSView]: TargetECSView,
    [ECSViewConst.TargetInfoECSView]: TargetInfoECSView,
    [ECSViewConst.SceneECSView]: SceneECSView,
    [ECSViewConst.DiamondECSView]: DiamondECSView,
    [ECSViewConst.GlassECSView]: GlassECSView,
    [ECSViewConst.PlaneECSView]: PlaneECSView,
    [ECSViewConst.PlaneSpineECSView]: PlaneSpineECSView,
    [ECSViewConst.SimpleEffectECSView]: SimpleEffectECSView,
    [ECSViewConst.SingleScoreEffectView]: SingleScoreEffectView,
    [ECSViewConst.ReviveECSView]: ReviveECSView,
    [ECSViewConst.CollectFailECSView]: CollectFailECSView,
    [ECSViewConst.ScoreFailECSView]: ScoreFailECSView,
    [ECSViewConst.ScoreWinECSView]: ScoreWinECSView,
    [ECSViewConst.CollectWinECSView]: CollectWinECSView,
    [ECSViewConst.WinStreak001ECSView]: WinStreak001ECSView,
    [ECSViewConst.MultiEliminationEffectSpecialECSView]: MultiEliminationSpecialEffectECSView,
    [ECSViewConst.BoardRefurbishEffectECSView]: BoardRefurbishEffectView,
};
