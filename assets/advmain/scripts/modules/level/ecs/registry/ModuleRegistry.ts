// assets/advmain/scripts/modules/level/ecs/registry/ModuleRegistry.ts

import { System } from '../cores/System';
import { ECSRuleBase } from '../rules/core/ECSRuleBase';
import { ISystemConfig } from '../cores/System';
import { IECSRuleConfig } from '../rules/core/ECSRuleBase';
import { SystemsRegistry } from './SystemRegistry';
import { ECSRuleRegistry } from './ECSRuleRegistry';
import { SystemConfig } from '../config/conf/SystemConfig';
import { RuleConfig } from '../config/conf/RuleConfig';

/**
 * 模块注册器接口
 */
export interface IModuleRegistry {
    /** 模块名称 */
    readonly moduleName: string;
    /** 模块版本 */
    readonly version: string;

    /** 注册系统 */
    registerSystems(systemRegistry: Record<string, clzz<System>>): void;
    /** 注册规则 */
    registerRules(ruleRegistry: Record<string, clzz<ECSRuleBase>>): void;
    /** 注册系统配置 */
    registerSystemConfigs(systemConfig: Record<string, ISystemConfig>): void;
    /** 注册规则配置 */
    registerRuleConfigs(ruleConfig: Record<string, IECSRuleConfig>): void;
    /** 获取系统常量 */
    getSystemConstants(): Record<string, string>;
    /** 获取规则常量 */
    getRuleConstants(): Record<string, string>;
}

/**
 * 模块注册器基类
 */
export abstract class BaseModuleRegistry implements IModuleRegistry {
    abstract readonly moduleName: string;
    abstract readonly version: string;

    registerSystems(systemRegistry: Record<string, clzz<System>>): void {
        // 子类实现
    }

    registerRules(ruleRegistry: Record<string, clzz<ECSRuleBase>>): void {
        // 子类实现
    }

    registerSystemConfigs(systemConfig: Record<string, ISystemConfig>): void {
        // 子类实现
    }

    registerRuleConfigs(ruleConfig: Record<string, IECSRuleConfig>): void {
        // 子类实现
    }

    getSystemConstants(): Record<string, string> {
        return {};
    }

    getRuleConstants(): Record<string, string> {
        return {};
    }
}

/**
 * 模块注册管理器
 * 统一管理所有玩法模块的注册，支持自动初始化
 */
export class ModuleRegistryManager {
    private static instance: ModuleRegistryManager;
    private moduleRegistries: Map<string, IModuleRegistry> = new Map();
    private isInitialized: boolean = false;
    private autoInitEnabled: boolean = true;

    constructor() {
        // 启用自动初始化
        this.enableAutoInit();
    }
    /**
     * 启用自动初始化
     * 当模块注册时自动初始化到ECS系统
     */
    private enableAutoInit(): void {
        this.autoInitEnabled = true;
    }

    /**
     * 禁用自动初始化
     */
    disableAutoInit(): void {
        this.autoInitEnabled = false;
    }

    /**
     * 注册模块
     * @param moduleRegistry 模块注册器
     */
    registerModule(moduleRegistry: IModuleRegistry): void {
        this.moduleRegistries.set(moduleRegistry.moduleName, moduleRegistry);
        console.log(`[ModuleRegistryManager] 模块 ${moduleRegistry.moduleName} v${moduleRegistry.version} 已注册`);
        // 如果启用了自动初始化，立即初始化该模块
        if (this.autoInitEnabled && !this.isInitialized) {
            this.initializeModule(moduleRegistry);
        }
    }

    /**
     * 初始化单个模块
     * @param moduleRegistry 模块注册器
     */
    private initializeModule(moduleRegistry: IModuleRegistry): void {
        moduleRegistry.registerSystems(SystemsRegistry);
        moduleRegistry.registerRules(ECSRuleRegistry);
        moduleRegistry.registerSystemConfigs(SystemConfig);
        moduleRegistry.registerRuleConfigs(RuleConfig);
    }

    /**
     * 获取所有已注册的模块
     */
    getAllModules(): IModuleRegistry[] {
        return Array.from(this.moduleRegistries.values());
    }

    /**
     * 获取指定模块
     * @param moduleName 模块名称
     */
    getModule(moduleName: string): IModuleRegistry | undefined {
        return this.moduleRegistries.get(moduleName);
    }

    /**
     * 清空所有模块
     */
    clear(): void {
        this.moduleRegistries.clear();
        this.isInitialized = false;
    }
}

/**
 * 便捷注册函数
 */
export function registerModule(
    moduleRegistry: IModuleRegistry,
    systemRegistry: Record<string, clzz<System>>,
    ruleRegistry: Record<string, clzz<ECSRuleBase>>,
    systemConfig: Record<string, ISystemConfig>,
    ruleConfig: Record<string, IECSRuleConfig>,
): void {
    try {
        moduleRegistry.registerSystems(systemRegistry);
        moduleRegistry.registerRules(ruleRegistry);
        moduleRegistry.registerSystemConfigs(systemConfig);
        moduleRegistry.registerRuleConfigs(ruleConfig);
        console.log(`[ModuleRegistry] 模块 ${moduleRegistry.moduleName} v${moduleRegistry.version} 注册成功`);
    } catch (error) {
        console.error(`[ModuleRegistry] 模块 ${moduleRegistry.moduleName} 注册失败:`, error);
        throw error;
    }
}

// 导出单例实例
export var moduleRegistryManager = new ModuleRegistryManager();
