import { System } from '../cores/System';
import { DestroySystem } from '../systems/DestroySystem';
import { GameResultSystem } from '../systems/logicSystems/board/GameResultSystem';
import { EliminationSystem } from '../systems/logicSystems/board/EliminationSystem';
import { InteractionSystem } from '../systems/logicSystems/board/InteractionSystem';
import { RuleSystem } from '../systems/RuleSystem';
import { RenderSystem } from '../systems/RenderSystem';
import { PreviewSystem } from '../systems/logicSystems/board/PreviewSystem';
import { AISystem } from '../systems/AISystem';
import ComboSystem from '../systems/logicSystems/board/ComboSystem';
import { EffectPreviewSystem } from '../systems/logicSystems/board/EffectPreviewSystem';
import TargetSystem from '../systems/logicSystems/TargetSystem';
import { ProjectileSystem } from '../systems/ProjectileSystem';
import { DragBlockSystem } from '../systems/logicSystems/board/DragBlockSystem';
import { HighlightNotifierSystem } from '../systems/HighlightNotifierSystem';
import { BuffSystem } from '../systems/BuffSystem';
import { ReviveSystem } from '../systems/logicSystems/ReviveSystem';
import { BlockAlgorithmSystem } from '../systems/BlockAlgorithmSystem';
import TurnSystem from '../systems/logicSystems/board/TurnSystem';
import { CutdownSystem } from '../systems/CutdownSystem';

/**基础系统 */
export const enum SystemConst {
    /**销毁系统 */
    DestroySystem = 'DestroySystem',
    /**规则系统 */
    RuleSystem = 'RuleSystem',
    /**渲染系统 */
    RenderSystem = 'RenderSystem',
    /**AI系统 */
    AISystem = 'AISystem',
    /**投射物系统 */
    ProjectileSystem = 'ProjectileSystem',
    /**Buff系统 */
    BuffSystem = 'BuffSystem',
    /**倒计时系统 */
    CutdownSystem = 'CutdownSystem',
}
/**基础业务系统 */
export const enum SystemConst {
    /**消除系统 */
    EliminationSystem = 'EliminationSystem',
    /**回合系统 */
    TurnSystem = 'TurnSystem',
    /**游戏结果系统 */
    GameResultSystem = 'GameResultSystem',
    /**交互系统 */
    InteractionSystem = 'InteractionSystem',
    /**预览系统 */
    PreviewSystem = 'PreviewSystem',
    /**连击系统 */
    ComboSystem = 'ComboSystem',
    /**特效行列预览 */
    EffectPreviewSystem = 'EffectPreviewSystem',
    /**目标系统 */
    TargetSystem = 'TargetSystem',
    /*拖拽块系统 */
    DragBlockSystem = 'DragBlockSystem',
    /**高亮通知系统 */
    HighlightNotifierSystem = 'HighlightNotifierSystem',
    /**复活系统 */
    ReviveSystem = 'ReviveSystem',
    /**生成块系统 */
    BlockAlgorithmSystem = 'BlockAlgorithmSystem',
}

/**基础系统 */
export const basicSystems = [
    SystemConst.AISystem,
    SystemConst.RuleSystem,
    SystemConst.RenderSystem,
    SystemConst.ProjectileSystem,
    SystemConst.BuffSystem,
    SystemConst.CutdownSystem,
];

export const SystemsRegistry: Record<string, clzz<System>> = {
    [SystemConst.ProjectileSystem]: ProjectileSystem,
    [SystemConst.BuffSystem]: BuffSystem,
    [SystemConst.EliminationSystem]: EliminationSystem,
    [SystemConst.TurnSystem]: TurnSystem,
    [SystemConst.BlockAlgorithmSystem]: BlockAlgorithmSystem,
    [SystemConst.GameResultSystem]: GameResultSystem,
    [SystemConst.DestroySystem]: DestroySystem,
    [SystemConst.InteractionSystem]: InteractionSystem,
    [SystemConst.RuleSystem]: RuleSystem,
    [SystemConst.RenderSystem]: RenderSystem,
    [SystemConst.PreviewSystem]: PreviewSystem,
    [SystemConst.AISystem]: AISystem,
    [SystemConst.ComboSystem]: ComboSystem,
    [SystemConst.EffectPreviewSystem]: EffectPreviewSystem,
    [SystemConst.TargetSystem]: TargetSystem,
    [SystemConst.DragBlockSystem]: DragBlockSystem,
    [SystemConst.HighlightNotifierSystem]: HighlightNotifierSystem,
    [SystemConst.ReviveSystem]: ReviveSystem,
    [SystemConst.CutdownSystem]: CutdownSystem,
};
