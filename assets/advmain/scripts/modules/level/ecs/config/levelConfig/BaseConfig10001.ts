import { FlowType } from '../../../../../base/rule/flow/FlowType';
import { IGameLevelConfig } from '../../cores/center/ConfigCenter';
import { ECSEvent } from '../../GameEvent';

export const BaseConfig10001: IGameLevelConfig = {
    levelId: 'BaseConfig10001',
    sys: [
        'system_interaction',
        'system_combo',
        'system_eliminate',
        'system_turn',
        'system_game_result',
        'system_preview',
        'system_target',
        'system_highlight_notifier',
        'system_revive',
    ],
    ruleFlowConfigForWorldInit: {
        type: FlowType.List,
        children: ['rule_init_board_scene', 'rule_init_board', 'rule_init_target', 'rule_produce_block_algorithm_algo_revive'],
    },
    ruleFlowConfigForEvent: {
        [ECSEvent.GameEvent.ELIMINATION]: {
            type: FlowType.List,
            children: ['rule_clear_effect', 'rule_check_remain_shape', 'rule_show_multi_clear_word'],
        },
        // [ECSEvent.GameEvent.PRODUCE_BLOCK]:
        // {
        //     type: FlowType.List,
        //     children: ['rule_produce_block'],
        // },
        [ECSEvent.GameEvent.PRODUCE_BLOCK_END]: {
            type: FlowType.List,
            children: ['rule_produce_block_end'],
        },
        [ECSEvent.GameEvent.PUT_BLOCK_BACK]: {
            type: FlowType.List,
            children: ['rule_check_remain_shape'],
        },
        [ECSEvent.GameEvent.COMBO_CHANGED]: {
            type: FlowType.List,
            children: ['rule_combo_play_sound'],
        },
        [ECSEvent.GameEvent.GAME_FAILED]: {
            type: FlowType.Select,
            children: ['rule_revive', 'rule_game_score_fail'],
        },
        [ECSEvent.GameEvent.GAME_WIN]: {
            type: FlowType.List,
            children: ['rule_game_score_win'],
        },
        [ECSEvent.GameEvent.PRODUCE_BLOCK_REVIVE]: {
            type: FlowType.List,
            children: ['rule_produce_block_algorithm_algo_revive'],
        },
    },
    snapshotEvents: [ECSEvent.GameEvent.ALL_SETTLE_DONE],
};
