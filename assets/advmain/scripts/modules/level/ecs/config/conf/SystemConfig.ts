import { ISystemConfig } from '../../cores/System';
import { SystemConst } from '../../registry/SystemRegistry';
import { IDragSystemConfig } from '../../systems/logicSystems/board/DragBlockSystem';
/**系统配置 */
export const SystemConfig: Record<string, ISystemConfig> = {
    system_interaction: {
        systemId: 'system_interaction',
        systemName: SystemConst.InteractionSystem,
    },
    system_eliminate: {
        systemId: 'system_eliminate',
        systemName: SystemConst.EliminationSystem,
    },
    system_game_result: {
        systemId: 'system_game_result',
        systemName: SystemConst.GameResultSystem,
    },
    system_preview: {
        systemId: 'system_preview',
        systemName: SystemConst.PreviewSystem,
    },
    // system_mirror: {
    //     systemId: 'system_mirror',
    //     systemName: SystemConst.MirrorSyncSystem,
    // },
    system_combo: {
        systemId: 'system_combo',
        systemName: SystemConst.ComboSystem,
    },
    // system_board_link: {
    //     systemId: 'system_board_link',
    //     systemName: SystemConst.BoardLinkSystem,
    // },
    // system_board_switch: {
    //     systemId: 'system_board_switch',
    //     systemName: SystemConst.BoardSwitchSystem,
    // },
    system_effect_preview: {
        systemId: 'system_effect_preview',
        systemName: SystemConst.EffectPreviewSystem,
    },
    system_target: {
        systemId: 'system_target',
        systemName: SystemConst.TargetSystem,
    },
    system_drag_block: {
        systemId: 'system_drag_block',
        systemName: SystemConst.DragBlockSystem,
        dragMultiplier: 1.4,
        dragYOffset: 200,
        baseScale: 0.5,
    } as IDragSystemConfig,
    system_mulity_board: {
        systemId: 'system_mulity_board',
        systemName: SystemConst.MultiBoardSystem,
    },
    system_highlight_notifier: {
        systemId: 'system_highlight_notifier',
        systemName: SystemConst.HighlightNotifierSystem,
    },
    system_nultity_tween: {
        systemId: 'system_nultity_tween',
        systemName: SystemConst.MultiBoardTweenSystem,
    },
    system_revive: {
        systemId: 'system_revive',
        systemName: SystemConst.ReviveSystem,
    },
    system_block_algorithm: {
        systemId: 'system_block_algorithm',
        systemName: SystemConst.BlockAlgorithmSystem,
    },

};
