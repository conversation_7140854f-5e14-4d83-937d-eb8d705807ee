import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';
import { ConfigTableConst } from '../../registry/ConfigRegistry';
import { ECSRuleConst } from '../../registry/ECSRuleRegistry';
import { IClearEffectECSRuleConfig } from '../../rules/ClearEffectECSRule';
import { IConfigECSRuleConfig } from '../../rules/ConfigECSRule';
import { IECSRuleConfig } from '../../rules/core/ECSRuleBase';
import { IInitBoardRuleConfig } from '../../rules/InitBoardRule';
import { ISimpleBlockAlgorithmRuleConfig } from '../../rules/blockAlgorithm/SimpleBlockAlgorithmRule';
import { IResultRuleConfig } from '../../rules/result/ResultRule';
import { IReviveRuleConfig } from '../../rules/revive/ReviveRule';
import { IShowMultiClearWordRuleConfig } from '../../rules/ShowMultiClearWordRule';
import { ISingleScoreEffectECSRuleConfig } from '../../rules/SingleScoreEffectECSRule';
import { IExpandHotAreaRuleConfig } from '../../rules/ExpandHotAreaRule';
import { IInitTargetRuleConfig } from '../../rules/InitTargetRule';
import { IWinStreak001RuleConfig } from '../../rules/winStreak/WinStreak001Rule';
import { IWinStreakAddRuleConfig } from '../../rules/winStreak/WinStreakAddRule';
import { IWinStreakClearRuleConfig } from '../../rules/winStreak/WinStreakClearRule';
import { IDragEffectAudioRuleConfig } from '../../rules/audiosRule/DragEffectAudioRule';
import { IComboEffectAudioRuleConfig } from '../../rules/audiosRule/ComboEffectAudioRule';
import { IMotivationalEffectAudioRuleConfig } from '../../rules/audiosRule/MotivationalEffectAudioRule';
import { IPutEffectAudioRuleConfig } from '../../rules/audiosRule/PutEffectAudioRule';
import { AudioId } from './EcsAudioConfig';
import { IAdaptivePreviewRuleConfig } from '../../rules/AdaptivePreviewRule';
import { IMultiStageProgressBlockAlgorithmRuleConfig } from '../../rules/blockAlgorithm/MultiStageProgressAlgorithmRule';
import { IInitBoardColorsRuleConfig } from '../../rules/InitBoardSceneColorsRule';
import { IDecreaseTargetRuleConfig } from '../../rules/DecreaseTargetRule';
import { IVibrationAndroidNativeRuleConfig } from '../../rules/VibrationAndroidNativeRule';
import { NormalColor7 } from '../../define/BoardDefine';
import { ILowPerformanceDeviceResAdaptRuleConfig } from '../../rules/LowPerformanceDeviceResAdaptRule';
import { IGameOverPreHandleRuleConfig } from '../../rules/GameOverPreHandleRule';
import { IGameOverCountdownDelayRuleConfig } from '../../rules/GameOverCountdownDelayRule';
import { IGameOverCountdownEndHandleRuleConfig } from '../../rules/GameOverCountdownEndHandleRule';

/**规则配置 */
export const RuleConfig: Record<string, IECSRuleConfig> = {
    rule_clear_effect: {
        ruleId: 'rule_clear_effect',
        ruleDesc: '清除特效规则',
        ruleType: ECSRuleConst.ClearEffectECSRule,
        delay: 0,
        aniName: { 1: 'blue', 5: 'red', 2: 'yellow', 6: 'green', 3: 'purple', 4: 'orange', 7: 'wathetblue' },
        eliminateEcsViewId: 'ecsview_simple_effect',
    } as IClearEffectECSRuleConfig,
    rule_config: {
        ruleId: 'rule_config',
        ruleDesc: '配置规则',
        ruleType: ECSRuleConst.ConfigECSRule,
        tableKey: ConfigTableConst.EcsViewConfig,
        configId: 'ecsview_simple_effect',
        configKey: 'animationName',
        configVal: 'yellow',
    } as IConfigECSRuleConfig,
    rule_init_board_scene: {
        ruleId: 'rule_init_board_scene',
        ruleDesc: '初始化棋盘场景',
        ruleType: ECSRuleConst.InitBoardSceneRule,
        delay: 0,
    },
    rule_init_board: {
        ruleId: 'rule_init_board',
        ruleDesc: '初始化棋盘',
        ruleType: ECSRuleConst.InitBoardRule,
        delay: 0,
        boardIdList: ['classic'],
    } as IInitBoardRuleConfig,
    rule_produce_block_algorithm_algoRandom: {
        ruleId: 'rule_produce_block_algorithm_algoRandom',
        ruleDesc: 'AlgoRandom随机生成块',
        ruleType: ECSRuleConst.ProduceBlockAlgorithmRule,
        algorithmId: AlgoClassTypeConst.AlgoRandom,
        delay: 0,
    } as ISimpleBlockAlgorithmRuleConfig,
    rule_produce_block_algorithm_algo_revive: {
        ruleId: 'rule_produce_block_algorithm_algo_revive',
        ruleDesc: 'algo_revive成块',
        ruleType: ECSRuleConst.ProduceBlockAlgorithmRule,
        algorithmId: AlgoClassTypeConst.algo_revive,
        delay: 0,
    } as ISimpleBlockAlgorithmRuleConfig,
    rule_init_target: {
        ruleId: 'rule_init_target',
        ruleDesc: '初始化目标',
        ruleType: ECSRuleConst.InitTargetRule,
        delay: 0,
        target: [[1, 50]],
    } as IInitTargetRuleConfig,
    rule_check_remain_shape: {
        ruleId: 'rule_check_remain_shape',
        ruleDesc: '检查剩余形状可放置',
        ruleType: ECSRuleConst.CheckRemainShapeRule,
        delay: 0,
    },
    rule_combo_play_sound: {
        ruleId: 'rule_combo_play_sound',
        ruleDesc: '连击播放音效',
        ruleType: ECSRuleConst.ComboPlaySoundRule,
        delay: 0,
    },
    rule_produce_block_without_death: {
        ruleId: 'rule_produce_block_without_death',
        ruleDesc: '生成无死块',
        ruleType: ECSRuleConst.ProduceBlockAlgorithmRule,
        algorithmId: AlgoClassTypeConst.algo_random_no_death,
        delay: 0,
    } as ISimpleBlockAlgorithmRuleConfig,
    rule_produce_block_end: {
        ruleId: 'rule_produce_block_end',
        ruleDesc: '生成块结束',
        ruleType: ECSRuleConst.ProduceBlockEndRule,
        delay: 0,
    },
    rule_show_multi_clear_word: {
        ruleId: 'rule_show_multi_clear_word',
        ruleDesc: '多消激励词展示',
        ruleType: ECSRuleConst.ShowMultiClearWordRule,
        color_name: { 1: `_darkblue`, 2: `_yellow`, 3: `_purple`, 4: `_orange`, 5: `_red`, 6: `_green`, 7: `_blue` },
        prompt: { 2: `good`, 3: `great`, 4: `excellect`, 5: `excellect`, 6: `amazing`, 7: `unbelievable`, 8: `newhighscore` },
        ecsViewId: 'ecsview_simple_effect',
        audioMapping: {
            2: AudioId.GOOD_2,
            3: AudioId.GREAT_2,
            4: AudioId.EXCELLENT_2,
            5: AudioId.EXCELLENT_2,
            6: AudioId.AMAZING_2,
            7: AudioId.UNBELIEVABLE_2,
        },
        defaultAudioId: AudioId.GOOD_2,
        audioId: AudioId.GOOD_2,
        delay: 0,
    } as IShowMultiClearWordRuleConfig,
    rule_show_target_info: {
        ruleId: 'rule_show_target_info',
        ruleDesc: '关卡目标信息显示',
        ruleType: ECSRuleConst.ShowTargetInfoRule,
        delay: 0,
    },
    rule_multiyclear_effect: {
        ruleId: 'rule_multiyclear_effect',
        ruleDesc: '层层叠叠清除特效规则',
        ruleType: ECSRuleConst.ClearEffectECSRule,
        delay: 0,
        aniName: { 1: 'blue', 5: 'red', 2: 'yellow', 6: 'green', 3: 'purple', 4: 'orange', 7: 'wathetblue' },
        eliminateEcsViewId: 'ecsview_simple_effect',
    } as IClearEffectECSRuleConfig,
    rule_single_score_effect: {
        ruleId: 'rule_single_score_effect',
        ruleDesc: '单次分数展示特效规则',
        ruleType: ECSRuleConst.ScoreEffectECSRule,
        ecsViewId: 'ecsview_single_score_effect',
        delay: 0,
    } as ISingleScoreEffectECSRuleConfig,
    rule_revive: {
        ruleId: 'rule_revive',
        ruleDesc: '复活规则',
        ruleType: ECSRuleConst.ReviveRule,
        ecsViewId: 'ecsview_revive',
        percent: 0.5,
        delay: 0,
    } as IReviveRuleConfig,
    rule_game_collect_fail: {
        ruleId: 'rule_game_collect_fail',
        ruleDesc: '游戏失败规则',
        ruleType: ECSRuleConst.ResultRule,
        ecsViewId: 'ecsview_collect_fail',
        delay: 0,
    } as IResultRuleConfig,

    rule_game_score_fail: {
        ruleId: 'rule_game_score_fail',
        ruleDesc: '分数关卡失败规则',
        ruleType: ECSRuleConst.ResultRule,
        ecsViewId: 'ecsview_score_fail',
        delay: 0,
    } as IResultRuleConfig,
    rule_elimination_score_calculator: {
        ruleId: 'rule_elimination_score_calculator',
        ruleDesc: '消除分数计算规则',
        ruleType: ECSRuleConst.EliminationScoreCalculatorRule,
        delay: 0,
    } as IECSRuleConfig,
    rule_put_block_score_calculator: {
        ruleId: 'rule_put_block_score_calculator',
        ruleDesc: '放置块分数计算规则',
        ruleType: ECSRuleConst.PutBlockScoreCalculatorRule,
        delay: 0,
    } as IECSRuleConfig,
    rule_smart_produce_block: {
        ruleId: 'rule_smart_produce_block',
        ruleDesc: '智能生产块规则',
        ruleType: ECSRuleConst.SmartProduceBlockRule,
        delay: 0,
    } as IECSRuleConfig,
    rule_cell_removed: {
        ruleId: 'rule_cell_removed',
        ruleDesc: '块被移除规则',
        ruleType: ECSRuleConst.CellRemovedRule,
        delay: 0,
    } as IECSRuleConfig,
    rule_game_score_win: {
        ruleId: 'rule_game_score_win',
        ruleDesc: '分数关卡成功规则',
        ruleType: ECSRuleConst.ResultRule,
        ecsViewId: 'ecsview_score_win',
        delay: 0,
    } as IResultRuleConfig,
    rule_game_collect_win: {
        ruleId: 'rule_game_collect_win',
        ruleDesc: '收集物关卡成功规则',
        ruleType: ECSRuleConst.ResultRule,
        ecsViewId: 'ecsview_collect_win',
        delay: 0,
    } as IResultRuleConfig,
    rule_game_score_hard_win: {
        ruleId: 'rule_game_score_hard_win',
        ruleDesc: '收集物关卡困难成功规则',
        ruleType: ECSRuleConst.ResultRule,
        ecsViewId: 'ecsview_score_hard_win',
        delay: 0,
    } as IResultRuleConfig,
    rule_drag_multiplier: {
        ruleId: 'rule_drag_multiplier',
        ruleDesc: '拖动倍率规则',
        ruleType: ECSRuleConst.ConfigECSRule,
        tableKey: ConfigTableConst.SystemConfig,
        configId: 'system_drag_block',
        configKey: 'dragMultiplier',
        configVal: 1.4,
        delay: 0,
    } as IConfigECSRuleConfig,
    rule_expand_hot_area: {
        ruleId: 'rule_expand_hot_area',
        ruleDesc: '扩大热区规则',
        ruleType: ECSRuleConst.ExpandHotAreaRule,
        expandMultiplier: 1.5,
        originalWidth: 600,
        originalHeight: 1200,
        delay: 0,
    } as IExpandHotAreaRuleConfig,
    rule_win_streak001: {
        ruleId: 'rule_win_streak001',
        ruleDesc: '连胜001规则',
        ruleType: ECSRuleConst.WinStreak001Rule,
        ecsViewId: 'ecsview_win_streak001',
        initialWinNum: 0,
        autoShow: false,
        winStreakCountKey: 'winStreakCount',
        delay: 0,
    } as IWinStreak001RuleConfig,
    rule_drag_effect_audio_play: {
        ruleId: 'rule_drag_effect_audio_play',
        ruleDesc: '拖动音效播放规则',
        ruleType: ECSRuleConst.DragEffectAudioPlayRule,
        audioId: AudioId.S_TOUCH_AUDIO,
        delay: 0,
    } as IDragEffectAudioRuleConfig,
    rule_combo_effect_audio_play: {
        ruleId: 'rule_combo_effect_audio_play',
        ruleDesc: '连击音效播放规则',
        ruleType: ECSRuleConst.ComboEffectAudioPlayRule,
        audioMapping: {
            1: AudioId.E_SCORE_STREAK_1,
            2: AudioId.E_SCORE_STREAK_2,
            3: AudioId.E_SCORE_STREAK_3,
            4: AudioId.E_SCORE_STREAK_4,
            5: AudioId.E_SCORE_STREAK_5,
            6: AudioId.E_SCORE_STREAK_6,
            7: AudioId.E_SCORE_STREAK_7,
            8: AudioId.E_SCORE_STREAK_8,
            9: AudioId.E_SCORE_STREAK_9,
            10: AudioId.E_SCORE_STREAK_10,
        },
        defaultAudioId: AudioId.E_SCORE_STREAK_1,
        delay: 0,
    } as IComboEffectAudioRuleConfig,
    rule_motivational_effect_audio_play: {
        ruleId: 'rule_motivational_effect_audio_play',
        ruleDesc: '激励语音播放规则',
        ruleType: ECSRuleConst.MotivationalEffectAudioPlayRule,
        motivationalMapping: {
            2: AudioId.GOOD_2,
            3: AudioId.GREAT_2,
            4: AudioId.EXCELLENT_2,
            5: AudioId.EXCELLENT_2,
            6: AudioId.AMAZING_2,
            7: AudioId.UNBELIEVABLE_2,
        },
        defaultMotivationalId: AudioId.GOOD_2,
        minTriggerCount: 2,
        delay: 0,
    } as IMotivationalEffectAudioRuleConfig,
    rule_put_effect_audio_play: {
        ruleId: 'rule_put_effect_audio_play',
        ruleDesc: '放置音效播放规则',
        ruleType: ECSRuleConst.PutEffectAudioPlayRule,
        audioId: AudioId.BLOCK_PUT_AUDIO,
        delay: 0,
    } as IPutEffectAudioRuleConfig,
    rule_adaptive_preview: {
        ruleId: 'rule_adaptive_preview',
        ruleDesc: '自适应预览规则',
        ruleType: ECSRuleConst.AdaptivePreviewRule,
        toleranceMultiplier: 1,
        minFreeSpacesThreshold: 2,
        delay: 0,
    } as IAdaptivePreviewRuleConfig,
    rule_multi_stage_progress_algorithm: {
        ruleId: 'rule_multi_stage_progress_algorithm',
        ruleDesc: '多阶段进度算法规则',
        ruleType: ECSRuleConst.MultiStageProgressAlgorithmRule,
        algorithmIdList: [
            { percent: 0, algorithmId: AlgoClassTypeConst.algo_fill_in_the_blank_journey },
            { percent: 0.4, algorithmId: AlgoClassTypeConst.algo_fill_in_the_blank_journey },
            { percent: 0.8, algorithmId: AlgoClassTypeConst.algo_fill_in_the_blank_journey },
        ],
        delay: 0,
    } as IMultiStageProgressBlockAlgorithmRuleConfig,
    rule_init_board_scene_colors: {
        ruleId: 'rule_init_board_scene_colors',
        ruleDesc: '初始化方块颜色规则',
        ruleType: ECSRuleConst.InitBoardSceneColorsRule,
        colors: NormalColor7,
        delay: 0,
    } as IInitBoardColorsRuleConfig,
    rule_decrease_target: {
        ruleId: 'rule_decrease_target',
        ruleDesc: '减少目标规则',
        ruleType: ECSRuleConst.DecreaseTargetRule,
        offlineDuration: 1000,
        decreaseRatio: 0.6,
        delay: 0,
    } as IDecreaseTargetRuleConfig,
    rule_vibration_android_native: {
        ruleId: 'rule_vibration_android_native',
        ruleDesc: 'Android原生震动规则',
        ruleType: ECSRuleConst.VibrationAndroidNativeRule,
        strength_level: 200,
        duration_time_second: 150,
        delay: 0,
    } as IVibrationAndroidNativeRuleConfig,
    rule_init_multi_elimination_special_effect: {
        ruleId: 'rule_init_multi_elimination_special_effect',
        ruleDesc: '初始化多消特效规则',
        ruleType: ECSRuleConst.InitMultiEliminationSpecialEffectRule,
        ecsViewId: 'ecsview_multi_elimination_special_effect',
        delay: 0,
    } as IECSRuleConfig,
    rule_win_streak_add: {
        ruleId: 'rule_win_streak_add',
        ruleDesc: '连胜增加规则',
        ruleType: ECSRuleConst.WinStreakAddRule,
        winStreakCountKey: 'winStreakCount',
        delay: 0,
    } as IWinStreakAddRuleConfig,
    rule_win_streak_clear: {
        ruleId: 'rule_win_streak_clear',
        ruleDesc: '连胜清除规则',
        ruleType: ECSRuleConst.WinStreakClearRule,
        winStreakCountKey: 'winStreakCount',
        delay: 0,
    } as IWinStreakClearRuleConfig,
    rule_produce_block_shape: {
        ruleId: 'rule_produce_block_shape',
        ruleDesc: '生成块形状规则',
        ruleType: ECSRuleConst.ProduceBlockShapeRule,
    } as IECSRuleConfig,
    rule_produce_block_color: {
        ruleId: 'rule_produce_block_color',
        ruleDesc: '生成块颜色规则',
        ruleType: ECSRuleConst.ProduceBlockColorRule,
    } as IECSRuleConfig,
    rule_produce_block_collectible: {
        ruleId: 'rule_produce_block_collectible',
        ruleDesc: '生成块收集物规则',
        ruleType: ECSRuleConst.ProduceBlockCollectibleRule,
    } as IECSRuleConfig,
    rule_produce_block_create_entity: {
        ruleId: 'rule_produce_block_create_entity',
        ruleDesc: '生成块创建实体规则',
        ruleType: ECSRuleConst.ProduceBlockCreateEntityRule,
        ecsViewId: 'ecsview_produce_block_create_entity',
        delay: 0,
    } as IECSRuleConfig,
    rule_board_refurbish_effect: {
        ruleId: 'rule_board_refurbish_effect',
        ruleDesc: '方块刷新动画规则',
        ruleType: ECSRuleConst.BoardRefurbishEffectRule,
        ecsViewId: 'ecsview_board_refurbish_effect',
        delay: 0,
    } as IECSRuleConfig,
    rule_check_thumbs_up_state: {
        ruleId: 'rule_check_thumbs_up_state',
        ruleDesc: '点赞状态检查规则',
        ruleType: ECSRuleConst.CheckThumbsUpStateRule,
    } as IECSRuleConfig,
    rule_show_thumbs_up_effect: {
        ruleId: 'rule_show_thumbs_up_effect',
        ruleDesc: '点赞特效展示规则',
        ruleType: ECSRuleConst.ShowThumbsUpEffectRule,
    } as IECSRuleConfig,
    rule_low_performance_device_res_adapt: {
        ruleId: 'rule_low_performance_device_res_adapt',
        ruleDesc: '低性能设备资源适配规则',
        ruleType: ECSRuleConst.LowPerformanceDeviceResAdaptRule,
        lowPerformanceResourceConfig: {
            'advres,dragonbones/eliminate/gameplay_word_tex': 'advres,dragonbones/eliminate/gameplay_word_small_tex',
            'advres,dragonbones/eliminate/gameplay_word_ske': 'advres,dragonbones/eliminate/gameplay_word_small_ske',
        },
        delay: 0,
    } as ILowPerformanceDeviceResAdaptRuleConfig,
    rule_game_over_pre_handle: {
        ruleId: 'rule_game_over_pre_handle',
        ruleDesc: '游戏结束前处理规则',
        ruleType: ECSRuleConst.GameOverPreHandleRule,
        delayTime: 1,
        cutdownTag: 'gameOverPreHandle',
        delay: 0,
    } as IGameOverPreHandleRuleConfig,
    rule_game_over_countdown_delay: {
        ruleId: 'rule_game_over_countdown_delay',
        ruleDesc: '游戏结束倒计时延迟规则',
        ruleType: ECSRuleConst.GameOverCountdownDelayRule,
        delayTime: 10,
        cutdownTag: 'gameOverPreHandle',
        delay: 0,
    } as IGameOverCountdownDelayRuleConfig,
    rule_game_over_countdown_end_handle: {
        ruleId: 'rule_game_over_countdown_end_handle',
        ruleDesc: '游戏结束倒计时结束处理规则',
        ruleType: ECSRuleConst.GameOverCountdownEndHandleRule,
        cutdownTag: 'gameOverPreHandle',
        delay: 0,
    } as IGameOverCountdownEndHandleRuleConfig,
};
