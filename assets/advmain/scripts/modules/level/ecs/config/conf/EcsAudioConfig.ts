/**
 * 音效ID枚举
 */
export enum AudioId {
    // ==================== EFFECT音效 ====================
    /** 按钮点击音效 */
    S_BUTTON_AUDIO = 'audio_s_button',
    /** 按钮显示音效 */
    S_BTN_SHOW_AUDIO = 'audio_s_btnShow',
    /** 方块拖拽开始音效 */
    S_TOUCH_AUDIO = 'audio_s_touch',
    /** 站点按钮音效 */
    SITE_BUTTON_AUDIO = 'audio_site_button',
    /** 数字音效 */
    NUMBER_AUDIO = 'audio_number',
    /** 时间音效 */
    TIME_AUDIO = 'audio_time',
    /** 方块放置音效 */
    BLOCK_PUT_AUDIO = 'audio_block_put',
    /** 连击音效1 */
    E_SCORE_STREAK_1 = 'audio_e_score_streak_1',
    /** 连击音效2 */
    E_SCORE_STREAK_2 = 'audio_e_score_streak_2',
    /** 连击音效3 */
    E_SCORE_STREAK_3 = 'audio_e_score_streak_3',
    /** 连击音效4 */
    E_SCORE_STREAK_4 = 'audio_e_score_streak_4',
    /** 连击音效5 */
    E_SCORE_STREAK_5 = 'audio_e_score_streak_5',
    /** 连击音效6 */
    E_SCORE_STREAK_6 = 'audio_e_score_streak_6',
    /** 连击音效7 */
    E_SCORE_STREAK_7 = 'audio_e_score_streak_7',
    /** 连击音效8 */
    E_SCORE_STREAK_8 = 'audio_e_score_streak_8',
    /** 连击音效9 */
    E_SCORE_STREAK_9 = 'audio_e_score_streak_9',
    /** 连击音效10 */
    E_SCORE_STREAK_10 = 'audio_e_score_streak_10',
    /** 优秀音效2 */
    EXCELLENT_2 = 'audio_excellent_2',
    /** 好音效2 */
    GOOD_2 = 'audio_good_2',
    /** 很棒音效 */
    GREAT_2 = 'audio_great_2',
    /** 非常棒音效 */
    AMAZING_2 = 'audio_amazing_2',
    /** 难以置信音效 */
    UNBELIEVABLE_2 = 'audio_unbelievable_2',
    /** 收集物品音效 */
    TRAVEL_COLLECT_ITEMS = 'audio_travel_collect_items',
    /** 收集物品音效1 */
    TRAVEL_GAME_COLLECT_ITEM1 = 'audio_travel_game_collect_item1',
    /** 收集物品音效2 */
    TRAVEL_GAME_COLLECT_ITEM2 = 'audio_travel_game_collect_item2',
    /** 胜利音效 */
    TRAVEL_WIN_LOGO = 'audio_travel_win_logo',
    /** 点赞音效1 */
    THUMBS_UP_1 = 'audio_put_thumb1',
    /** 点赞音效2 */
    THUMBS_UP_2 = 'audio_put_thumb2',
    /** 点赞音效3 */
    THUMBS_UP_3 = 'audio_put_thumb3',
    /** 点赞音效4 */
    THUMBS_UP_4 = 'audio_put_thumb4',
    // ==================== SOUND音效 ====================
    /** 通用背景音乐 */
    COMMON_BGM_AUDIO = 'audio_bgm_commonBgm',
}
/**
 * 本地音效类型常量 - 避免模块加载时 falcon.AudioType 未初始化的问题
 */
export const AUDIO_TYPE = {
    SOUND: 0 as falcon.AudioType, // 对应 falcon.AudioType.SOUND
    EFFECT: 1 as falcon.AudioType, // 对应 falcon.AudioType.EFFECT
};
/**
 * ECS音效配置常量
 */
export const EcsAudioConfig: Record<AudioId, falcon.IAudioInfoOption> = {
    // ==================== EFFECT音效 ====================
    [AudioId.S_BUTTON_AUDIO]: {
        url: 'audios/s_button',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.S_BTN_SHOW_AUDIO]: {
        url: 'audios/s_btnShow',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.S_TOUCH_AUDIO]: {
        url: 'audios/s_touch',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.SITE_BUTTON_AUDIO]: {
        url: 'audios/site_button',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.NUMBER_AUDIO]: {
        url: 'audios/number',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.TIME_AUDIO]: {
        url: 'audios/time',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.BLOCK_PUT_AUDIO]: {
        url: 'audios/s_put',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_1]: {
        url: 'audios/e_score_streak_1',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_2]: {
        url: 'audios/e_score_streak_2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_3]: {
        url: 'audios/e_score_streak_3',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_4]: {
        url: 'audios/e_score_streak_4',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_5]: {
        url: 'audios/e_score_streak_5',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_6]: {
        url: 'audios/e_score_streak_6',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_7]: {
        url: 'audios/e_score_streak_7',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_8]: {
        url: 'audios/e_score_streak_8',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_9]: {
        url: 'audios/e_score_streak_9',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.E_SCORE_STREAK_10]: {
        url: 'audios/e_score_streak_10',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.EXCELLENT_2]: {
        url: 'audios/Excellent2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.GOOD_2]: {
        url: 'audios/Good2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.GREAT_2]: {
        url: 'audios/Great2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.AMAZING_2]: {
        url: 'audios/amazing2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.UNBELIEVABLE_2]: {
        url: 'audios/unbelievable2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.TRAVEL_COLLECT_ITEMS]: {
        url: 'audios/travel_overui_collect_items',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.TRAVEL_GAME_COLLECT_ITEM1]: {
        url: 'audios/travel_game_collect_item1',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.TRAVEL_GAME_COLLECT_ITEM2]: {
        url: 'audios/travel_game_collect_item2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.TRAVEL_WIN_LOGO]: {
        url: 'audios/travel_win_logo',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.THUMBS_UP_1]: {
        url: 'audios/put_thumb1',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.THUMBS_UP_2]: {
        url: 'audios/put_thumb2',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.THUMBS_UP_3]: {
        url: 'audios/put_thumb3',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    [AudioId.THUMBS_UP_4]: {
        url: 'audios/put_thumb4',
        volume: 1,
        bundleName: 'advres',
        type: AUDIO_TYPE.EFFECT,
    } as falcon.IAudioInfoOption,
    // ==================== SOUND音效 ====================
    [AudioId.COMMON_BGM_AUDIO]: {
        url: 'audios/bgm/commonBgm',
        volume: 0.5,
        bundleName: 'advres',
        type: AUDIO_TYPE.SOUND,
    } as falcon.IAudioInfoOption,
};
/**
 * 音效配置工具类
 */
export class EcsAudioConfigHelper {
    /**
     * 根据音效ID获取配置
     * @param audioId 音效ID
     * @returns 音效配置
     */
    static getAudioConfig(audioId: AudioId | string): falcon.IAudioInfoOption | null {
        return EcsAudioConfig[audioId as AudioId] || null;
    }
    /**
     * 获取指定类型的所有音效配置
     * @param type 音效类型
     * @returns 音效配置数组
     */
    static getAudioConfigsByType(type: falcon.AudioType): falcon.IAudioInfoOption[] {
        return Object.values(EcsAudioConfig).filter((config) => config.type === type);
    }
    /**
     * 获取所有音效配置ID
     * @returns 音效ID数组
     */
    static getAllAudioIds(): AudioId[] {
        return Object.keys(EcsAudioConfig) as AudioId[];
    }
    /**
     * 验证音效配置是否完整
     * @param config 音效配置
     * @returns 验证结果
     */
    static validateAudioConfig(config: falcon.IAudioInfoOption): {
        valid: boolean;
        errors: string[];
    } {
        const errors: string[] = [];
        if (!config.url) {
            errors.push('url is required');
        }
        if (config.volume < 0 || config.volume > 1) {
            errors.push('volume must be between 0 and 1');
        }
        if (config.type !== AUDIO_TYPE.SOUND && config.type !== AUDIO_TYPE.EFFECT) {
            errors.push('invalid audio type');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    /**
     * 创建音效配置的副本
     * @param config 原始配置
     * @param overrides 覆盖属性
     * @returns 新的音效配置
     */
    static createAudioConfigCopy(config: falcon.IAudioInfoOption, overrides: Partial<falcon.IAudioInfoOption>): falcon.IAudioInfoOption {
        return {
            ...config,
            ...overrides,
        };
    }
}
