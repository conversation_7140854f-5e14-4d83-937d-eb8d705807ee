import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先触发出块计算并标记为预览模式
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;
        this.eventData;
        const col = 5;
        const row = 2;
        "level/revive/clear_tips_node"
        console.log('RevivePreviewBlocksRule: 复活预览块规则', this.eventData);
    }
}
