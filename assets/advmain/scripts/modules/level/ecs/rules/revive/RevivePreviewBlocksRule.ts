import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import BoardComponent from '../../components/board/BoardComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import NodeComponent from '../../components/NodeComponent';
import { NormalColor7 } from '../../define/BoardDefine';
import { RcShape } from '../../define/EcsConfig';



export interface IReviveBlockRuleConfig extends IECSRuleConfig {
    /**复活特效视图id */
    ecsViewId: EcsViewId;
    /**行索引 */
    row: number;
    /**列索引 */
    col: number;
}

/**
 * 复活块规则
 * 创建视图播放行列特效
 */
export class RevivePreviewBlockRule extends ECSRuleBase<IReviveBlockRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    async execute(): Promise<void> {
        const world = this.world;

        // 获取棋盘实体和组件
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;

        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;

        // 1. 准备块显示数据
        let blockDisplayData: any[] = [];
        console.log('RevivePreviewBlockRule: 准备块显示数据', this.eventData);
        if (this.eventData && typeof this.eventData === 'object' && 'blockIds' in this.eventData && 'blockPoses' in this.eventData) {
            const eventData = this.eventData as any;
            console.log('RevivePreviewBlockRule: blockIds:', eventData.blockIds);
            console.log('RevivePreviewBlockRule: blockPoses:', eventData.blockPoses);
            blockDisplayData = this.prepareBlockDisplayData(boardEntity, eventData.blockIds, eventData.blockPoses);
        } else {
            console.log('RevivePreviewBlockRule: 没有块数据或数据不完整', {
                hasEventData: !!this.eventData,
                eventData: this.eventData
            });
        }

        // 2. 计算特效位置数据
        const effectData = this.calculateEffectPositions(boardEntity, boardComp);

        // 3. 添加块显示数据到特效数据中
        effectData.blockDisplayData = blockDisplayData;

        // 4. 创建特效视图实体
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: {
                    isBreakLink: true,
                    data: effectData
                },
            },
        );

        // 立即销毁实体，视图会继续存在直到特效播放完成
        world.destroyEntity(effectEntity);
    }

    /**
     * 获取棋盘实体
     */
    private getBoardEntity(): number | null {
        const boardEntities = this.world.query([BoardComponent]);
        return boardEntities.length > 0 ? boardEntities[0] : null;
    }

    /**
     * 计算特效位置数据
     */
    private calculateEffectPositions(boardEntity: number, boardComp: BoardComponent): any {
        const world = this.world;
        const effectData: any = {};

        let rows: number[] = [];
        let cols: number[] = [];

        if (this.eventData && typeof this.eventData === 'object' && 'blockIds' in this.eventData && 'blockPoses' in this.eventData) {
            const eventData = this.eventData as any;
            if (eventData.blockIds?.length > 0 && eventData.blockPoses?.length > 0) {
                const blockId = eventData.blockIds[1];
                const blockPos = eventData.blockPoses[1];
                const rcShape = RcShape[blockId];

                if (rcShape) {
                    const boardOccupy = this.world.utilCenter.boardUtil.getBoardOccupy(boardEntity);
                    const clearResult = this.world.utilCenter.boardUtil.canClearRowCol(rcShape, boardOccupy, blockPos);
                    rows = clearResult.rows;
                    cols = clearResult.cols;
                }
            }
        }
        console.log("rows", rows);
        console.log("cols", cols);

        // 计算行特效数据 - 处理多行消除
        if (rows && rows.length > 0) {
            effectData.rowEffects = [];
            for (const rowIndex of rows) {
                const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${rowIndex}_0`)[0];
                const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${rowIndex}_${boardComp.cCount - 1}`)[0];

                if (startSlotEntity && endSlotEntity) {
                    const startNode = world.getComponent(startSlotEntity, NodeComponent);
                    const endNode = world.getComponent(endSlotEntity, NodeComponent);

                    if (startNode && endNode) {
                        effectData.rowEffects.push({
                            x: (startNode.x + endNode.x) / 2,
                            y: startNode.y,
                            angle: 0,
                            scaleX: boardComp.cCount / 8,
                            boardEntity: boardEntity,
                            rowIndex: rowIndex
                        });
                    }
                }
            }
        }

        // 计算列特效数据 - 处理多列消除
        if (cols && cols.length > 0) {
            effectData.colEffects = [];
            for (const colIndex of cols) {
                const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `0_${colIndex}`)[0];
                const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${colIndex}`)[0];

                if (startSlotEntity && endSlotEntity) {
                    const startNode = world.getComponent(startSlotEntity, NodeComponent);
                    const endNode = world.getComponent(endSlotEntity, NodeComponent);

                    if (startNode && endNode) {
                        effectData.colEffects.push({
                            x: startNode.x,
                            y: (startNode.y + endNode.y) / 2,
                            angle: 90,
                            scaleX: boardComp.rCount / 8,
                            boardEntity: boardEntity,
                            colIndex: colIndex
                        });
                    }
                }
            }
        }



        return effectData;
    }

    /**
     * 准备块显示数据（不创建真实实体，只准备显示信息）
     */
    private prepareBlockDisplayData(boardEntity: number, blockIds: number[], blockPoses: any[]): any[] {
        const world = this.world;
        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return [];

        console.log('RevivePreviewBlockRule: 开始准备块显示数据');
        console.log('RevivePreviewBlockRule: blockIds 数组:', blockIds);
        console.log('RevivePreviewBlockRule: blockPoses 数组:', blockPoses);


        const blockDisplayData: any[] = [];
        const blockId = blockIds[1];
        const rcShape = RcShape[blockId];
        const pos = blockPoses[1];

        // 检查形状数据是否存在
        if (!rcShape || !rcShape.shape || !rcShape.shape.length) {
            console.warn(`RevivePreviewBlockRule: 找不到形状数据 blockId=${blockId}`);
        }

        // 检查位置数据的有效性，支持 {r,c} 和 {row,col} 两种格式
        if (!pos || (pos.r === undefined && pos.row === undefined) || (pos.c === undefined && pos.col === undefined)) {
            console.warn(`RevivePreviewBlockRule: 位置数据无效`, pos);
        }

        // 统一位置数据格式 - 这是形状的基准位置
        const baseR = pos.r !== undefined ? pos.r : pos.row;
        const baseC = pos.c !== undefined ? pos.c : pos.col;

        // 随机颜色（整个形状使用同一颜色）
        const randomColor = NormalColor7[Math.floor(Math.random() * NormalColor7.length)];

        // 根据形状信息创建每个小块的显示数据
        for (const shapeCell of rcShape.shape) {
            // 计算实际位置：基准位置 + 形状偏移
            const actualR = baseR + shapeCell.r;
            const actualC = baseC + shapeCell.c;

            // 获取对应位置的格子实体
            const slotKey = `${actualR}_${actualC}`;
            console.log(`RevivePreviewBlockRule: 查找格子实体，key=${slotKey}`);
            const slotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, slotKey)[0];
            if (!slotEntity) {
                console.warn(`RevivePreviewBlockRule: 找不到位置 ${actualR}_${actualC} 的格子实体`);
                continue;
            }

            // 获取格子的位置信息
            const slotNodeComp = world.getComponent(slotEntity, NodeComponent);
            if (!slotNodeComp) {
                console.warn(`RevivePreviewBlockRule: 找不到位置 ${actualR}_${actualC} 的节点组件`);
                continue;
            }

            // 准备块显示数据
            const blockData = {
                blockId: blockId,
                shapeIndex: 1,
                cellIndex: rcShape.shape.indexOf(shapeCell),
                position: { r: actualR, c: actualC },
                worldPosition: { x: slotNodeComp.x, y: slotNodeComp.y },
                color: randomColor,
                boardEntity: boardEntity
            };

            blockDisplayData.push(blockData);

            console.log(`RevivePreviewBlockRule: 准备位置 ${actualR}_${actualC} 的块显示数据，形状=${blockId}, 颜色=${randomColor}`);
        }

        return blockDisplayData;
    }


}
