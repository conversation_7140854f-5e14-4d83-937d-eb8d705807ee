import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import BoardComponent from '../../components/board/BoardComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import NodeComponent from '../../components/NodeComponent';
import { CellType, TargetType, NormalColor7 } from '../../define/BoardDefine';
import { CellOption } from '../../template/CellTemplete';


export interface IReviveBlockRuleConfig extends IECSRuleConfig {
    /**复活特效视图id */
    ecsViewId: EcsViewId;
    /**行索引 */
    row: number;
    /**列索引 */
    col: number;
}

/**
 * 复活块规则
 * 创建视图播放行列特效
 */
export class RevivePreviewBlockRule extends ECSRuleBase<IReviveBlockRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;

        // 获取棋盘实体和组件
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;

        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;

        // 1. 创建块
        console.log('RevivePreviewBlockRule: 创建块', this.eventData);
        if (this.eventData && this.eventData.blockIds && this.eventData.blockPoses) {
            console.log('RevivePreviewBlockRule: blockIds:', this.eventData.blockIds);
            console.log('RevivePreviewBlockRule: blockPoses:', this.eventData.blockPoses);
            this.createBlocks(boardEntity, this.eventData.blockIds, this.eventData.blockPoses);
        } else {
            console.log('RevivePreviewBlockRule: 没有块数据或数据不完整', {
                hasEventData: !!this.eventData,
                hasBlockIds: !!(this.eventData && this.eventData.blockIds),
                hasBlockPoses: !!(this.eventData && this.eventData.blockPoses),
                eventData: this.eventData
            });
        }

        // 2. 计算特效位置数据
        const effectData = this.calculateEffectPositions(boardEntity, boardComp);

        // 3. 创建特效视图实体
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: {
                    isBreakLink: true,
                    data: effectData
                },
            },
        );

        // 立即销毁实体，视图会继续存在直到特效播放完成
        world.destroyEntity(effectEntity);
        
        console.log('RevivePreviewBlockRule: 创建复活特效视图', this.config);
    }

    /**
     * 获取棋盘实体
     */
    private getBoardEntity(): number | null {
        const boardEntities = this.world.query([BoardComponent]);
        return boardEntities.length > 0 ? boardEntities[0] : null;
    }

    /**
     * 计算特效位置数据
     */
    private calculateEffectPositions(boardEntity: number, boardComp: BoardComponent): any {
        const world = this.world;
        const effectData: any = {};

        // 计算行特效数据
        if (this.config.row !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_0`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_${boardComp.cCount - 1}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.rowEffect = {
                        x: (startNode.x + endNode.x) / 2,
                        y: startNode.y,
                        angle: 0,
                        scaleX: boardComp.cCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        // 计算列特效数据
        if (this.config.col !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `0_${this.config.col}`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${this.config.col}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.colEffect = {
                        x: startNode.x,
                        y: (startNode.y + endNode.y) / 2,
                        angle: 90,
                        scaleX: boardComp.rCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        return effectData;
    }

    /**
     * 创建块
     */
    private createBlocks(boardEntity: number, blockIds: number[], blockPoses: any[]): void {
        const world = this.world;
        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;
        
        console.log('RevivePreviewBlockRule: 开始 创建块');
        console.log('RevivePreviewBlockRule: blockIds 数组:', blockIds);
        console.log('RevivePreviewBlockRule: blockPoses 数组:', blockPoses);

        // 确保 blockIds 和 blockPoses 数组长度一致
        const minLength = Math.min(blockIds.length, blockPoses.length);
        console.log('RevivePreviewBlockRule: 将创建', minLength, '个块');

        for (let i = 0; i < minLength; i++) {
            const blockId = blockIds[i];
            const pos = blockPoses[i];

            console.log(`RevivePreviewBlockRule: 处理第${i}个块, blockId=${blockId}, pos=`, pos);

            // 检查位置数据的有效性，支持 {r,c} 和 {row,col} 两种格式
            if (!pos || (pos.r === undefined && pos.row === undefined) || (pos.c === undefined && pos.col === undefined)) {
                console.warn(`RevivePreviewBlockRule: 位置数据无效`, pos);
                continue;
            }
            
            // 统一位置数据格式
            const r = pos.r !== undefined ? pos.r : pos.row;
            const c = pos.c !== undefined ? pos.c : pos.col;

            // 获取对应位置的格子实体
            const slotKey = `${r}_${c}`;
            console.log(`RevivePreviewBlockRule: 查找格子实体，key=${slotKey}`);
            const slotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, slotKey)[0];
            if (!slotEntity) {
                console.warn(`RevivePreviewBlockRule: 找不到位置 ${r}_${c} 的格子实体`);
                continue;
            }
            console.log(`RevivePreviewBlockRule: 找到格子实体，ID=${slotEntity}`);

            // 随机颜色
            const randomColor = NormalColor7[Math.floor(Math.random() * NormalColor7.length)];

            // 创建完整的块选项 - 参考InteractionSystem的实现
            const cellOption: CellOption = {
                type: TargetType.Normal as CellType,
                color: randomColor
            };

            // 创建预览块实体，使用PreviewCell模板
            const blockEntity = world.templeteCenter.createTempleteEntity(
                TempleteType.CellRender,
                {
                    rc: { r: r, c: c },
                    parentSize: { width: boardComp.cCount, height: boardComp.rCount },
                    parentEntity: boardEntity,
                    slotEntity: slotEntity,
                    cellOption: cellOption
                }
            );

            console.log(`RevivePreviewBlockRule: 在位置 ${r}_${c} 创建预览块，ID=${blockId}, 颜色=${randomColor}, 实体ID=${blockEntity}`);
        }
    }
}
