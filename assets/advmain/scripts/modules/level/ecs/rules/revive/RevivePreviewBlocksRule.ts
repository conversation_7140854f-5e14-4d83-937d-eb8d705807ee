import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import BoardComponent from '../../components/board/BoardComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import NodeComponent from '../../components/NodeComponent';
import { CellType, TargetType, NormalColor7 } from '../../define/BoardDefine';
import { CellOption } from '../../template/CellTemplete';


export interface IReviveBlockRuleConfig extends IECSRuleConfig {
    /**复活特效视图id */
    ecsViewId: EcsViewId;
    /**行索引 */
    row: number;
    /**列索引 */
    col: number;
}

/**
 * 复活块规则
 * 创建视图播放行列特效
 */
export class RevivePreviewBlockRule extends ECSRuleBase<IReviveBlockRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;

        // this.eventData;

        // 获取棋盘实体和组件
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;

        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;

        // 1. 创建块
        console.log('RevivePreviewBlockRule: 创建块', this.eventData);
        if (this.eventData && this.eventData.blockIds && this.eventData.blockPoses) {
            this.createBlocks(boardEntity, this.eventData.blockIds, this.eventData.blockPoses);
        }

        // 2. 计算特效位置数据
        const effectData = this.calculateEffectPositions(boardEntity, boardComp);

        // 3. 创建特效视图实体
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: {
                    isBreakLink: true,
                    data: effectData
                },
            },
        );

        // 立即销毁实体，视图会继续存在直到特效播放完成
        world.destroyEntity(effectEntity);
        console.log('RevivePreviewBlockRule: 创建复活特效视图', this.config);
    }

    /**
     * 获取棋盘实体
     */
    private getBoardEntity(): number | null {
        const boardEntities = this.world.query([BoardComponent]);
        return boardEntities.length > 0 ? boardEntities[0] : null;
    }

    /**
     * 计算特效位置数据
     */
    private calculateEffectPositions(boardEntity: number, boardComp: BoardComponent): any {
        const world = this.world;
        const effectData: any = {};

        // 计算行特效数据
        if (this.config.row !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_0`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_${boardComp.cCount - 1}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.rowEffect = {
                        x: (startNode.x + endNode.x) / 2,
                        y: startNode.y,
                        angle: 0,
                        scaleX: boardComp.cCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        // 计算列特效数据
        if (this.config.col !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `0_${this.config.col}`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${this.config.col}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.colEffect = {
                        x: startNode.x,
                        y: (startNode.y + endNode.y) / 2,
                        angle: 90,
                        scaleX: boardComp.rCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        return effectData;
    }

    /**
     * 创建块
     */
    private createBlocks(boardEntity: number, blockIds: number[], blockPoses: { r: number, c: number }[]): void {
        const world = this.world;
        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;
        console.log('RevivePreviewBlockRule: 创建块', blockIds, blockPoses);
        // 确保 blockIds 和 blockPoses 数组长度一致
        const minLength = Math.min(blockIds.length, blockPoses.length);

        for (let i = 0; i < minLength; i++) {
            const blockId = blockIds[i];
            const pos = blockPoses[i];

            // 获取对应位置的格子实体
            const slotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${pos.r}_${pos.c}`)[0];
            if (!slotEntity) {
                console.warn(`RevivePreviewBlockRule: 找不到位置 ${pos.r}_${pos.c} 的格子实体`);
                continue;
            }

            // 随机颜色
            const randomColor = NormalColor7[Math.floor(Math.random() * NormalColor7.length)];

            // 创建块选项
            const cellOption: CellOption = {
                type: TargetType.Normal as CellType,
                color: randomColor,
                occupy: true,
                through: true
            };

            // 创建块实体
            world.templeteCenter.createTempleteEntity(
                TempleteType.Cell,
                {
                    rc: { r: pos.r, c: pos.c },
                    parentSize: { width: boardComp.cCount, height: boardComp.rCount },
                    parentEntity: boardEntity,
                    slotEntity: slotEntity,
                    cellOption: cellOption
                }
            );

            console.log(`RevivePreviewBlockRule: 在位置 ${pos.r}_${pos.c} 创建块，ID=${blockId}, 颜色=${randomColor}`);
        }
    }
}
