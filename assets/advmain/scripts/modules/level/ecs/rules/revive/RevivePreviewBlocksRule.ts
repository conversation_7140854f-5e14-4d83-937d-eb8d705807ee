import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import BoardComponent from '../../components/board/BoardComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import NodeComponent from '../../components/NodeComponent';


export interface IReviveBlockRuleConfig extends IECSRuleConfig {
    /**复活特效视图id */
    ecsViewId: EcsViewId;
    /**行索引 */
    row: number;
    /**列索引 */
    col: number;
}

/**
 * 复活块规则
 * 创建视图播放行列特效
 */
export class ReviveBlockRule extends ECSRuleBase<IReviveBlockRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;

        // 获取棋盘实体和组件
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;

        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;

        // 计算特效位置数据
        const effectData = this.calculateEffectPositions(boardEntity, boardComp);

        // 创建特效视图实体
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: {
                    isBreakLink: true,
                    data: effectData
                },
            },
        );

        // 立即销毁实体，视图会继续存在直到特效播放完成
        world.destroyEntity(effectEntity);
        console.log('ReviveBlockRule: 创建复活特效视图', this.config);
    }

    /**
     * 获取棋盘实体
     */
    private getBoardEntity(): number | null {
        const boardEntities = this.world.query([BoardComponent]);
        return boardEntities.length > 0 ? boardEntities[0] : null;
    }

    /**
     * 计算特效位置数据
     */
    private calculateEffectPositions(boardEntity: number, boardComp: BoardComponent): any {
        const world = this.world;
        const effectData: any = {};

        // 计算行特效数据
        if (this.config.row !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_0`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_${boardComp.cCount - 1}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.rowEffect = {
                        x: (startNode.x + endNode.x) / 2,
                        y: startNode.y,
                        angle: 0,
                        scaleX: boardComp.cCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        // 计算列特效数据
        if (this.config.col !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `0_${this.config.col}`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${this.config.col}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.colEffect = {
                        x: startNode.x,
                        y: (startNode.y + endNode.y) / 2,
                        angle: 90,
                        scaleX: boardComp.rCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        return effectData;
    }
}
