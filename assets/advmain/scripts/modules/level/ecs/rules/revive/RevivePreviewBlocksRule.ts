import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import BoardComponent from '../../components/board/BoardComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import NodeComponent from '../../components/NodeComponent';
import { NormalColor7 } from '../../define/BoardDefine';
import { RcShape } from '../../define/EcsConfig';


export interface IReviveBlockRuleConfig extends IECSRuleConfig {
    /**复活特效视图id */
    ecsViewId: EcsViewId;
    /**行索引 */
    row: number;
    /**列索引 */
    col: number;
}

/**
 * 复活块规则
 * 创建视图播放行列特效
 */
export class RevivePreviewBlockRule extends ECSRuleBase<IReviveBlockRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    async execute(): Promise<void> {
        const world = this.world;

        // 获取棋盘实体和组件
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;

        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;



        // 1. 准备块显示数据
        let blockDisplayData: any[] = [];
        console.log('RevivePreviewBlockRule: 准备块显示数据', this.eventData);
        if (this.eventData && typeof this.eventData === 'object' && 'blockIds' in this.eventData && 'blockPoses' in this.eventData) {
            const eventData = this.eventData as any;
            console.log('RevivePreviewBlockRule: blockIds:', eventData.blockIds);
            console.log('RevivePreviewBlockRule: blockPoses:', eventData.blockPoses);
            blockDisplayData = this.prepareBlockDisplayData(boardEntity, eventData.blockIds, eventData.blockPoses);
        } else {
            console.log('RevivePreviewBlockRule: 没有块数据或数据不完整', {
                hasEventData: !!this.eventData,
                eventData: this.eventData
            });
        }

        // 2. 计算特效位置数据
        const effectData = this.calculateEffectPositions(boardEntity, boardComp);

        // 3. 添加块显示数据到特效数据中
        effectData.blockDisplayData = blockDisplayData;

        // 4. 创建特效视图实体
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: {
                    isBreakLink: true,
                    data: effectData
                },
            },
        );

        // 立即销毁实体，视图会继续存在直到特效播放完成
        world.destroyEntity(effectEntity);
        
        console.log('RevivePreviewBlockRule: 创建复活特效视图', this.config);
    }

    /**
     * 获取棋盘实体
     */
    private getBoardEntity(): number | null {
        const boardEntities = this.world.query([BoardComponent]);
        return boardEntities.length > 0 ? boardEntities[0] : null;
    }

    /**
     * 计算特效位置数据
     */
    private calculateEffectPositions(boardEntity: number, boardComp: BoardComponent): any {
        const world = this.world;
        const effectData: any = {};

        // 计算行特效数据
        if (this.config.row !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_0`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${this.config.row}_${boardComp.cCount - 1}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.rowEffect = {
                        x: (startNode.x + endNode.x) / 2,
                        y: startNode.y,
                        angle: 0,
                        scaleX: boardComp.cCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        // 计算列特效数据
        if (this.config.col !== undefined) {
            const startSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `0_${this.config.col}`)[0];
            const endSlotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${this.config.col}`)[0];

            if (startSlotEntity && endSlotEntity) {
                const startNode = world.getComponent(startSlotEntity, NodeComponent);
                const endNode = world.getComponent(endSlotEntity, NodeComponent);

                if (startNode && endNode) {
                    effectData.colEffect = {
                        x: startNode.x,
                        y: (startNode.y + endNode.y) / 2,
                        angle: 90,
                        scaleX: boardComp.rCount / 8,
                        boardEntity: boardEntity
                    };
                }
            }
        }

        return effectData;
    }

    /**
     * 准备块显示数据（不创建真实实体，只准备显示信息）
     */
    private prepareBlockDisplayData(boardEntity: number, blockIds: number[], blockPoses: any[]): any[] {
        const world = this.world;
        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return [];

        console.log('RevivePreviewBlockRule: 开始准备块显示数据');
        console.log('RevivePreviewBlockRule: blockIds 数组:', blockIds);
        console.log('RevivePreviewBlockRule: blockPoses 数组:', blockPoses);

        // 确保 blockIds 和 blockPoses 数组长度一致

        // const minLength = Math.min(blockIds.length, blockPoses.length);
        // console.log('RevivePreviewBlockRule: 将准备', minLength, '个块的显示数据');

        const blockDisplayData: any[] = [];

        // for (let i = 0; i < minLength; i++) {
        const blockId = blockIds[1];
        const rcShape = RcShape[blockId];
        const pos = blockPoses[1];

        // console.log(`RevivePreviewBlockRule: 处理第${i}个块, blockId=${blockId}, pos=`, pos);

        // 检查位置数据的有效性，支持 {r,c} 和 {row,col} 两种格式
        if (!pos || (pos.r === undefined && pos.row === undefined) || (pos.c === undefined && pos.col === undefined)) {
            console.warn(`RevivePreviewBlockRule: 位置数据无效`, pos);
        }

        // 统一位置数据格式
        const r = pos.r !== undefined ? pos.r : pos.row;
        const c = pos.c !== undefined ? pos.c : pos.col;

        // 获取对应位置的格子实体
        const slotKey = `${r}_${c}`;
        console.log(`RevivePreviewBlockRule: 查找格子实体，key=${slotKey}`);
        const slotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, slotKey)[0];
        if (!slotEntity) {
            console.warn(`RevivePreviewBlockRule: 找不到位置 ${r}_${c} 的格子实体`);
            // continue;
        }

        // 获取格子的位置信息
        const slotNodeComp = world.getComponent(slotEntity, NodeComponent);
        if (!slotNodeComp) {
            console.warn(`RevivePreviewBlockRule: 找不到位置 ${r}_${c} 的节点组件`);
            // continue;
        }

        // 随机颜色
        const randomColor = NormalColor7[Math.floor(Math.random() * NormalColor7.length)];

        // 准备块显示数据
        const blockData = {
            blockId: blockId,
            position: { r: r, c: c },
            worldPosition: { x: slotNodeComp.x, y: slotNodeComp.y },
            color: randomColor,
            boardEntity: boardEntity
        };

        blockDisplayData.push(blockData);

        console.log(`RevivePreviewBlockRule: 准备位置 ${r}_${c} 的块显示数据，ID=${blockId}, 颜色=${randomColor}`);
        // }

        return blockDisplayData;
    // }
    }


}
