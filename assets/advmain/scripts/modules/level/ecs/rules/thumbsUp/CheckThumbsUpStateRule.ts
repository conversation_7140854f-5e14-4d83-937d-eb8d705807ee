import BoardComponent from '../../components/board/BoardComponent';
import { BoardScene } from '../../components/board/BoardScene';
import ShapeComponent from '../../components/board/ShapeComponent';
import { ThumbsUpComponent } from '../../components/ThumbsUpComponent';
import { ECSEvent, GameEventMap } from '../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

/**
 * 判断本次生成的三个块，是否存在无法直接放置
 * 如果不能直接放置，则标识一下，ShowThumbsUpStateRule会根据这个标识来决定是否显示点赞状态
 */
export class CheckThumbsUpStateRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    bindEventData(): keyof GameEventMap {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const world = this.world;
        // 如果有ThumbsUpComponent，则移除它
        const thumbsUpEntities = world.query([ThumbsUpComponent]);
        if (thumbsUpEntities.length > 0) {
            for (const entity of thumbsUpEntities) {
                world.removeEntity(entity);
            }
        }
        const sceneE = world.query([BoardScene])[0];
        const handler = world.utilCenter.boardUtil;
        const sceneComp = world.getComponent(sceneE, BoardScene);
        // 1. 取所有棋盘占用并集
        let boards = world.query([BoardComponent]).map((eid) => handler.getBoardOccupy(eid));
        const combinedBoardOccupy = handler.combineBoardOccupy(boards);

        if (combinedBoardOccupy.length === 0) return;
        if (sceneComp.waitPutCells.length === 0) {
            // 已经没有待放置块，系统会自动生成
            return;
        }
        if (!sceneE) return;
        const waitShapes = world.getComponent(sceneE, BoardScene).waitPutCells;
        // 检查是否有至少一个形状不可放置
        const hasAnyNotPut = waitShapes.some((shapeE) => {
            const sc = world.getComponent(shapeE, ShapeComponent);
            if (!sc) return true;
            return !handler.canPutShape(sc, combinedBoardOccupy);
        });

        // 如果有任何一个形状不能放置，则设置点赞状态
        if (hasAnyNotPut) {
            const entity = world.createEntity();
            world.addComponent(entity, ThumbsUpComponent);
        }
    }
}
