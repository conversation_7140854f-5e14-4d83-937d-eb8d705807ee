import BoardComponent from '../../components/board/BoardComponent';
import { BoardScene } from '../../components/board/BoardScene';
import ShapeComponent from '../../components/board/ShapeComponent';
import { ThumbsUpComponent } from '../../components/ThumbsUpComponent';
import { AudioId } from '../../config/conf/EcsAudioConfig';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import { BoardLayer } from '../../define/BoardDefine';
import { ECSEvent, GameEventMap } from '../../GameEvent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

/**
 * 当出的三个块存在无法直接放置时，从第一步开始只要玩家放置了仍有解就给予肯定
 */
export class ShowThumbsUpEffectRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PUT_BLOCK_BACK> {
    bindEventData(): keyof GameEventMap {
        return ECSEvent.GameEvent.PUT_BLOCK_BACK;
    }
    canExecute(): boolean {
        if (!this.eventData.isSuccess) {
            return false; // 只有放置成功时才执行
        }
        const thumbsUpComponent = this.world.query([ThumbsUpComponent]);
        if (!thumbsUpComponent || thumbsUpComponent.length === 0) {
            console.log('没有找到 ThumbsUpComponent 实体，无法执行 ShowThumbsUpEffectRule');
            return false;
        }
        return true;
    }
    execute(): void {
        const world = this.world;
        const sceneE = world.query([BoardScene])[0];
        const handler = world.utilCenter.boardUtil;
        const sceneComp = world.getComponent(sceneE, BoardScene);
        // 1. 取所有棋盘占用并集
        let boards = world.query([BoardComponent]).map((eid) => handler.getBoardOccupy(eid));
        const combinedBoardOccupy = handler.combineBoardOccupy(boards);

        if (combinedBoardOccupy.length === 0) return;
        if (sceneComp.waitPutCells.length === 0) {
            // 已经没有待放置块 最后一次放块，不播放动画
            return;
        }
        if (!sceneE) return;
        const waitShapes = sceneComp.waitPutCells;
        // 此时拿棋盘数据，已经是排放后的数据了，如果发生了消除，就是消除后的数据
        const allBoard = world.query([BoardComponent]);
        // 检查剩余形状是否可以放置
        let canPlaceAny = sceneComp.waitPutCells.some((shapeE) => {
            const shapeComp = world.getComponent(shapeE, ShapeComponent);
            if (!shapeComp) return false;
            return allBoard.some((boardE) => world.utilCenter.boardUtil.canShapePlace(world, boardE, shapeComp));
        });
        const boardEntity = this.world
            .query([BoardComponent])
            .find((eid) => this.world.getComponent(eid, BoardComponent).boardId === this.eventData.boardId);
        if (!canPlaceAny) {
            return; // 没有可放置的形状，直接返回
        }
        const times = 3 - waitShapes.length;
        const useTime = Date.now() - sceneComp.lastPutBlockTime;
        const info = this.eventData;
        const { animName, audioId } = this.getThumbsUpEffectData(times, useTime);

        // 获得cell数组
        for (const rc of info.shape.shape) {
            const r = info.y + rc.r;
            const c = info.x + rc.c;
            const slotE = this.world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            // 找到slotE的Cell
            const cellE = handler.getSlotOccupy(slotE);
            const parentEntity = cellE ? cellE : slotE;
            const effectEntity = world.templeteCenter.createTempleteEntity(
                TempleteType.Render,
                {
                    parentEntity: parentEntity,
                    ecsViewId: 'ecsview_simple_effect',
                },
                {
                    renderParam: {
                        data: {
                            aniName: animName,
                            src: 'dragonbones/thumbsUp/skeleton37_ske',
                            dragonNames: ['skeleton37_tex', 'armatureName'],
                            audioId: audioId,
                        },
                        childrenPaths: [BoardLayer[BoardLayer.Top]],
                        isBreakLink: true,
                    },
                },
            );
            world.destroyEntity(effectEntity);
        }
    }

    getThumbsUpEffectData(times: number, useTime: number): { animName: string; audioId: AudioId } {
        let audioId: AudioId;
        let animName = '';
        if (times == 1) {
            if (useTime <= 5000) {
                animName = 'copper';
                audioId = AudioId.THUMBS_UP_2;
            } else {
                animName = 'blue';
                audioId = AudioId.THUMBS_UP_1;
            }
        } else if (times == 2) {
            if (useTime <= 3000) {
                animName = 'gold';
                audioId = AudioId.THUMBS_UP_4;
            } else {
                animName = 'silver';
                audioId = AudioId.THUMBS_UP_3;
            }
        }
        return {
            animName,
            audioId,
        };
    }
}
