import { CutdownComponent } from "../components/CutdownComponent";
import { ECSRuleBase, IECSRuleConfig } from "./core/ECSRuleBase";
export interface IGameOverPreHandleRuleConfig extends IECSRuleConfig {
    /** 倒计时时间 s */
    delayTime: number;
    /** 倒计时标签 */
    cutdownTag: string;
}
/** 游戏结束前处理规则: 目的是用来处理游戏结束前的逻辑,会延迟游戏真正结束时间 */
export class GameOverPreHandleRule extends ECSRuleBase<IGameOverPreHandleRuleConfig> {
    canExecute(): boolean {
        const cutdownEntities = this.world.query([CutdownComponent]);
        for (const cutdownEntity of cutdownEntities) {
            const cutdownComponent = this.world.getComponent(cutdownEntity, CutdownComponent);
            if (cutdownComponent.tag === this.config.cutdownTag) {
                return false;
            }
        }
        return true;
    }
    execute(): void {
        // 创建实体用来倒计时
        console.log('GameOverPreHandleRule execute');
        const cutdownEntity = this.world.createEntity();
        const cutdownComponent = this.world.addComponent(cutdownEntity, CutdownComponent);
        cutdownComponent.duration = this.config.delayTime;
        cutdownComponent.remainingTime = this.config.delayTime;
        cutdownComponent.isRunning = true;
        cutdownComponent.tag = this.config.cutdownTag;
        cutdownComponent.autoDestroy = false;
        cutdownComponent.loopCount = 1;
        cutdownComponent.currentLoop = 0;
        cutdownComponent.isDelay = false;
    }
}