import { CutdownComponent } from "../components/CutdownComponent";
import { ECSRuleBase, IECSRuleConfig } from "./core/ECSRuleBase";

export interface IGameOverCountdownDelayRuleConfig extends IECSRuleConfig {
    /** 延迟时间，单位秒，如果被打断了，也会在这个时间范围内结束 */
    delayTime: number;
    /** 倒计时标签 */
    cutdownTag: string;
}
/** 游戏结束倒计时延迟规则: 目的是用来增加游戏结束倒计时时间，只会增加一次，如果被打断了，也会在这个时间范围内结束 */
export class GameOverCountdownDelayRule extends ECSRuleBase<IGameOverCountdownDelayRuleConfig> {
    canExecute(): boolean {
        const cutdownEntities = this.world.query([CutdownComponent]);   
        for (const cutdownEntity of cutdownEntities) {
            const cutdownComponent = this.world.getComponent(cutdownEntity, CutdownComponent);
            if (cutdownComponent.tag === this.config.cutdownTag) {
                return cutdownComponent.remainingTime > 0 && !cutdownComponent.isDelay;
            }
        }
        return false;
    }
    execute(): void {
        const cutdownEntities = this.world.query([CutdownComponent]);   
        for (const cutdownEntity of cutdownEntities) {
            const cutdownComponent = this.world.getComponent(cutdownEntity, CutdownComponent);
            if (cutdownComponent.tag === this.config.cutdownTag) {
                cutdownComponent.remainingTime = this.config.delayTime;
                cutdownComponent.isDelay = true;
            }
        }
    }
}