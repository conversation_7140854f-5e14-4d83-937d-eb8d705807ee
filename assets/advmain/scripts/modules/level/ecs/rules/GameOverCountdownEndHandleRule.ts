import { CutdownComponent } from "../components/CutdownComponent";
import { ECSEvent } from "../GameEvent";
import { ECSRuleBase, IECSRuleConfig } from "./core/ECSRuleBase";

export interface IGameOverCountdownEndHandleRuleConfig extends IECSRuleConfig {
    /** 倒计时标签 */
    cutdownTag: string;
}
/** 游戏结束倒计时结束处理规则: 目的是用来处理游戏结束倒计时结束后的逻辑 */
export class GameOverCountdownEndHandleRule extends ECSRuleBase<IGameOverCountdownEndHandleRuleConfig> {
    canExecute(): boolean {
        const cutdownEntities = this.world.query([CutdownComponent]);   
        for (const cutdownEntity of cutdownEntities) {
            const cutdownComponent = this.world.getComponent(cutdownEntity, CutdownComponent);
            if (cutdownComponent.tag === this.config.cutdownTag) {
                return cutdownComponent.remainingTime <= 0;
            }
        }
        return false;
    }
    execute(): void {
        const cutdownEntities = this.world.query([CutdownComponent]);   
        for (const cutdownEntity of cutdownEntities) {
            const cutdownComponent = this.world.getComponent(cutdownEntity, CutdownComponent);
            if (cutdownComponent.tag === this.config.cutdownTag) {
                this.world.eventBus.emit(ECSEvent.GameEvent.GAME_FAILED);
                this.world.removeEntity(cutdownEntity);
            }
        }
    }
}