import { Component } from '../../cores/Component';

export interface IBoardScene {
    waitPutCells: number[];
    score: number;
    collectMap: Record<number, number>;
    targetScore?: number;
    lastPutBlockTime?: number;
}
export class BoardScene extends Component {
    public static readonly type = 'BoardScene';
    public waitPutCells: number[] = [];
    public lastPutBlockTime: number = 0;
    public score: number = 0;
    public targetScore: number = 0;
    public collectMap: Record<number, number> = {};
    constructor(board: IBoardScene = { waitPutCells: [], score: 0, collectMap: {}, targetScore: 100, lastPutBlockTime: 0 }) {
        super();
        this.waitPutCells = board.waitPutCells;
        this.score = board.score;
        this.collectMap = board.collectMap;
        this.targetScore = board.targetScore;
        this.lastPutBlockTime = board.lastPutBlockTime;
    }
}
