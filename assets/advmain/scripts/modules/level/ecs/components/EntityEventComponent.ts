import { BehaviorConst } from "../cores/center/BehaviorCenter";
import { Component } from "../cores/Component";
import { ECSEvent } from "../GameEvent";

/**
 * 实体事件配置
 */
export interface EntityEventConfig {
    /** 主行为类型 */
    masterBehaviorType: BehaviorConst;
    /** 行为ID */
    behaviorId: string;
    /** 是否启用 */
    enabled: boolean;
}
/**
 * 实体事件组件
 */
export class EntityEventComponent extends Component {
    /** 事件监听配置列表 */
    public eventHandlerMap: Record<ECSEvent.EntityEvent, EntityEventConfig[]> = {} as Record<ECSEvent.EntityEvent, EntityEventConfig[]>;
    
    /** 是否启用所有事件监听 */
    public enabled: boolean = true;
}