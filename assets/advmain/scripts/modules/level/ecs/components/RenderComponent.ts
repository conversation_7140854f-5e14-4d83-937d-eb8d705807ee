import { Component } from '../cores/Component';
import { EcsViewId } from '../renderWorld/ECSViewBase';
/**渲染组件：仅存放视图id与自定义渲染数据，prefabUrl 等信息可通过 ecsViewId 在配置表中查询*/
export class RenderComponent extends Component {
    /**EcsView 配置表里的视图 Id*/
    ecsViewId: EcsViewId = '';
    /**渲染视图内部节点路径列表 */
    childrenPaths: string[];
    /**自定义数据，RenderCmd / ECSView 自行解析*/
    data: any = null;
    /**是否和渲染视图断开链接(断开链接后，逻辑实体销毁,不会影响渲染视图) */
    isBreakLink: boolean;
    constructor(
        ecsViewId: EcsViewId,
        initParam: {
            data?: any;
            childrenPaths?: string[];
            isBreakLink?: boolean;
        } = {},
    ) {
        super();
        this.ecsViewId = ecsViewId;
        this.data = initParam.data;
        this.childrenPaths = initParam.childrenPaths || [];
        this.isBreakLink = initParam.isBreakLink;
    }
}
