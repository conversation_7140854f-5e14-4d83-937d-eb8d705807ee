import HighlightTag from '../components/tag/HighlightTag';
import { System } from '../cores/System';
import { ECSEvent } from '../GameEvent';

/**高亮变化时，向实体发送事件 */
export class HighlightNotifierSystem extends System {
    init() {
        this.world.onComponentAdd(HighlightTag, this._onAdd, this);
        this.world.onComponentRemove(HighlightTag, this._onRemove, this);
    }
    dispose(): void {
        this.world.offComponentAdd(HighlightTag, this._onAdd, this);
        this.world.offComponentRemove(HighlightTag, this._onRemove, this);
    }
    update() {}
    private _onAdd(entityId: number) {
        let willDestroy: boolean = this.world.willDestroyEntity(entityId);
        if (willDestroy) {
            return;
        }
        // 向实体发送高亮事件
        this.world.emitEntityEvent(entityId, ECSEvent.EntityEvent.HIGHLIGHT_CHANGED, true);

    }
    private _onRemove(entity: number) {
        // 向实体发送取消高亮事件
        this.world.emitEntityEvent(entity, ECSEvent.EntityEvent.HIGHLIGHT_CHANGED, false);
    }
}
