import ShapeComponent from '../../../components/board/ShapeComponent';
import TurnComponent from '../../../components/TurnComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { System } from '../../../cores/System';
import { ECSEvent } from '../../../GameEvent';

/**回合系统 */
export default class TurnSystem extends System {
    init(): void {
        // this.world.onComponentAdd(TurnComponent, this.onTurnCompAdd, this)
        this.world.eventBus.on(ECSEvent.GameEvent.SETTLE_TURN, this.onSettleTurn, this);
        this.world.eventBus.on(ECSEvent.GameEvent.SETTLE_DONE, this.onSettleDone, this);
    }
    dispose(): void {
        // this.world.offComponentAdd(TurnComponent, this.onTurnCompAdd, this)
        this.world.eventBus.off(ECSEvent.GameEvent.SETTLE_TURN, this.onSettleTurn, this);
        this.world.eventBus.off(ECSEvent.GameEvent.SETTLE_DONE, this.onSettleDone, this);
    }
    update(dt: number): void {}

    /**处理结算回合 */
    onSettleTurn(): void {
        const world = this.world;
        const turnState = world.cacheCenter.caculateData.turnState;
        if (turnState === 'wait') return;
        else if (turnState === 'ing') {
            world.cacheCenter.caculateData.turnState = 'wait';
            return;
        }
        world.cacheCenter.caculateData.turnState = 'ing';
        const turnData = world.cacheCenter.turnData;
        world.query([TurnComponent]).forEach((entity) => {
            if (world.getComponent(world.getSource(entity, RelationName.PARENT_CHILD), ShapeComponent)) return;
            turnData.count = (turnData.count || 0) + 1;
            world.emitEntityEvent(entity, ECSEvent.EntityEvent.SETTLE);
        });
        if (!turnData.count) this.onSettleDone();
    }

    /**实体结算完毕 */
    onSettleDone(): void {
        const world = this.world;
        if (--world.cacheCenter.turnData.count > 0) return;
        //回合结束，重置回合数据
        world.cacheCenter.turnData = {};
        if (world.cacheCenter.caculateData.turnState === 'wait') {
            //还有要等待的回合，则继续结算
            world.cacheCenter.caculateData.turnState = '';
            this.onSettleTurn();
        } else {
            //所有回合结束，结算完毕
            world.cacheCenter.caculateData = {};
            world.eventBus.emit(ECSEvent.GameEvent.ALL_SETTLE_DONE);
        }
    }
}
