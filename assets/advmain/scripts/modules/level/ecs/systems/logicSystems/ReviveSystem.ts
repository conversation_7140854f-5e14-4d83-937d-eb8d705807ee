import { System } from '../../cores/System';
import { ECSEvent } from '../../GameEvent';
// import { IProduceBlockEnd } from '../../GameEventData';

export class ReviveSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_FAILED, this.onReviveFailed, this);
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_SUCCESS, this.onReviveSuccesss, this);
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_CONFIRMED, this.onReviveConfirmed, this);
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_CANCELLED, this.onReviveCancelled, this);
        // 监听出块完成事件，处理复活预览
        this.world.eventBus.on(ECSEvent.GameEvent.PRODUCE_BLOCK_END, this.onBlockGenerated, this);
    }

    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_FAILED, this.onReviveFailed, this);
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_SUCCESS, this.onReviveSuccesss, this);
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_CONFIRMED, this.onReviveConfirmed, this);
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_CANCELLED, this.onReviveCancelled, this);
        this.world.eventBus.off(ECSEvent.GameEvent.PRODUCE_BLOCK_END, this.onBlockGenerated, this);
    }

    private onReviveFailed() {
        // console.log('yjf__复活失败 走失败逻辑');
        
    }

    private onReviveSuccesss() {
        // console.log('yjf__复活成功 走成功逻辑');
        this.world.eventBus.emit(ECSEvent.GameEvent.PRODUCE_BLOCK_REVIVE);
    }

    private onReviveConfirmed() {
        // console.log('yjf__复活确认 显示复活块');
        // 复活确认事件已经通过规则流处理，这里可以做其他逻辑
    }

    private onReviveCancelled() {
        // console.log('yjf__复活取消 清除预览数据');
        // 清除预览数据
        this.world.utilCenter.reviveUtil.clearPreviewData();
    }

    private onBlockGenerated(info: any): void {
        const reviveComp = this.world.utilCenter.reviveUtil.getReviveComponent();
        console.log('ReviveSystem: 复活预览模式，缓存复活块数据111', info);
        // 只处理复活预览模式
        if (!reviveComp || !reviveComp.isPreviewMode) {
            return;
        }

        console.log('ReviveSystem: 复活预览模式，缓存复活块数据', info);

        // 缓存复活块数据
        reviveComp.cachedReviveBlocks = info;
        reviveComp.isPreviewMode = false;

        // 触发预览完成事件
        this.world.eventBus.emit(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, info);
    }

    update(): void {}
}
