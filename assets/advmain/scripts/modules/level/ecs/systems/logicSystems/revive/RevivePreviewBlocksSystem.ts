import { System } from '../../../cores/System';
import { ECSEvent } from '../../../GameEvent';
import { PreviewTag } from '../../../components/tag/PreviewTag';
import NodeComponent from '../../../components/NodeComponent';

/**
 * 复活预览块系统
 * 处理复活预览块的缩放动画和生命周期管理
 */
export class RevivePreviewBlocksSystem extends System {
    /**存储预览块的管理数据 */
    private revivePreviewBlocks: Map<number, {
        entityId: number;
        createTime: number;
        hasAnimation: boolean;
    }> = new Map();

    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, this.onRevivePreviewComplete, this);
    }

    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, this.onRevivePreviewComplete, this);
    }

    private onRevivePreviewComplete(): void {
        // 查找所有带有PreviewTag的实体
        const previewEntities = this.world.query([PreviewTag, NodeComponent]);
        
        console.log('RevivePreviewBlocksSystem: 找到预览块实体', previewEntities.length);

        previewEntities.forEach((entityId) => {
            // 检查是否是新创建的预览块
            if (!this.revivePreviewBlocks.has(entityId)) {
                console.log('RevivePreviewBlocksSystem: 注册新的预览块', entityId);
                
                // 注册预览块
                this.revivePreviewBlocks.set(entityId, {
                    entityId: entityId,
                    createTime: Date.now(),
                    hasAnimation: false
                });

                // 启动缩放动画
                this.startScaleAnimation(entityId);
            }
        });
    }

    /**
     * 启动缩放动画
     */
    private startScaleAnimation(entityId: number): void {
        const nodeComp = this.world.getComponent(entityId, NodeComponent);
        if (!nodeComp || !(nodeComp as any).node) return;

        const node = (nodeComp as any).node;
        if (!cc.isValid(node)) return;

        console.log('RevivePreviewBlocksSystem: 启动缩放动画', entityId);

        // 创建缩放动画序列
        const scaleUp = cc.scaleTo(0.2, 1.2);
        const scaleDown = cc.scaleTo(0.2, 0.8);
        const scaleNormal = cc.scaleTo(0.2, 1.0);
        
        // 创建循环动画
        const scaleSequence = cc.sequence(scaleUp, scaleDown, scaleNormal);
        const repeatAction = cc.repeatForever(scaleSequence);

        // 执行动画
        node.runAction(repeatAction);

        // 标记已有动画
        const blockData = this.revivePreviewBlocks.get(entityId);
        if (blockData) {
            blockData.hasAnimation = true;
        }
    }

    /**
     * 停止缩放动画
     */
    private stopScaleAnimation(entityId: number): void {
        const nodeComp = this.world.getComponent(entityId, NodeComponent);
        if (!nodeComp || !(nodeComp as any).node) return;

        const node = (nodeComp as any).node;
        if (!cc.isValid(node)) return;

        console.log('RevivePreviewBlocksSystem: 停止缩放动画', entityId);

        // 停止所有动画
        node.stopAllActions();
        
        // 恢复原始缩放
        node.scale = 1.0;
    }

    update(): void {
        const currentTime = Date.now();
        const toRemove: number[] = [];

        // 检查所有预览块的生命周期
        this.revivePreviewBlocks.forEach((blockData, entityId) => {
            // 检查实体是否仍然存在
            if (!this.world.entityCenter.entities.has(entityId)) {
                toRemove.push(entityId);
                return;
            }

            // 检查是否超过2秒
            if (currentTime - blockData.createTime > 2000) {
                console.log('RevivePreviewBlocksSystem: 2秒后销毁预览块', entityId);
                
                // 停止动画
                this.stopScaleAnimation(entityId);
                
                // 销毁实体
                this.world.removeEntity(entityId);
                
                // 标记移除
                toRemove.push(entityId);
            }
        });

        // 清理已移除的实体
        toRemove.forEach(entityId => {
            this.revivePreviewBlocks.delete(entityId);
        });
    }
}