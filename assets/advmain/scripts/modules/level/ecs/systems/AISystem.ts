import { AIComponent } from "../components/AIComponent";
import { BehaviorConst } from "../cores/center/BehaviorCenter";
import { System } from "../cores/System";
import { AIBehaviorBase } from "../behavior/ai/AIBehaviorBase";

export class AISystem extends System {
    init(): void {
        this.world.onComponentAdd(AIComponent,this.__onAIComponentAdd,this);
        this.world.onComponentRemove(AIComponent,this.__onAIComponentRemove,this);
    }
    dispose(): void {
        this.world.offComponentAdd(AIComponent,this.__onAIComponentAdd,this);
        this.world.offComponentRemove(AIComponent,this.__onAIComponentRemove,this);
        const entities = this.world.query([AIComponent]);
        for(const entityId of entities){
            this.unregisterEntityEvent(entityId);
        }
    }
    update(dt: number): void {
        for(const entityId of this.world.query([AIComponent])){//kktodo 没有实现update的ai行为可以直接就不执行。
            const aiComponent = this.world.getComponent(entityId,AIComponent);
            const aiBehavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.AI,aiComponent.aiBehaviorId);
            if(aiBehavior && aiBehavior.updateAI){
                aiBehavior.updateAI(entityId,dt);
            }
        }
    }
    /**注册事件 */
    private registerEntityEvent(entityId: number): AIBehaviorBase {
        const aiComponent = this.world.getComponent(entityId,AIComponent);
        const aiBehavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.AI,aiComponent.aiBehaviorId);
        for(const event of aiBehavior.bindEntityEvent()){
            this.world.entityCenter.onEntityEvent(entityId,event,BehaviorConst.AI,aiComponent.aiBehaviorId);
        }
        return aiBehavior;
    }
    /**注销事件 */
    private unregisterEntityEvent(entityId: number): AIBehaviorBase {
        const aiComponent = this.world.getComponent(entityId,AIComponent);
        const aiBehavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.AI,aiComponent.aiBehaviorId);
        for(const event of aiBehavior.bindEntityEvent()){
            this.world.entityCenter.offEntityEvent(entityId,event,BehaviorConst.AI,aiComponent.aiBehaviorId);
        }
        return aiBehavior;
    }
    /**添加AI组件 */
    private __onAIComponentAdd(entityId: number,aiComponent: AIComponent): void {
        const aiBehavior = this.registerEntityEvent(entityId);
        aiBehavior&& aiBehavior.initAI(entityId);
    }
    /**移除AI组件 */
    private __onAIComponentRemove(entityId: number,aiComponent: AIComponent): void {
        const aiBehavior = this.unregisterEntityEvent(entityId);
        aiBehavior&& aiBehavior.disposeAI(entityId);
    }
}