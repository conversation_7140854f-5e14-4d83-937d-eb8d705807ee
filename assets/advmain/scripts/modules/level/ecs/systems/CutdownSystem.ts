import { CutdownComponent } from "../components/CutdownComponent";
import { System } from "../cores/System";
import { ECSEvent } from "../GameEvent";
export interface ICutdownInfo {
    entityId: number;
    duration: number;
    remainingTime: number;
}
/** 倒计时系统 */
export class CutdownSystem extends System {
    init(): void { }
    dispose(): void { }
    update(dt: number): void {
        const cutdownEntities = this.world.query([CutdownComponent]);
        for (const cutdownEntity of cutdownEntities) {
            const cutdownComponent = this.world.getComponent(cutdownEntity, CutdownComponent);
            cutdownComponent.remainingTime -= dt;
            if (cutdownComponent.remainingTime === cutdownComponent.duration) {
                this.world.eventBus.emit(ECSEvent.BaseEvent.CUTDOWN_START, {
                    entityId: cutdownEntity,
                    duration: cutdownComponent.duration,
                    remainingTime: cutdownComponent.remainingTime,
                });
            } 
            if (cutdownComponent.remainingTime > 0) {
                this.world.eventBus.emit(ECSEvent.BaseEvent.CUTDOWN_UPDATE, {
                    entityId: cutdownEntity,
                    duration: cutdownComponent.duration,
                    remainingTime: cutdownComponent.remainingTime,
                });
            }
            if (cutdownComponent.remainingTime <= 0) {
                this.world.eventBus.emit(ECSEvent.BaseEvent.CUTDOWN_END, {
                    entityId: cutdownEntity,
                    duration: cutdownComponent.duration,
                    remainingTime: cutdownComponent.remainingTime,
                });
                if (cutdownComponent.autoDestroy) {
                    this.world.destroyEntity(cutdownEntity);
                }
            }
        }
    }
}