import { DelayRuleComponent } from "../components/DelayRuleComponent";
import { System } from "../cores/System";
import { ECSEvent } from "../GameEvent";
import { ICreateDelayRuleData} from "../GameEventData";
import { ECSExecuteContext } from "../rules/core/ECSRuleBase";
export const RuleSystemId = "RuleSystem";
/** 流节点类型 */
export interface IECSFlowNode {
    type: 'list' | 'select';
    children: (IECSFlowNode | string)[];
}
/** 流配置类型 */
export type IECSFlowConfig = IECSFlowNode | string;

/**规则系统 */
export class RuleSystem extends System{
    init(): void {
        this.initRuleFlowConfigForEvent();
        this.world.eventBus.setGlobalListener(this.__globalListener, this);
        this.world.eventBus.on(ECSEvent.BaseEvent.CREATE_DELAY_RULE, this.__onCreateDelayRule, this);
        this.world.eventBus.on(ECSEvent.BaseEvent.INIT_WORLD, this.__onInitWorldRuleFlow, this);
    }
    dispose(): void {
        this.world.eventBus.removeGlobalListener(this.__globalListener, this);
        this.world.eventBus.off(ECSEvent.BaseEvent.CREATE_DELAY_RULE, this.__onCreateDelayRule, this);
        this.world.eventBus.off(ECSEvent.BaseEvent.INIT_WORLD, this.__onInitWorldRuleFlow, this);
    }
    update(dt: number): void {
        const ruleEntities = this.world.query([DelayRuleComponent]);
        for (const ruleEntity of ruleEntities) {
            const ruleComponent = this.world.getComponent(ruleEntity, DelayRuleComponent);
            ruleComponent.delay -= dt;
            if (ruleComponent.delay <= 0) {
                const rule = this.world.ruleCenter.createRule(ruleComponent.ruleId);
                if (!rule) {
                    console.error(`规则${ruleComponent.ruleId}不存在`);
                    continue;
                }
                const context = {
                    world: this.world,
                    event: ruleComponent.event,
                    eventData: ruleComponent.eventData
                };
                rule.setContext(context);
                rule.delayRun();
                this.world.removeEntity(ruleEntity);
            }
        }
    }
    /** 全局事件监听 
     *  @param event 事件类型
     *  @param data 事件数据
    */
    private __globalListener(event: ECSEvent.GameEvent | ECSEvent.BaseEvent, data: any): void {
        const context: ECSExecuteContext = {
            world: this.world,
            event: event,
            eventData: data
        };
        this.world.ruleCenter.executeFlow(event, context);
    }
    /**
     * 创建延迟规则
     * @param data 
     */
    private __onCreateDelayRule(data: ICreateDelayRuleData): void {
        this.world.addComponent(this.world.createEntity(), DelayRuleComponent, data);
    }
    /**
     * 应用初始化场景规则
     * @param data 
     */
    private __onInitWorldRuleFlow(): void {
        const ruleFlowConfig = this.world.configCenter.levelConfig.ruleFlowConfigForWorldInit;
        if (!ruleFlowConfig) {
            console.error(`世界初始化规则流配置为空`);
            return;
        }
        const commonInitRuleFlowId = "commonInitRuleFlowId_ONCE";//公共初始化规则流ID
        this.world.ruleCenter.registerFlow(commonInitRuleFlowId, ruleFlowConfig);
        this.world.ruleCenter.executeFlow(commonInitRuleFlowId, {
            world: this.world,
            event: ECSEvent.BaseEvent.INIT_WORLD_COMPLETE,
            eventData: null
        });
        this.world.eventBus.emit(ECSEvent.BaseEvent.INIT_WORLD_COMPLETE, null);
    }
    /**
     * 初始化事件规则流配置
     */
    private initRuleFlowConfigForEvent(): void {
        const ruleConfig = this.world.configCenter.levelConfig.ruleFlowConfigForEvent;
        if (!ruleConfig) {
            console.error("规则配置为空");
            return;
        }
        const ruleCenter = this.world.ruleCenter;
        for (const event in ruleConfig) {
            const config = ruleConfig[event];
            if (!config) {
                console.error(`规则流配置${config}为空`);
                continue;
            }
            ruleCenter.registerFlow(event, config);
        }
    }
}