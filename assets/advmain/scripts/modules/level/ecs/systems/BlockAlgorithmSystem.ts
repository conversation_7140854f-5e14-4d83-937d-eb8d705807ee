import BoardComponent from '../components/board/BoardComponent';
import ProduceBlockAlgorithmComponent from '../components/special/ProduceBlockAlgorithmComponent';
import { System } from '../cores/System';
import { ECSEvent } from '../GameEvent';
import { IBoardData } from '../GameEventData';

/**
 * 块生成算法系统
 * 负责根据配置的算法ID生成对应的块
 */
export class BlockAlgorithmSystem extends System {
    init(): void {
        // 在此订阅事件或初始化逻辑
        this.world.onComponentAdd(ProduceBlockAlgorithmComponent, this.produceBlock, this);
    }

    dispose(): void {
        this.world.offComponentAdd(ProduceBlockAlgorithmComponent, this.produceBlock, this);
    }

    update(dt: number): void {}

    /**
     * 生成块
     */
    private produceBlock(entityId: number): void {
        // 获取棋盘组件
        const boardEntity = this.world.query([BoardComponent]).filter((entity) => {
            const boardComp = this.world.getComponent(entity, BoardComponent);
            return boardComp.isCanPut === true;
        })[0];
        let boardComp = this.world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) {
            console.warn('没有找到棋盘实体');
            return;
        }
        const combinedBoardOccupy = this.world.utilCenter.boardUtil.combineBoardOccupy(
            this.world.query([BoardComponent]).map((entity) => {
                return this.world.utilCenter.boardUtil.getBoardOccupy(entity);
            }),
        );
        // 算法枚举  例如：AlgoClassTypeConst.algo_random_no_death
        const algorithmId = this.world.getComponent(entityId, ProduceBlockAlgorithmComponent).algorithmId;
        // 准备棋盘数据
        const boardData: IBoardData = {
            boardOccupy: combinedBoardOccupy,
            rows: boardComp.rCount,
            cols: boardComp.cCount,
            algorithmId: algorithmId,
        };
        console.log('ProduceBlockSystem 生成块开始 algorithmId:', algorithmId, boardData);
        this.world.eventBus.emit(ECSEvent.BaseEvent.PRODUCE_BLOCK_START, boardData);
        // 销毁实体
        this.world.destroyEntity(entityId);
    }
}
