import { ECSEvent } from '../../GameEvent';
import { CellAIBehavior } from './CellAIBehavior';

/**结算块AI，需要结算的块直接继承此类 */
export abstract class SettleCellAIBehavior extends CellAIBehavior {
    bindEntityEvent() {
        const events = super.bindEntityEvent();
        events.push(ECSEvent.EntityEvent.SETTLE);
        return events;
    }
    receiveEntityEvent(entityId: number, event: ECSEvent.EntityEvent, eventData: any): void {
        if (event === ECSEvent.EntityEvent.SETTLE) {
            this.onSettle(entityId);
            return;
        }
        super.receiveEntityEvent(entityId, event, eventData);
    }

    /**处理结算，结算完必须调用settleDone */
    abstract onSettle(entityId: number): void;
    /**结算完毕 */
    protected settleDone(): void {
        this.world.eventBus.emit(ECSEvent.GameEvent.SETTLE_DONE);
    }
}
