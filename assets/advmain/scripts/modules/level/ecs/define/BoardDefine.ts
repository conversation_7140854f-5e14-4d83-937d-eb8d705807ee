//棋盘相关的定义写在这里，原则上不可引用其他脚本

/**块颜色 */
export enum CellColor {
    None = 0,
    Blue = 1,
    Yellow = 2,
    Purple = 3,
    Orange = 4,
    Red = 5,
    <PERSON> = 6,
    <PERSON><PERSON> = 7,
    DBlue = 8,
    Gray = 9,
}

/**块尺寸 */
export const CellSize = 106;

/**盘面层级 */
export enum BoardLayer {
    /**背景 */
    Bg = 0,
    /**底层 */
    Bottom,
    /**底层之上 */
    BottomOn,
    /**元素层 */
    Element,
    /**元素之上 */
    ElementOn,
    /**屏障层 */
    Barrier,
    /**屏障之上 */
    BarrierOn,
    /**遮挡层 */
    Shelter,
    /**遮挡之上 */
    ShelterOn,
    /**数据层 */
    Data,
    /**顶层（特效） */
    Top,
}

/**目标类型（各种块类型也在这里枚举）
 * @块编号规则 99以上，其他类型挑选100以下数字
 * @主类型加10 例如：100、110、120
 * @附属或同源加1 例如110的1号附属为111，2号附属为112
 */
export const enum TargetType {
    /**分数 */
    Score = 1,
    /**普通块 */
    Normal = 100,
    /**蓝宝石 */
    Gem_101 = 101,
    /**绿宝石 */
    Gem_102 = 102,
    /**橙宝石 */
    Gem_103 = 103,
    /**黄宝石 */
    Gem_104 = 104,
    /**红宝石 */
    Gem_105 = 105,
    /**紫宝石 */
    Gem_106 = 106,
    /**飞机 */
    Plane = 110,
    /**玻璃（冰块） */
    Glass = 120,
    /**钻石 */
    Diamond = 130,
}

/**各种类型的块模板的非默认可选参数（CellTempleteOptionalArgs）都在这里枚举，key必须对应TargetType枚举值 */
export const CELL_TEMPLATE_PARAM = {
    [TargetType.Normal]: {},
    [TargetType.Gem_101]: { ecsViewId: 'ecsview_collect_cell' },
    [TargetType.Gem_102]: { ecsViewId: 'ecsview_collect_cell' },
    [TargetType.Gem_103]: { ecsViewId: 'ecsview_collect_cell' },
    [TargetType.Gem_104]: { ecsViewId: 'ecsview_collect_cell' },
    [TargetType.Gem_105]: { ecsViewId: 'ecsview_collect_cell' },
    [TargetType.Gem_106]: { ecsViewId: 'ecsview_collect_cell' },
    [TargetType.Plane]: { ecsViewId: 'ecsview_plane_cell', aiBehaviorId: 'ai_cell_plane' },
    [TargetType.Glass]: {
        ecsViewId: 'ecsview_glass_cell',
        aiBehaviorId: 'ai_cell_glass',
        layer: BoardLayer.Barrier,
        components: [{ name: 'HPComponent', defaultArg: 2 }],
    },
    [TargetType.Diamond]: {
        ecsViewId: 'ecsview_diamond_cell',
        aiBehaviorId: 'ai_cell_diamond',
        clearCode: 0b100,
        components: [{ name: 'TurnComponent' }],
    },
};
/**块类型 */
export type CellType = keyof typeof CELL_TEMPLATE_PARAM;

/**普通块七色 */
export const NormalColor7 = [CellColor.Blue, CellColor.Yellow, CellColor.Purple, CellColor.Orange, CellColor.Red, CellColor.Green, CellColor.Cyan];
/**普通块三色 */
export const NormalColor3 = [CellColor.Blue, CellColor.Red, CellColor.Yellow];
/**宝石枚举 */
export const GemTypes = [TargetType.Gem_101, TargetType.Gem_102, TargetType.Gem_103, TargetType.Gem_104, TargetType.Gem_105, TargetType.Gem_106];

/**没有收集曲线动画的块 */
export const NO_COLLECT_TWEEN = [TargetType.Normal, TargetType.Plane, TargetType.Glass, TargetType.Diamond];
