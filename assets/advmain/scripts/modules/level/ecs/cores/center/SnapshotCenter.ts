import { LogicWorld } from '../World';
import { ECSEvent } from '../../GameEvent';
import { RenderComponent } from '../../components/RenderComponent';

// ==================== 类型定义 ====================

/**
 * 存储元信息结构
 */
interface StorageMeta {
    /** 数据版本 */
    version: string;
    /** 时间戳 */
    timestamp: number;
    /** 关卡ID */
    levelId: string;
}

/**
 * ECS快照结构
 */
interface EcsSnapshot {
    logicWorld: any;
}

/**
 * 存储数据结构
 */
export interface EcsStorageData {
    /** 存储键名 */
    storageKey: string;
    /** 元信息 */
    meta: StorageMeta;
    /** 快照数据 */
    data: EcsSnapshot;
}

// ==================== 快照管理器 ====================

/**
 * 快照中心
 * 负责ECS世界状态的保存、加载和恢复功能
 */
export class SnapshotCenter {
    // ==================== 私有属性 ====================

    /** 逻辑世界实例 */
    private readonly world: LogicWorld;

    /** 关卡ID */
    private readonly levelId: string;

    /** 是否需要快照 */
    private needSnapshot: boolean;

    /** 存储前缀 */
    private readonly storagePrefix: string = 'ecs_snapshot_';
    // ==================== 构造函数 ====================

    /**
     * 构造快照管理器
     * @param world 逻辑世界实例
     * @param levelId 关卡ID
     * @param config 存储配置（可选）
     */
    constructor(world: LogicWorld, levelId: string) {
        this.world = world;
        this.levelId = levelId;
        this.needSnapshot = false;
        this.initSnapshotEventListeners();
    } 
    /**
    * 添加快照事件监听器
    */
    private initSnapshotEventListeners(): void {
        const levelData = this.world.configCenter.levelConfig;
        if (levelData?.snapshotEvents?.length > 0) {
            for (const event of levelData.snapshotEvents) {
                this.world.eventBus.on(event, this.__markSnapshotNeeded, this);
            }
        }
    }
    
    /**
     * 标记需要快照
     */
    private __markSnapshotNeeded(): void {
        this.needSnapshot = true;
    }

    /**
     * 检查是否需要生成快照
     */
    public checkSnapshot(): void {
        if (this.needSnapshot) {
            this.needSnapshot = false;
            this.generateSnapshot();
        }
    }
    /**
     * 生成并保存快照
     * @returns 生成的快照数据
     */
    private generateSnapshot(): EcsSnapshot {
        const snapshot = this.createSnapshot();
        const storageData = this.createStorageData(snapshot);
        this.world.eventBus.emit(ECSEvent.BaseEvent.SAVE_SNAPSHOT, storageData);
        return snapshot;
    }

    /**
     * 恢复快照到ECS世界
     * @param snapshot 快照数据
     * @returns 是否恢复成功
     */
    public restoreSnapshot(snapshot: EcsSnapshot): boolean {
        return this.restoreWorldFromSnapshot(snapshot);
    }
    // ==================== 快照生成 ====================

    /**
     * 创建快照数据
     */
    private createSnapshot(): EcsSnapshot {
        const snapshot: EcsSnapshot = {  logicWorld: this.world.serialize() };
        return snapshot;
    }

    // ==================== 快照恢复 ====================
    /**
     * 从快照恢复世界状态
     */
    private restoreWorldFromSnapshot(snapshot: EcsSnapshot): boolean {
        // 反序列化快照对象数据
        this.world.deserialize(snapshot.logicWorld);
        this.world.eventBus.emit(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_DATA_DONE);

        
        const renderEntities = this.restoreRenderWorld();
        if (renderEntities.size === 0) {
            this.world.eventBus.emit(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE);
            return true;
        }

        this.waitForRenderCompletion(renderEntities);
        return true;
    }

    private restoreRenderWorld(): Set<number> {
        const renderEntities = new Set<number>();
        Array.from(this.world.entityCenter.entities)
            .sort((a, b) => a - b)
            .forEach(entityId => {
                if (this.shouldTriggerRender(entityId)) {
                    renderEntities.add(entityId);
                    this.world.eventBus.emit(ECSEvent.BaseEvent.RENDER_ADD, entityId);
                }
            });
        return renderEntities;
    }

    /**
     * 判断是否需要触发渲染
     */
    private shouldTriggerRender(entityId: number): boolean {
        const hasRenderComponent = this.world.hasComponent(entityId, RenderComponent);
        const willDestroy = this.world.willDestroyEntity(entityId);
        return hasRenderComponent && !willDestroy;
    }

    /**
     * 等待渲染完成
     */
    private waitForRenderCompletion(renderEntities: Set<number>): void {
        let completedCount = 0;

        const handleRenderComplete = (entityId: number) => {
            if (!renderEntities.has(entityId)) return;

            if (++completedCount === renderEntities.size) {
                this.cleanupRenderListeners(handleRenderComplete);
                this.world.eventBus.emit(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE);
            }
        };

        this.world.eventBus.on(ECSEvent.BaseEvent.RENDER_CREATED, handleRenderComplete, this);
        this.world.eventBus.on(ECSEvent.BaseEvent.RENDER_CREATED_FAILED, handleRenderComplete, this);
    }

    /**
     * 清理渲染监听器
     */
    private cleanupRenderListeners(handler: (entityId: number) => void): void {
        this.world.eventBus.off(ECSEvent.BaseEvent.RENDER_CREATED, handler, this);
        this.world.eventBus.off(ECSEvent.BaseEvent.RENDER_CREATED_FAILED, handler, this);
    }

    // ==================== 存储管理 ====================

    /**
     * 创建存储数据结构
     */
    private createStorageData(snapshot: EcsSnapshot): EcsStorageData {
        return {
            storageKey: this.getStorageKey(),
            meta: this.createMeta(),
            data: snapshot,
        };
    }

    /**
     * 获取存储键名
     */
    public getStorageKey(): string {
        return `${this.storagePrefix}${this.levelId}`;
    }

    /**
     * 创建存储元信息
     */
    private createMeta(): StorageMeta {
        return {
            version: '1.0.0',
            timestamp: Date.now(),
            levelId: this.levelId,
        };
    }
}
