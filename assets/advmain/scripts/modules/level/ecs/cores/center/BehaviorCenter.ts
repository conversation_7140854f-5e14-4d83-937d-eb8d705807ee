import { BehaviorBase, EntityBehaviorBase, IBehaviorConfig } from "../../combat/core/BehaviorBase";
import { ConfigTableConst } from "../../registry/ConfigRegistry";
import { AIBehaviorBase } from "../../behavior/ai/AIBehaviorBase";
import { LogicWorld } from "../World";
import { AIBehaviorRegistry } from "../../registry/behavior/AIBehaviorRegistry";
import { ProjectilePathBehaviorRegistry } from "../../registry/behavior/ProjectilePathBehaviorRegistry";
import { ProjectileBehaviorBase } from "../../behavior/projectile/ProjectileBehaviorBase";
import { ProjectileBehaviorRegistry } from "../../registry/behavior/ProjectileBehaviorRegistry";
import { ProjectilePathBehaviorBase } from "../../behavior/projectilePath/ProjectilePathBehaviorBase";
import { BuffBehaviorBase } from "../../behavior/buff/BuffBehaviorBase";
import { BuffStackBehaviorBase } from "../../behavior/buffstack/BuffStackBehaviorBase";
import { BuffBehaviorRegistry } from "../../registry/behavior/BuffBehaviorRegistry";
import { BuffStackBehaviorRegistry } from "../../registry/behavior/BuffStackBehaviorRegistry";

/**
 * 行为枚举
 */
export enum BehaviorConst {
    /**
     * AI行为
     */
    AI = "AI",
    /**
     * 投射物行为
     */
    Projectile = "Projectile",                   
    /**
     * 投射物路径行为
     */
    ProjectilePath = "ProjectilePath",
    /**
     * buff行为
     */
    Buff = "Buff",
    /**
     * buff叠加行为
     */
    BuffStack = "BuffStack",
}

/**
 * 行为映射接口
 */
export interface IBehaviorTypeMap {
    [BehaviorConst.AI]: AIBehaviorBase;
    [BehaviorConst.Projectile]: ProjectileBehaviorBase;
    [BehaviorConst.ProjectilePath]: ProjectilePathBehaviorBase;
    [BehaviorConst.Buff]: BuffBehaviorBase;
    [BehaviorConst.BuffStack]: BuffStackBehaviorBase;
}

/**
 * 通用行为中心
 * 负责管理所有的行为注册表
 * 支持通过行为类型和行为ID动态创建并缓存行为实例
 */
export class BehaviorCenter{
    // 行为注册表映射 - 存储每种行为类型的注册表
    private behaviorTypeRegistryMap: Map<BehaviorConst, Record<string, clzz<BehaviorBase<any>>>> = new Map();
    
    // 行为实例缓存映射 - 存储已创建的行为实例
    private behaviorInstanceCacheMap: Map<BehaviorConst, Map<string, BehaviorBase<any>>> = new Map();

    /**行为与配置表映射 */
    private behaviorTypeConfigMap: Map<BehaviorConst, ConfigTableConst> = new Map();

    /**实体行为缓存映射 - 存储实体行为实例 */
    private entityBehaviorInstanceCacheMap: Map<BehaviorConst, Map<string, EntityBehaviorBase<any>>> = new Map();

    /**世界实例 */
    private world: LogicWorld;
    /**
     * 初始化行为
     */
    constructor (world: LogicWorld) {
        this.world = world;
        this.initBehaviorTypeRegistry(BehaviorConst.AI, AIBehaviorRegistry, ConfigTableConst.AIBehaviorConfig,true);
        this.initBehaviorTypeRegistry(BehaviorConst.Projectile, ProjectileBehaviorRegistry, ConfigTableConst.ProjectileBehaviorConfig);
        this.initBehaviorTypeRegistry(BehaviorConst.ProjectilePath, ProjectilePathBehaviorRegistry, ConfigTableConst.ProjectilePathBehaviorConfig);
        this.initBehaviorTypeRegistry(BehaviorConst.Buff, BuffBehaviorRegistry, ConfigTableConst.BuffBehaviorConfig);
        this.initBehaviorTypeRegistry(BehaviorConst.BuffStack, BuffStackBehaviorRegistry, ConfigTableConst.BuffStackBehaviorConfig);
    }
    /**
     * 初始化行为类型注册表
     * @param masterBehaviorType 主行为类型
     * @param registry 行为类型注册表
     * @param configName 行为配置表名
     * @param hasEntityEvent 是否需要初始化实体行为事件
     */
    private initBehaviorTypeRegistry(masterBehaviorType: BehaviorConst, registry: Record<string, clzz<BehaviorBase<any>>>,configName:ConfigTableConst,hasEntityEvent: boolean = false): void {
        const behaviorInstanceCache = new Map();
        this.behaviorTypeRegistryMap.set(masterBehaviorType, registry);
        this.behaviorTypeConfigMap.set(masterBehaviorType, configName);
        this.behaviorInstanceCacheMap.set(masterBehaviorType, behaviorInstanceCache);
        if(hasEntityEvent){
            this.entityBehaviorInstanceCacheMap.set(masterBehaviorType, behaviorInstanceCache);
            this.initBehaviorEntityEvent(behaviorInstanceCache,masterBehaviorType,registry);
        }
    }
    /**
     * 初始化实体行为事件
     * @param masterBehaviorType 主行为类型
     * @param registry 行为类型注册表
     */
    private initBehaviorEntityEvent(entityBehaviorInstanceCache: Map<string, EntityBehaviorBase>,masterBehaviorType: BehaviorConst,registry: Record<string, clzz<BehaviorBase>>): void {
        for (const [behaviorType, behaviorClass] of Object.entries(registry)) {
            const behaviorInstance = new behaviorClass() as EntityBehaviorBase;
            behaviorInstance.world = this.world;
            entityBehaviorInstanceCache.set(behaviorType, behaviorInstance);
            // 缓存实例
            const instanceCache = this.behaviorInstanceCacheMap.get(masterBehaviorType);
            instanceCache.set(behaviorType, behaviorInstance);
        }
    }
    /**
     * 获取带实体事件的行为实例
     */
    getEntityBehavior(masterBehaviorType: BehaviorConst, behaviorId: string){
        const config = this.world.configCenter.getConfigByTableKey(this.behaviorTypeConfigMap.get(masterBehaviorType), behaviorId)as IBehaviorConfig;
        const behaviorInstance = this.entityBehaviorInstanceCacheMap.get(masterBehaviorType)?.get(config.behaviorType);
        if(!behaviorInstance){
            return null;
        }
        behaviorInstance.config = config;
        return behaviorInstance;
    }

    /**
     * 根据行为类型和行为ID获取或创建行为实例
     * 如果实例已存在则返回缓存的实例，否则创建新实例并缓存
     * @param masterBehaviorType 行为主类型
     * @param behaviorId 行为ID
     * @returns 行为实例
     */
    getBehaviorById<T extends BehaviorConst>(
        masterBehaviorType: T,
        behaviorId: string
    ): IBehaviorTypeMap[T] | undefined {
        // 首先检查缓存中是否已有实例
        const config = this.world.configCenter.getConfigByTableKey(this.behaviorTypeConfigMap.get(masterBehaviorType), behaviorId)as IBehaviorConfig;
        return this.getBehaviorByType(masterBehaviorType, config.behaviorType,config);
    }
    /**
     * 根据行为类型和行为ID获取或创建行为实例
     * 如果实例已存在则返回缓存的实例，否则创建新实例并缓存
     * @param masterBehaviorType 行为主类型
     * @param behaviorType 行为类型
     * @returns 行为实例
     */
    getBehaviorByType<T extends BehaviorConst>(
        masterBehaviorType: T,
        behaviorType: string,
        config?: IBehaviorConfig
    ): IBehaviorTypeMap[T] | undefined {
        const instanceCache = this.behaviorInstanceCacheMap.get(masterBehaviorType);
        let behaviorInstance = instanceCache.get(behaviorType);
        if (!behaviorInstance) {

            // 缓存中没有，从注册表获取行为类并创建实例
            const typeRegistry = this.behaviorTypeRegistryMap.get(masterBehaviorType);
            if (!typeRegistry) {
                console.warn(`未找到行为类型=${masterBehaviorType}的注册表`);
                return undefined;
            }

            const behaviorClass = typeRegistry[behaviorType];
            if (!behaviorClass) {
                console.warn(`未找到行为类型=${masterBehaviorType}, 行为类型=${behaviorType}的行为类`);
                return undefined;
            }

            // 创建新的行为实例
            behaviorInstance = new behaviorClass() as IBehaviorTypeMap[T];
            behaviorInstance.world = this.world;
            // 缓存新创建的实例
            instanceCache.set(behaviorType, behaviorInstance);
        }
        behaviorInstance.config = config;
        return behaviorInstance as IBehaviorTypeMap[T]//kktodo 临时强制转换;
    }
} 
