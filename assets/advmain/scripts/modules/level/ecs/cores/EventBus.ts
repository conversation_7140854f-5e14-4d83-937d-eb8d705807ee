import { Expand } from '../define/EcsDefine';
import { ECSEvent, GameEventMap } from '../GameEvent';
/** 事件总线实现 */
export class EventBus {
    /** 逻辑全局事件映射：事件类型 -> 处理器数组 */
    private handlers = new Map<
        string,
        Array<{
            func: Function;
            thisObj: any;
            once?: boolean;
        }>
    >();

    /** 实体事件映射：事件类型 -> 处理器数组 */
private entityHandlers = new Map<
        string,
        Array<{
            func: Function;
            thisObj: any;
            once?: boolean;
        }>
    >();

    /**渲染全局事件映射：事件类型 -> 处理器数组 */
    private renderHandlers = new Map<
        string,
        Array<{
            func: Function;
            thisObj: any;
            once?: boolean;
        }>
    >();

    /** 全局事件监听器 */
    private globalHandlers: {
        func: Function;
        thisObj: any;
    };

    /** 获取事件key */
    private getEventKey(event: string, eventId: number | string = ''): string {
        let eventKey = event as string;
        if (eventId) {
            eventKey += '_' + eventId;
        }
        return eventKey;
    }

    /**
     * 安全添加监听器，避免重复添加
     */
    private _safeAddHandler(
        handlers: Map<
            string,
            Array<{
                func: Function;
                thisObj: any;
                once?: boolean;
            }>
        >,
        eventKey: string,
        func: Function,
        thisObj: any,
        once: boolean = false,
    ): void {
        const handlerArray = handlers.get(eventKey) || [];
        if (!handlers.has(eventKey)) {
            handlers.set(eventKey, handlerArray);
        }

        // 检查是否已经存在相同的监听器
        if (handlerArray.some((h) => h.func === func && h.thisObj === thisObj)) {
            const eventType = once ? '一次性事件监听器' : '事件监听器';
            console.warn(`${eventType}已存在，跳过重复添加: ${eventKey}`);
            return;
        }

        handlerArray.push({ func, thisObj, once });
    }

    /**
     * 安全删除监听器，避免重复删除
     */
    private _safeRemoveHandler(
        handlers: Map<
            string,
            Array<{
                func: Function;
                thisObj: any;
                once?: boolean;
            }>
        >,
        eventKey: string,
        func: Function,
        thisObj: any,
    ): boolean {
        const handlerArray = handlers.get(eventKey);
        if (!handlerArray) {
            console.warn(`事件监听器不存在，跳过删除: ${eventKey}`);
            return false;
        }

        const index = handlerArray.findIndex((h) => h.func === func && h.thisObj === thisObj);

        if (index === -1) {
            console.warn(`事件监听器不存在，跳过删除: ${eventKey}`);
            return false;
        }

        handlerArray.splice(index, 1);

        // 如果数组为空，删除该事件键
        if (handlerArray.length === 0) {
            handlers.delete(eventKey);
        }

        return true;
    }

    /**
     * 订阅事件
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     */
    on<T extends keyof GameEventMap>(
        event: T & (ECSEvent.BaseEvent | ECSEvent.GameEvent),
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId: number | string = '',
    ): void {
        const eventKey = this.getEventKey(event, eventId);
        this._safeAddHandler(this.handlers, eventKey, func, thisObj);
    }

    /**
     * 一次性订阅
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     */
    once<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId: number | string = '',
    ): void {
        const eventKey = this.getEventKey(event, eventId);
        this._safeAddHandler(this.handlers, eventKey, func, thisObj, true);
    }

    /**
     * 取消订阅
     * @param event 事件类型
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    off<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId: number | string = '',
    ): void {
        const eventKey = this.getEventKey(event, eventId);
        this._safeRemoveHandler(this.handlers, eventKey, func, thisObj);
    }

    /**
     * 触发事件
     * @param event 事件类型
     * @param data 事件数据
     */
    emit<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        data?: GameEventMap[T],
        eventId: number | string = '',
    ): void {
        const eventKey = this.getEventKey(event, eventId);
        const handlers = [...(this.handlers.get(eventKey) || [])]; // 创建副本避免处理过程中数组变化
        // 处理特定事件的监听器
        for (const { func, thisObj, once } of handlers) {
            func.call(thisObj, data);
            if (once) this.off(event as never, func as never, thisObj);
        }

        // 处理全局监听器
        if (this.globalHandlers) {
            this.globalHandlers.func.call(this.globalHandlers.thisObj, event, data);
        }
    }

    /**
     * 订阅所有事件
     * @param func 处理函数，接收事件名和数据
     * @param thisObj 函数执行的this对象
     */
    setGlobalListener(func: (event: string, data: any) => void, thisObj: any): void {
        // 检查是否已经存在相同的全局监听器
        if (this.globalHandlers && this.globalHandlers.func === func && this.globalHandlers.thisObj === thisObj) {
            console.warn(`全局事件监听器已存在，跳过重复添加`);
            return;
        }

        this.globalHandlers = { func, thisObj };
    }

    /**
     * 取消订阅所有事件
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    removeGlobalListener(func: (event: string, data: any) => void, thisObj: any): void {
        if (!this.globalHandlers) {
            console.warn(`全局事件监听器不存在，跳过删除`);
            return;
        }

        if (this.globalHandlers.func === func && this.globalHandlers.thisObj === thisObj) {
            this.globalHandlers = null;
        } else {
            console.warn(`全局事件监听器不匹配，跳过删除`);
        }
    }
    /**
     * 订阅实体事件
     * @param event 事件类型
     * @param entityId 实体ID
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     */
    onEntityEvent<T extends keyof GameEventMap & ECSEvent.EntityEvent>(
        entityId: number,
        event: T,
        func: (entityId: number, event: T, data: GameEventMap[T]) => void,
        thisObj: any,
    ): void {
        const eventKey = this.getEventKey(event as string, entityId);
        this._safeAddHandler(this.entityHandlers, eventKey, func, thisObj);
    }

    /**
     * 取消订阅实体事件
     * @param event 事件类型
     * @param entityId 实体ID
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    offEntityEvent<T extends keyof GameEventMap & ECSEvent.EntityEvent>(
        entityId: number,
        event: T,
        func: (entityId: number, event: T, data: GameEventMap[T]) => void,
        thisObj: any,
    ): void {
        const eventKey = this.getEventKey(event as string, entityId);
        this._safeRemoveHandler(this.entityHandlers, eventKey, func, thisObj);
    }
    /**
     * 触发实体事件
     * @param event 事件类型
     * @param entityId 实体ID
     * @param data 事件数据
     */
    emitEntityEvent<T extends keyof GameEventMap & ECSEvent.EntityEvent>(entityId: number, event: T, data?: GameEventMap[T]): void {
        const eventKey = this.getEventKey(event as string, entityId);
        const handlers = [...(this.entityHandlers.get(eventKey) || [])]; // 创建副本避免处理过程中数组变化
        // 处理特定事件的监听器
        for (const { func, thisObj, once } of handlers) {
            func.call(thisObj, entityId, event, data);
            if (once) this.offEntityEvent(entityId, event as never, func as never, thisObj);
        }
    }
    /**
     * 订阅渲染事件
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     */
    onRenderEvent<T extends keyof GameEventMap>(event: T, func: (data: GameEventMap[T]) => void, thisObj: any): void {
        const eventKey = this.getEventKey(event as string);
        this._safeAddHandler(this.renderHandlers, eventKey, func, thisObj);
    }
    /**
     * 取消订阅渲染事件
     * @param event 事件类型
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    offRenderEvent<T extends keyof GameEventMap>(event: T, func: (data: GameEventMap[T]) => void, thisObj: any): void {
        const eventKey = this.getEventKey(event as string);
        this._safeRemoveHandler(this.renderHandlers, eventKey, func, thisObj);
    }
    /**
     * 触发渲染事件
     */
    emitRenderEvent<T extends keyof GameEventMap>(event: T, data?: GameEventMap[T]): void {
        const eventKey = this.getEventKey(event as string);
        const handlers = [...(this.renderHandlers.get(eventKey) || [])]; // 创建副本避免处理过程中数组变化
        // 处理特定事件的监听器
        for (const { func, thisObj, once } of handlers) {
            func.call(thisObj, data);
            if (once) this.offRenderEvent(event as never, func as never, thisObj);
        }
    }
}
/**逻辑事件总线,只能监听,不能派发 */
export interface LogicEventBus {
    /**
     * 订阅所有事件
     * @param func 处理函数，接收事件名和数据
     * @param thisObj 函数执行的this对象
     */
    setGlobalListener(func: (event: string, data: any) => void, thisObj: any): void;
    /**
     * 取消订阅所有事件
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    removeGlobalListener(func: (event: string, data: any) => void, thisObj: any): void;
    /**
     * 订阅逻辑世界事件
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     * @param eventId 事件ID
     */
    on<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId?: number | string,
    ): void;
    /**
     * 取消订阅逻辑世界事件
     * @param event 事件类型
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     * @param eventId 事件ID
     */
    off<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId?: number | string,
    ): void;
    /**
     * 触发逻辑事件
     * @param event 事件类型
     * @param data 事件数据
     */
    emit<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        data?: Expand<GameEventMap[T]>,
        eventId?: number | string,
    ): void;
    /**
     * 一次性订阅逻辑世界事件
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     * @param eventId 事件ID
     */
    once<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId?: number | string,
    ): void;
}
/**只读逻辑事件总线 */
export interface ReadonlyLogicEventBus {
    /**
     * 订阅逻辑世界事件
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     * @param eventId 事件ID
     */
    on<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId?: number | string,
    ): void;
    /**
     * 取消订阅逻辑世界事件
     * @param event 事件类型
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     * @param eventId 事件ID
     */
    off<T extends keyof GameEventMap & (ECSEvent.BaseEvent | ECSEvent.GameEvent)>(
        event: T,
        func: (data: GameEventMap[T]) => void,
        thisObj: any,
        eventId?: number | string,
    ): void;

    /**
     * 订阅实体事件
     * @param event 事件类型
     * @param entityId 实体ID
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     */
    onEntityEvent<T extends keyof GameEventMap & ECSEvent.EntityEvent>(
        entityId: number,
        event: T,
        func: (entityId: number, event: T, data: GameEventMap[T]) => void,
        thisObj: any,
    ): void;

    /**
     * 取消订阅实体事件
     * @param event 事件类型
     * @param entityId 实体ID
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    offEntityEvent<T extends keyof GameEventMap & ECSEvent.EntityEvent>(
        entityId: number,
        event: T,
        func: (entityId: number, event: T, data: GameEventMap[T]) => void,
        thisObj: any,
    ): void;
}
/**渲染事件总线 */
export interface RenderEventBus {
    /**
     * 订阅渲染事件
     * @param event 事件类型
     * @param func 处理函数
     * @param thisObj 函数执行的this对象
     */
    onRenderEvent<T extends keyof GameEventMap & ECSEvent.RenderEvent>(event: T, func: (data: GameEventMap[T]) => void, thisObj: any): void;
    /**
     * 取消订阅渲染事件
     * @param event 事件类型
     * @param func 要移除的处理函数
     * @param thisObj 函数执行的this对象
     */
    offRenderEvent<T extends keyof GameEventMap & ECSEvent.RenderEvent>(event: T, func: (data: GameEventMap[T]) => void, thisObj: any): void;
    /**
     * 触发渲染事件
     */
    emitRenderEvent<T extends keyof GameEventMap & ECSEvent.RenderEvent>(event: T, data?: GameEventMap[T]): void;
}
