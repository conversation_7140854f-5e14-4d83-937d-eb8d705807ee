import { ECSViewBase } from '../renderWorld/ECSViewBase';
import { ECSViewRegistry } from '../registry/ECSViewRegistry';
import { LogicWorld, ReadonlyWorld, World } from './World';
import { RenderCmdBase } from '../renderWorld/RenderCmdBase';
import NodeComponent from '../components/NodeComponent';
import { CreateECSViewRenderCmd } from '../renderWorld/cmd/CreateECSViewRenderCmd';
import OpacityComponent from '../components/OpacityComponent';
import { ECSEvent, GameEventMap } from '../GameEvent';
import { EventBus, RenderEventBus } from './EventBus';
import { UITransformComponent } from '../components/UITransformComponent';
import { EcsAudioConfigHelper, AudioId } from '../config/conf/EcsAudioConfig';
import { audioInfoData } from '../../../audio/vo/AudioInfoData';
import { FullScreenShakeManager } from './FullScreenShakeManager';
import { IPoint } from '../define/EcsDefine';
import DragComponent from '../components/DragComponent';
import { DragBlock } from '../renderWorld/renderComponents/DragBlock';
import { Loader } from '../../../../base/Loader';
import { PlaceholderManager } from './PlaceholderManager';
import { RenderComponent } from '../components/RenderComponent';

/**
 * 渲染世界
 * 负责管理ECS视图的创建、更新、销毁和渲染命令的执行
 */
export class RenderWorld {
    // ==================== 私有属性 ====================

    /** 逻辑世界实例 */
    private _logicWorld: LogicWorld & ReadonlyWorld;

    /** 渲染根节点 */
    private _rootNode: cc.Node;

    /** 是否已经销毁 */
    private _isDestroy: boolean;

    /** 渲染实体映射表 */
    private _ecsViewMap: Map<number, ECSViewBase>;

    /** 渲染命令映射表 */
    private _renderCmdMap: Map<number, RenderCmdBase>;

    /** 渲染命令队列 */
    private _renderCmds: RenderCmdBase[];

    /** 待销毁实体集合 */
    private _destroyEntitySet: Set<number>;

    /** 被断开的实体组件缓存 */
    private _disconnectedEntityComponentCache: Map<number, Map<string, any>>;

    /** 占位节点管理器 */
    private _placeholderManager: PlaceholderManager;

    /** 全屏震动管理器 */
    public _fullScreenShakeManager: FullScreenShakeManager;

    /** 渲染事件总线 */
    public readonly eventBus: RenderEventBus;
    // ==================== 生命周期方法 ====================

    /**
     * 初始化渲染世界
     * @param world 逻辑世界实例
     * @param _rootNode 场景节点
     */
    public init(world: World, _rootNode: cc.Node): void {
        this._logicWorld = world as LogicWorld & ReadonlyWorld;
        this._rootNode = _rootNode;
        (this.eventBus as any) = new EventBus();
        this.initializeCollections();
        this.initializeManagers();
        this.initializeShakeManager();
        this.addEventListeners();
    }

    /**
     * 销毁渲染世界
     */
    public dispose(): void {
        this.destroyAllECSViews();
        this.cleanupRenderCommands();
        this.clearCollections();
        this.cleanupManagers();
        this.removeEventListeners();
        this.markAsDestroyed();
    }

    /**
     * 添加事件监听器
     */
    private addEventListeners(): void {
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE, this.__onSnapshotDone, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.PLAY_AUDIO, this.__onPlayAudio, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.STOP_AUDIO, this.__onStopAudio, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.FULL_SCREEN_SHAKE, this.__onFullScreenShake, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.PARENT_CHILD_CHANGE, this.__onParentChildChange, this);
    }

    /**
     * 移除事件监听器
     */
    private removeEventListeners(): void {
        this._logicWorld.eventBus.off(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE, this.__onSnapshotDone, this);
        this._logicWorld.eventBus.off(ECSEvent.BaseEvent.PLAY_AUDIO, this.__onPlayAudio, this);
        this._logicWorld.eventBus.off(ECSEvent.BaseEvent.STOP_AUDIO, this.__onStopAudio, this);
        this._logicWorld.eventBus.off(ECSEvent.BaseEvent.FULL_SCREEN_SHAKE, this.__onFullScreenShake, this);
        this._logicWorld.eventBus.off(ECSEvent.BaseEvent.PARENT_CHILD_CHANGE, this.__onParentChildChange, this);
    }

    /**
     * 更新渲染世界
     * @param dt 帧间隔时间
     */
    public update(dt: number): void {
        this.executeRenderCmds();
        this.updateEcsView();
    }

    // ==================== 公共API ====================

    /**
     * 创建渲染命令
     * @param entityId 实体ID
     * @returns 创建的渲染命令
     */
    public createRenderCmd(entityId: number): RenderCmdBase {
        const renderCmd = new CreateECSViewRenderCmd();
        renderCmd.initCmd(entityId, this, this._logicWorld);
        return renderCmd;
    }

    /**
     * 添加渲染命令
     * @param cmd 渲染命令
     */
    public addRenderCmd(cmd: RenderCmdBase): void {
        if (this._isDestroy || !cmd) {
            if (!cmd) {
                console.error('渲染命令为空,终止添加');
            }
            return;
        }

        this._renderCmdMap.set(cmd.entityId, cmd);

        if (this.tryAddAsSubCommand(cmd)) {
            return;
        }

        this._renderCmds.push(cmd);
    }

    /**
     * 移除渲染命令
     * @param cmd 渲染命令
     */
    public removeRenderCmd(cmd: RenderCmdBase): void {
        if (this._isDestroy) {
            return;
        }

        this._renderCmdMap.delete(cmd.entityId);
        this.redistributeSubCommands(cmd);
    }

    /**
     * 销毁实体
     * @param entityId 实体ID
     */
    public destroyEntity(entityId: number): void {
        const cmd = this._renderCmdMap.get(entityId);
        if (cmd) {
            this.handleRemoveRenderCommand(cmd, entityId);
        }

        this.destroyExistingView(entityId);
    }

    /**
     * 创建ECS视图（使用占位节点保证顺序）
     * @param ecsViewConfigId 视图配置ID
     * @param logicEntityId 逻辑实体ID
     * @param parentEntityId 父实体ID
     * @param children 子节点路径
     * @returns 创建的ECS视图
     */
    public async createECSView(
        ecsViewConfigId: string,
        logicEntityId: number,
        parentEntityId: number,
        children: string[],
    ): Promise<ECSViewBase | null> {
        if (this._isDestroy) {
            return null;
        }

        const ecsViewConfig = this.getECSViewConfig(ecsViewConfigId);
        if (!ecsViewConfig) {
            return null;
        }

        return await this.createViewWithPlaceholder(ecsViewConfig, logicEntityId, parentEntityId, children);
    }

    /**
     * 销毁ECS视图
     * @param view ECS视图
     */
    public destroyECSView(view: ECSViewBase): void {
        if (this._isDestroy || !view) {
            return;
        }

        this._ecsViewMap.delete(view.logicEntityId);
        view.dispose();
        this.destroyViewNode(view.node);
    }

    /**
     * 加载资源
     * @param bundleName 资源包名
     * @param paths 资源路径
     * @param type 资源类型
     * @returns 加载的资源
     */
    public async loadRes<T extends cc.Asset>(bundleName: string, paths: string, type?: typeof cc.Asset): Promise<T> {
        let res: T = null;
        const replaceResConfig = this.getReplaceResConfig(bundleName, paths);
        if (replaceResConfig.path) {
            res = await Loader.asyncLoadByBundle(replaceResConfig.bundle, replaceResConfig.path, type);
            if (this._isDestroy) return Promise.reject('RenderWorld已经被销毁');
        }
        return res;
    }

    /** 获取替换资源配置 */
    private getReplaceResConfig(bundle: string, path: string): { bundle: string; path: string } {
        const lowPerformanceResourceConfig = this._logicWorld.configCenter.getLowPerformanceResourceConfig('low_performance_resource_config');
        const key = `${bundle},${path}`;
        if (lowPerformanceResourceConfig && lowPerformanceResourceConfig[key]) {
            console.log(`[RenderWorld] 替换资源: ${key}`);
            return {
                bundle: lowPerformanceResourceConfig[key].bundle,
                path: lowPerformanceResourceConfig[key].path,
            };
        }
        return { bundle: bundle, path: path };
    }

    // ==================== 私有初始化方法 ====================

    /**
     * 初始化集合
     */
    private initializeCollections(): void {
        this._ecsViewMap = new Map();
        this._renderCmdMap = new Map();
        this._destroyEntitySet = new Set();
        this._disconnectedEntityComponentCache = new Map();
        this._renderCmds = [];
        this._isDestroy = false;
    }

    /**
     * 初始化管理器
     */
    private initializeManagers(): void {
        this._placeholderManager = new PlaceholderManager(this._ecsViewMap, this._rootNode);
    }

    private initializeShakeManager(): void {
        this._fullScreenShakeManager = new FullScreenShakeManager();
    }

    public get fullScreenShakeManager(): FullScreenShakeManager {
        return this._fullScreenShakeManager;
    }

    /**
     * 标记为已销毁
     */
    private markAsDestroyed(): void {
        this._isDestroy = true;
        this._placeholderManager = null;
    }

    // ==================== 事件管理 ====================

    /**
     * 快照恢复完成事件处理器
     */
    private __onSnapshotDone(): void {
        this._ecsViewMap.forEach((view, entityId) => {
            const node = view.node;
            if (!cc.isValid(node)) {
                console.error(`节点不存在: ${entityId}, view: ${view?.constructor?.name}`);
                this._ecsViewMap.delete(entityId);
                return;
            }
            const { nodeComp, transform, opacityComp } = this.getUpdateViewComponent(entityId);
            this.updateEcsViewNodeComponent(node, nodeComp, transform);
            this.updateEcsViewOpacityComponent(node, opacityComp);
        });
    }

    // ==================== 渲染命令管理 ====================

    /**默认每帧预算，可按需调整*/
    private _frameBudgetMs = 16;
    /**逐帧执行渲染命令，控制在可配置的时间预算内，避免瞬时卡顿 */
    public executeRenderCmds(): void {
        if (this._isDestroy || this._renderCmds.length === 0) return;

        const start = performance.now ? performance.now() : Date.now();
        // 在预算时间内尽可能多执行命令
        while (this._renderCmds.length > 0) {
            const cmd = this._renderCmds.shift();
            cmd.execute();

            // 超出预算立即停
            const now = performance.now ? performance.now() : Date.now();
            if (now - start >= this._frameBudgetMs) break;
        }
    }

    /**
     * 尝试添加为子命令
     */
    private tryAddAsSubCommand(cmd: RenderCmdBase): boolean {
        if (!cmd.parentEntityId) {
            return false;
        }

        const parentCmd = this._renderCmdMap.get(cmd.parentEntityId);
        if (parentCmd) {
            parentCmd.subCmds.push(cmd);
            return true;
        }

        return false;
    }

    /**
     * 重新分配子命令
     */
    private redistributeSubCommands(cmd: RenderCmdBase): void {
        if (cmd.subCmds) {
            for (const subCmd of cmd.subCmds) {
                this.addRenderCmd(subCmd);
            }
        }
    }

    /**
     *  处理移除渲染命令
     */
    private handleRemoveRenderCommand(cmd: RenderCmdBase, entityId: number): void {
        if (!cmd.isExecuting) {
            this.removeCommandFromQueue(cmd);
        } else {
            this._destroyEntitySet.add(entityId);
        }
    }

    /**
     * 从队列中移除命令
     */
    private removeCommandFromQueue(cmd: RenderCmdBase): void {
        this._renderCmdMap.delete(cmd.entityId);
        const index = this._renderCmds.indexOf(cmd);
        if (index !== -1) {
            this._renderCmds.splice(index, 1);
        }
    }

    // ==================== ECS视图管理 ====================

    /**
     * 获取ECS视图配置
     */
    private getECSViewConfig(ecsViewConfigId: string): any {
        const ecsViewConfig = this._logicWorld.configCenter.getEcsViewConfig(ecsViewConfigId);
        if (!ecsViewConfig) {
            console.error(`ecsViewConfigId:${ecsViewConfigId} 不存在`);
            return null;
        }

        if (!ECSViewRegistry[ecsViewConfig.ecsViewName]) {
            console.error(`ecsViewName:${ecsViewConfig.ecsViewName} 不存在`);
            return null;
        }

        return ecsViewConfig;
    }

    /**
     * 使用占位节点创建视图
     */
    private async createViewWithPlaceholder(
        ecsViewConfig: any,
        logicEntityId: number,
        parentEntityId: number,
        children: string[],
    ): Promise<ECSViewBase | null> {
        // 创建占位节点
        this._placeholderManager.createPlaceholder(logicEntityId, parentEntityId, children);
        // 异步加载预制体
        const prefab = await this.loadViewPrefab(ecsViewConfig);
        if (!prefab) {
            this.handleViewCreationFailure(logicEntityId);
            return null;
        }

        // 检查是否在加载期间被标记为销毁
        if (this.isMarkedForDestroy(logicEntityId)) {
            this.handleViewCreationFailure(logicEntityId);
            return null;
        }
        // 创建真正的视图
        return this.createRealView(prefab, ecsViewConfig, logicEntityId);
    }

    /**
     * 加载视图预制体
     */
    private async loadViewPrefab(ecsViewConfig: any): Promise<cc.Prefab | null> {
        const prefab: cc.Prefab = await this.loadRes(ecsViewConfig.prefabBundleName, ecsViewConfig.prefabUrl, cc.Prefab);

        if (!prefab) {
            console.error(`prefab:${ecsViewConfig.prefabUrl} 不存在`);
        }

        return prefab;
    }

    /**
     * 检查是否被标记为销毁
     */
    private isMarkedForDestroy(logicEntityId: number): boolean {
        return this._destroyEntitySet.has(logicEntityId);
    }

    /**
     * 创建真正的视图
     */
    private createRealView(prefab: cc.Prefab, ecsViewConfig: any, logicEntityId: number): ECSViewBase {
        const node: cc.Node = cc.instantiate(prefab);
        if (node.name === 'emptyNode') node.name = ecsViewConfig.ecsViewId;
        const view: ECSViewBase = node.addComponent(ECSViewRegistry[ecsViewConfig.ecsViewName]);

        view.init(this._logicWorld, this, logicEntityId, ecsViewConfig);

        this.createDragBlock(node, logicEntityId);
        // 如果配置了蒙版，创建蒙版
        this.createMaskIfNeeded(node, ecsViewConfig);

        // 用真实节点替换占位节点
        this._placeholderManager.replacePlaceholder(logicEntityId, node);

        this._ecsViewMap.set(logicEntityId, view);

        // 通知视图创建完成
        this._logicWorld.eventBus.emit(ECSEvent.BaseEvent.RENDER_CREATED, logicEntityId);

        // 初始化视图状态
        this.initializeViewState(node, logicEntityId);

        return view;
    }

    /**
     * 创建蒙版（如果需要的话）
     */
    private async createMaskIfNeeded(node: cc.Node, ecsViewConfig: any): Promise<void> {
        if (!ecsViewConfig.mask) {
            return;
        }

        try {
            const maskPrefab: cc.Prefab = await this.loadRes(
                ecsViewConfig.mask.prefabBundleName || '',
                ecsViewConfig.mask.prefabUrl || 'prefabs/modal/Modal',
                cc.Prefab,
            );

            if (maskPrefab) {
                const maskNode = cc.instantiate(maskPrefab);
                maskNode.name = 'mask';

                // 设置蒙版透明度
                if (ecsViewConfig.mask.opacity !== undefined) {
                    maskNode.opacity = (ecsViewConfig.mask.opacity || 0.5) * 255;
                }

                // 将蒙版添加为子节点
                node.addChild(maskNode);

                // 确保蒙版在最后面
                maskNode.zIndex = -1000;
            }
        } catch (error) {
            console.error(`创建蒙版失败: ${ecsViewConfig.mask.prefabUrl}`, error);
        }
    }

    private createDragBlock(node: cc.Node, logicEntityId: number) {
        const dragComp = this._logicWorld.getComponent(logicEntityId, DragComponent);
        if (dragComp) {
            node.addComponent(DragBlock)?.setData(logicEntityId, this._logicWorld);
        }
    }

    /**
     * 初始化视图状态
     */
    private initializeViewState(node: cc.Node, logicEntityId: number): void {
        const { nodeComp, transform, opacityComp } = this.getUpdateViewComponent(logicEntityId);
        this.updateEcsViewNodeComponent(node, nodeComp, transform);
        this.updateEcsViewOpacityComponent(node, opacityComp);
    }
    /** 获取更新视图的相关组件 */
    private getUpdateViewComponent(logicEntityId: number): any {
        if (this._disconnectedEntityComponentCache.has(logicEntityId)) {
            return this._disconnectedEntityComponentCache.get(logicEntityId);
        }
        const nodeComp = this._logicWorld.getComponent(logicEntityId, NodeComponent);
        const transform = this._logicWorld.getComponent(logicEntityId, UITransformComponent);
        const opacityComp = this._logicWorld.getComponent(logicEntityId, OpacityComponent);
        return { nodeComp, transform, opacityComp };
    }

    /** 处理断开连接的实体组件 */
    public handleDisconnectedEntityComponent(logicEntityId: number, renderComponent: DeepReadonly<RenderComponent>): void {
        if (renderComponent.isBreakLink) {
            this._disconnectedEntityComponentCache.set(logicEntityId, this.getUpdateViewComponent(logicEntityId));
        }
    }
    /**
     * 处理视图创建失败
     */
    private handleViewCreationFailure(logicEntityId: number): void {
        this._destroyEntitySet.delete(logicEntityId);
        this._placeholderManager.removePlaceholder(logicEntityId);
        this._logicWorld.eventBus.emit(ECSEvent.BaseEvent.RENDER_CREATED_FAILED, logicEntityId);
    }

    /**
     * 销毁现有视图
     */
    private destroyExistingView(entityId: number): void {
        const view = this._ecsViewMap.get(entityId);
        if (view) {
            this.destroyECSView(view);
        }
    }

    /**
     * 销毁视图节点
     */
    private destroyViewNode(node: cc.Node): void {
        if (!cc.isValid(node)) {
            return;
        }
        node.removeFromParent();
        node.destroy();
    }

    /**获取渲染实体的世界坐标 */
    public getRenderEntityWorldPosition(entity: number, path?: string): IPoint {
        const node = this._ecsViewMap.get(entity)?.node;
        if (!node) return cc.Vec2.ZERO;
        const targetNode = path ? cc.find(path, node) : node;
        targetNode.convertToWorldSpaceAR(cc.Vec2.ZERO, this.tempOutV2);
        return { x: this.tempOutV2.x, y: this.tempOutV2.y };
    }

    /**获取渲染实体相对渲染根节点的坐标 */
    public getRenderEntityRootPosition(entity: number, path?: string): IPoint {
        const worldPos = this.getRenderEntityWorldPosition(entity, path);
        this.tempOutV2.x = worldPos.x;
        this.tempOutV2.y = worldPos.y;
        this._rootNode.convertToNodeSpaceAR(this.tempOutV2, this.tempOutV2);
        return { x: this.tempOutV2.x, y: this.tempOutV2.y };
    }

    private tempOutV2 = cc.v2();

    // ==================== 清理方法 ====================

    /**
     * 销毁所有ECS视图
     */
    private destroyAllECSViews(): void {
        this._ecsViewMap.forEach((view) => {
            this.destroyECSView(view);
        });
    }

    /**
     * 清理渲染命令
     */
    private cleanupRenderCommands(): void {
        this._renderCmds.forEach((cmd) => {
            if (cmd && cmd.isExecuting) {
                this._destroyEntitySet.add(cmd.entityId);
            }
        });
    }

    /**
     * 清理集合
     */
    private clearCollections(): void {
        this._ecsViewMap.clear();
        this._renderCmdMap.clear();
        this._renderCmds.length = 0;
    }

    /**
     * 清理管理器
     */
    private cleanupManagers(): void {
        if (this._placeholderManager) {
            this._placeholderManager.clearPool();
        }
    }

    // ==================== 组件更新 ====================

    /**
     * 更新ECS视图关键节点数据
     */
    private updateEcsView(): void {
        this._ecsViewMap.forEach((view, entityId) => {
            const node = view.node;
            if (!cc.isValid(node)) {
                console.error(`节点不存在: ${entityId}, view: ${view?.constructor?.name}`);
                return;
            }
            const { nodeComp, transform, opacityComp } = this.getUpdateViewComponent(entityId);
            this.updateEcsViewNodeComponent(node, nodeComp, transform);
            this.updateEcsViewOpacityComponent(node, opacityComp);
        });
    }

    /**
     * 更新节点组件数据
     * @param node 节点
     * @param entityId 实体ID
     */
    updateEcsViewNodeComponent(node: cc.Node, nodeComp: NodeComponent, transform: UITransformComponent): void {
        if (!nodeComp) {
            return;
        }

        this.updateNodePosition(node, nodeComp);
        this.updateNodeScale(node, nodeComp);
        this.updateNodeRotation(node, nodeComp);
        this.updateNodeZIndex(node, nodeComp);
        if (transform) {
            this.updateTransform(node, transform);
        }
    }

    updateNodeZIndex(node: cc.Node, nodeComp: NodeComponent): void {
        if (node.zIndex !== nodeComp.zIndex) {
            node.zIndex = nodeComp.zIndex;
            // 触发父节点重新排序子节点
            if (node.parent) {
                node.parent.sortAllChildren();
            }
        }
    }

    updateTransform(node: cc.Node, nodeComp: UITransformComponent): void {
        if (node.width !== nodeComp.width || node.height !== nodeComp.height) {
            node.width = nodeComp.width;
            node.height = nodeComp.height;
        }
    }

    /**
     * 更新透明度组件数据
     * @param node 节点
     * @param entityId 实体ID
     */
    updateEcsViewOpacityComponent(node: cc.Node, opacityComp: OpacityComponent): void {
        if (!opacityComp) {
            return;
        }

        if (node.opacity !== opacityComp.opacity) {
            node.opacity = opacityComp.opacity;
        }
    }

    // ==================== 节点更新辅助方法 ====================

    /**
     * 更新节点位置
     */
    private updateNodePosition(node: cc.Node, nodeComp: any): void {
        const oldPos = node.position;
        if (oldPos.x !== nodeComp.x || oldPos.y !== nodeComp.y) {
            node.setPosition(nodeComp.x, nodeComp.y);
        }
    }

    /**
     * 更新节点缩放
     */
    private updateNodeScale(node: cc.Node, nodeComp: any): void {
        if (node.scaleX !== nodeComp.scaleX || node.scaleY !== nodeComp.scaleY) {
            node.setScale(nodeComp.scaleX, nodeComp.scaleY);
        }
    }

    /**
     * 更新节点旋转
     */
    private updateNodeRotation(node: cc.Node, nodeComp: any): void {
        if (node.angle !== nodeComp.angle) {
            node.angle = nodeComp.angle;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 等待指定时间（测试用）
     * @param time 等待时间（毫秒）
     * @returns Promise
     */
    private waitTime(time: number): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(resolve, time);
        });
    }

    // ==================== 音效方法 ====================

    private __onPlayAudio(data: { audioId: AudioId | string; options?: Partial<falcon.IAudioInfoOption & { forcePlay?: boolean }> }): void {
        this.playAudio(data.audioId, data.options);
    }
    private __onStopAudio(data: { audioId: AudioId | string }): void {
        this.stopAudio(data.audioId);
    }
    /**
     * 播放音效
     * @param audioId 音效配置ID
     * @param options 覆盖选项
     * @returns 是否成功播放
     */
    public playAudio(audioId: AudioId | string, options?: Partial<falcon.IAudioInfoOption & { forcePlay?: boolean }>): boolean {
        // 获取音效配置
        const config = EcsAudioConfigHelper.getAudioConfig(audioId);
        if (!config) {
            console.warn(`音效配置不存在: ${audioId}`);
            return false;
        }
        // 检查音效开关
        if (!audioInfoData.audioSwitch && !options?.forcePlay) {
            console.log(`音效已关闭，跳过播放: ${audioId}`);
            return false;
        }
        // 合并配置选项
        const finalConfig = { ...config, ...options };
        try {
            // 构建falcon音效播放参数
            const audioOptions = {
                url: finalConfig.url,
                volume: finalConfig.volume || 1,
                type: finalConfig.type,
                bundleName: finalConfig.bundleName,
            };
            // 播放音效
            falcon.audioInfo.play(audioOptions);
            console.log(`播放音效成功: ${audioId}`);
            return true;
        } catch (error) {
            console.error(`播放音效失败: ${audioId}`, error);
            return false;
        }
    }

    /**
     * 停止音效
     * @param audioId 音效配置ID
     * @returns 是否成功停止
     */
    public stopAudio(audioId: AudioId | string): boolean {
        const config = EcsAudioConfigHelper.getAudioConfig(audioId);
        if (!config) {
            console.warn(`音效配置不存在: ${audioId}`);
            return false;
        }
        try {
            falcon.audioInfo.stop({
                url: config.url,
                type: config.type,
            });
            console.log(`停止音效成功: ${audioId}`);
            return true;
        } catch (error) {
            console.error(`停止音效失败: ${audioId}`, error);
            return false;
        }
    }

    // ==================== 全屏震动管理 ====================
    private __onFullScreenShake(data: GameEventMap[ECSEvent.BaseEvent.FULL_SCREEN_SHAKE]): void {
        this._fullScreenShakeManager?.startWithAmplitude(data.duration, data.amplitude, data.frequency);
    }

    // ==================== 父子关系变化 ====================
    private __onParentChildChange(data: GameEventMap[ECSEvent.BaseEvent.PARENT_CHILD_CHANGE]): void {
        const renderComp = this._logicWorld.getComponent(data.target, RenderComponent);
        const viewNode = this._ecsViewMap.get(data.source).node;
        const parentNode = renderComp.childrenPaths.length ? cc.find(renderComp.childrenPaths.join('/'), viewNode) : viewNode;
        this._ecsViewMap.get(data.target).node.setParent(parentNode);
    }
}

// ==================== 占位节点管理器 ====================
