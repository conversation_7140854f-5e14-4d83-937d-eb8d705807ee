import { ECSViewBase } from "../renderWorld/ECSViewBase";

/**
 * 占位节点管理器
 * 负责管理异步加载过程中的占位节点，确保渲染顺序的正确性
 */
export class PlaceholderManager {
    // ==================== 私有属性 ====================

    /** 实体ID -> 占位节点映射 */
    private _placeholders: Map<number, cc.Node> = new Map();

    /** ECS视图映射表引用 */
    private _ecsViewMap: Map<number, ECSViewBase>;

    /** 节点池 */
    private _nodePool: cc.NodePool;

    /** 渲染根节点 */
    private _rootNode: cc.Node;

    /** 路径占位节点映射：路径 -> 占位节点 */
    private _pathPlaceholders: Map<string, cc.Node> = new Map();

    // ==================== 构造函数 ====================

    /**
     * 构造占位节点管理器
     * @param ecsViewMap ECS视图映射表
     * @param rootNode 渲染根节点
     */
    constructor(ecsViewMap: Map<number, ECSViewBase>, rootNode: cc.Node) {
        this._ecsViewMap = ecsViewMap;
        this._nodePool = new cc.NodePool();
        this._rootNode = rootNode;
    }

    // ==================== 公共API ====================

    /**
     * 创建占位节点
     * @param entityId 实体ID
     * @param parentEntityId 父实体ID
     * @param children 子节点路径
     * @returns 占位节点
     */
    public createPlaceholder(entityId: number, parentEntityId: number, children: string[]): cc.Node {
        const parentNode = this.findParentNode(parentEntityId, children);
        if (!parentNode) {
            console.error(`父节点不存在: ${parentEntityId}`);
            return null;
        }

        // 检查目标位置是否已经有路径占位节点
        const targetPath = this.buildTargetPath(parentEntityId, children);
        const existingPathPlaceholder = this._pathPlaceholders.get(targetPath);

        if (existingPathPlaceholder && cc.isValid(existingPathPlaceholder)) {
            // 复用已存在的路径占位节点
            console.log(`复用路径占位节点作为实体 ${entityId} 的占位节点: ${targetPath}`);
            this._placeholders.set(entityId, existingPathPlaceholder);
            // 从路径占位映射中移除，因为现在它成为了实体占位节点
            this._pathPlaceholders.delete(targetPath);
            return existingPathPlaceholder;
        }

        // 没有现有的路径占位节点，创建新的实体占位节点
        const placeholder = this.getOrCreatePlaceholderNode();
        parentNode.addChild(placeholder);
        this._placeholders.set(entityId, placeholder);

        return placeholder;
    }

    /**
     * 构建目标路径
     * @param parentEntityId 父实体ID
     * @param children 子节点路径
     * @returns 完整的目标路径
     */
    private buildTargetPath(parentEntityId: number, children: string[]): string {
        let basePath: string;

        if (parentEntityId === 0) {
            basePath = this._rootNode.name;
        } else {
            const parentView = this.getParentView(parentEntityId);
            if (parentView) {
                basePath = `entity_${parentEntityId}`;
            } else {
                basePath = this._rootNode.name; // 父实体不存在，使用根节点
            }
        }

        if (!children || children.length === 0) {
            return basePath;
        }

        return `${basePath}/${children.join('/')}`;
    }

    /**
     * 替换占位节点
     * @param entityId 实体ID
     * @param realNode 真实节点
     */
    public replacePlaceholder(entityId: number, realNode: cc.Node): void {
        const placeholder = this._placeholders.get(entityId);
        if (!placeholder || !cc.isValid(placeholder)) {
            return;
        }

        const parent = placeholder.parent;
        if (!parent) {
            return;
        }

        // 检查是否有路径占位节点需要被这个真实节点替换
        this.checkAndReplacePathPlaceholders(realNode, entityId);

        this.replaceNodeAtSameIndex(placeholder, realNode, parent);
        this.recyclePlaceholder(placeholder);
        this._placeholders.delete(entityId);
    }

    /**
     * 检查并替换相关的路径占位节点
     * @param realNode 真实节点
     * @param entityId 实体ID
     */
    private checkAndReplacePathPlaceholders(realNode: cc.Node, entityId: number): void {
        const entityPath = `entity_${entityId}`;

        // 查找所有以这个实体为起始路径的路径占位节点
        const pathsToReplace: string[] = [];
        this._pathPlaceholders.forEach((pathPlaceholder, path) => {
            if (path.startsWith(entityPath + '/')) {
                pathsToReplace.push(path);
            }
        });

        // 替换这些路径占位节点
        pathsToReplace.forEach((path) => {
            const pathPlaceholder = this._pathPlaceholders.get(path);
            if (pathPlaceholder && cc.isValid(pathPlaceholder)) {
                // 计算相对路径
                const relativePath = path.substring(entityPath.length + 1);
                const targetNode = this.findNodeByPath(realNode, relativePath);

                if (targetNode) {
                    this.replacePathPlaceholder(targetNode, path);
                } else {
                    console.warn(`无法在真实节点中找到路径: ${relativePath}`);
                }
            }
        });
    }

    /**
     * 根据路径查找节点
     * @param startNode 起始节点
     * @param path 相对路径
     * @returns 找到的节点或null
     */
    private findNodeByPath(startNode: cc.Node, path: string): cc.Node | null {
        if (!path) {
            return startNode;
        }

        const pathParts = path.split('/');
        let currentNode = startNode;

        for (const part of pathParts) {
            currentNode = currentNode.getChildByName(part);
            if (!currentNode) {
                return null;
            }
        }

        return currentNode;
    }

    /**
     * 移除占位节点
     * @param entityId 实体ID
     */
    public removePlaceholder(entityId: number): void {
        const placeholder = this._placeholders.get(entityId);
        if (placeholder && cc.isValid(placeholder)) {
            placeholder.removeFromParent();
            this._nodePool.put(placeholder);
        }
        this._placeholders.delete(entityId);
    }

    /**
     * 清理节点池
     */
    public clearPool(): void {
        this._nodePool.clear();
        this.clearPathPlaceholders();
    }

    // ==================== 私有方法 ====================

    /**
     * 查找父节点
     */
    private findParentNode(parentEntityId: number, children: string[]): cc.Node {
        const parentView = this.getParentView(parentEntityId);
        let parentNode: cc.Node = (parentView && parentView.node) || this._rootNode;

        if (!parentNode) {
            return null;
        }

        return this.navigateToChildNode(parentNode, children);
    }

    /**
     * 获取或创建占位节点
     */
    private getOrCreatePlaceholderNode(): cc.Node {
        if (this._nodePool.size() > 0) {
            return this._nodePool.get();
        }

        return this.createNewPlaceholderNode();
    }

    /**
     * 创建新的占位节点
     */
    private createNewPlaceholderNode(): cc.Node {
        const placeholder = new cc.Node('Placeholder');
        placeholder.width = 0;
        placeholder.height = 0;
        placeholder.opacity = 0; // 完全透明
        return placeholder;
    }

    /**
     * 在相同索引位置替换节点（包含子节点迁移）
     */
    private replaceNodeAtSameIndex(placeholder: cc.Node, realNode: cc.Node, parent: cc.Node): void {
        const index = parent.children.indexOf(placeholder);

        // 将占位节点的所有子节点迁移到真实节点
        this.migrateChildrenNodes(placeholder, realNode);

        // 替换节点
        placeholder.removeFromParent();
        parent.insertChild(realNode, index);
    }

    /**
     * 迁移子节点
     * @param fromNode 源节点（占位节点）
     * @param toNode 目标节点（真实节点）
     */
    private migrateChildrenNodes(fromNode: cc.Node, toNode: cc.Node): void {
        if (!fromNode || !toNode) {
            return;
        }

        // 获取所有子节点的副本，避免在迭代过程中修改数组
        const children = [...fromNode.children];

        if (children.length > 0) {
            console.log(`迁移 ${children.length} 个子节点从占位节点到真实节点`);
        }

        // 直接迁移所有子节点
        children.forEach((child) => {
            if (cc.isValid(child)) {
                child.removeFromParent();
                toNode.addChild(child);
            }
        });
    }

    /**
     * 回收占位节点
     */
    private recyclePlaceholder(placeholder: cc.Node): void {
        this._nodePool.put(placeholder);
    }

    /**
     * 获取父视图
     * @param parentEntityId 父实体ID
     * @returns 父视图或null
     */
    private getParentView(parentEntityId: number): ECSViewBase | null {
        return this._ecsViewMap.get(parentEntityId);
    }

    /**
     * 导航到子节点（支持路径占位创建）
     */
    private navigateToChildNode(parentNode: cc.Node, children: string[]): cc.Node {
        if (!children || children.length === 0) {
            return parentNode;
        }

        let currentNode = parentNode;
        let currentPath = this.getNodePath(parentNode);

        for (let i = 0; i < children.length; i++) {
            const childName = children[i];
            if (!currentNode) {
                console.error(`父节点为空，无法继续查找子节点路径: ${children.slice(0, i).join('/')}`);
                return null;
            }

            // 构建当前子节点的完整路径
            const childPath = `${currentPath}/${childName}`;

            // 先尝试找到真实的子节点
            let nextNode = currentNode.getChildByName(childName);

            if (!nextNode) {
                // 检查是否已经有路径占位节点
                nextNode = this._pathPlaceholders.get(childPath);

                if (!nextNode) {
                    // 创建新的路径占位节点
                    console.log(`创建路径占位节点: ${childPath}`);
                    nextNode = this.createPathPlaceholderNode(childName);
                    currentNode.addChild(nextNode);
                    this._pathPlaceholders.set(childPath, nextNode);
                } else {
                    console.log(`使用已存在的路径占位节点: ${childPath}`);
                }
            }

            currentNode = nextNode;
            currentPath = childPath;
        }

        return currentNode;
    }

    /**
     * 获取节点的路径标识
     */
    private getNodePath(node: cc.Node): string {
        if (node === this._rootNode) {
            return 'root';
        }

        // 尝试从ECS视图映射中找到对应的实体ID
        for (const [entityId, view] of this._ecsViewMap) {
            if (view.node === node) {
                return `entity_${entityId}`;
            }
        }

        // 如果找不到，使用节点名称
        return node.name || 'unknown';
    }

    /**
     * 创建路径占位节点
     */
    private createPathPlaceholderNode(name: string): cc.Node {
        const placeholder = new cc.Node(`PathPlaceholder_${name}`);
        placeholder.width = 0;
        placeholder.height = 0;
        placeholder.opacity = 0; // 完全透明
        // 添加标记，表示这是路径占位节点
        placeholder['__isPathPlaceholder'] = true;
        return placeholder;
    }

    /**
     * 替换路径占位节点
     * @param realNode 真实节点
     * @param targetPath 目标路径
     */
    public replacePathPlaceholder(realNode: cc.Node, targetPath: string): void {
        const placeholder = this._pathPlaceholders.get(targetPath);
        if (!placeholder || !cc.isValid(placeholder)) {
            return;
        }

        const parent = placeholder.parent;
        if (!parent) {
            return;
        }

        console.log(`替换路径占位节点: ${targetPath}`);

        // 将占位节点的所有子节点转移到真实节点
        const children = [...placeholder.children];
        children.forEach((child) => {
            child.removeFromParent();
            realNode.addChild(child);
        });

        // 替换占位节点
        this.replaceNodeAtSameIndex(placeholder, realNode, parent);

        // 清理占位节点记录
        this._pathPlaceholders.delete(targetPath);
        placeholder.destroy();
    }

    /**
     * 清理路径占位节点
     */
    public clearPathPlaceholders(): void {
        this._pathPlaceholders.forEach((placeholder) => {
            if (cc.isValid(placeholder)) {
                placeholder.destroy();
            }
        });
        this._pathPlaceholders.clear();
    }
}
