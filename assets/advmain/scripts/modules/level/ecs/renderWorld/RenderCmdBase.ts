import { RenderComponent } from '../components/RenderComponent';
import { RenderWorld } from '../cores/RenderWorld';
import { ReadonlyWorld } from '../cores/World';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { EcsViewId } from './ECSViewBase';

export type IRenderCmdData = any;
/**渲染命令基类 */
export abstract class RenderCmdBase {
    /**子命令队列 */
    public subCmds: RenderCmdBase[] = [];
    /**是否执行中 */
    public isExecuting: boolean;

    /**实体id */
    public entityId: number;
    /**父实体id */
    public parentEntityId: number;
    /**ecs视图id */
    public ecsViewId: EcsViewId;
    /**子节点路径 */
    public children: DeepReadonly<string[]>;
    /**数据 */
    public data: any;
    /**渲染世界 */
    public renderWorld: RenderWorld;
    /**世界 */
    public world: ReadonlyWorld;

    initCmd(entityId: number, renderWorld: RenderWorld, world: ReadonlyWorld) {
        this.entityId = entityId;
        this.renderWorld = renderWorld;
        this.world = world;
        const renderComponent = this.world.getComponent(entityId, RenderComponent);
        if (!renderComponent) {
            console.error(`entityId:${entityId} 不存在渲染组件`);
            return;
        }
        this.data = renderComponent.data;
        this.parentEntityId = world.getSource(entityId, RelationName.PARENT_CHILD);
        this.ecsViewId = renderComponent.ecsViewId;
        this.children = renderComponent.childrenPaths || [];
        this.renderWorld.handleDisconnectedEntityComponent(entityId, renderComponent);
    }
    abstract do();
    async execute() {
        this.isExecuting = true;
        await this.do();
        this.isExecuting = false;
        this.renderWorld.removeRenderCmd(this);
    }
}
