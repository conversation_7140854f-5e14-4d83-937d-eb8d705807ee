import BoardComponent from '../../../components/board/BoardComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { ECSViewBase } from '../../ECSViewBase';

export default class DiamondECSView extends ECSViewBase {
    protected initView() {
        this.fresh();
    }
    protected addEvents() {
        // this.logicWorld.onComponentChange(this.logicEntityId, HPComponent, this.onHpChange, this);
    }
    protected removeEvents() {
        // this.logicWorld.offComponentChange(this.logicEntityId, HPComponent, this.onHpChange, this);
    }
    protected disposeView() {}
    private fresh() {
        const parentEntity = this.logicWorld.getSource(this.logicEntityId, RelationName.PARENT_CHILD);
        this.renderWorld
            .loadRes('level', `cell/130/img/icon${this.logicWorld.getComponent(parentEntity, BoardComponent) ? 'Board' : 'Shape'}`, cc.SpriteFrame)
            .then((spf: cc.SpriteFrame) => {
                if (!cc.isValid(this.node)) return;
                this.ui.icon.getComponent(cc.Sprite).spriteFrame = spf;
            });
    }
}
