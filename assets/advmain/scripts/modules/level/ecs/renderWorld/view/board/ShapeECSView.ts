import ShapeComponent from '../../../components/board/ShapeComponent';
import DragComponent from '../../../components/DragComponent';
import NodeComponent from '../../../components/NodeComponent';
import { BoardLayer } from '../../../define/BoardDefine';
import { IPoint } from '../../../define/EcsDefine';
import { ECSEvent } from '../../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';
import { DragBlock } from '../../renderComponents/DragBlock';
export interface IShapeECSViewConfig extends IECSViewConfig {
    putAudioId: string;
}
export default class ShapeECSView extends ECSViewBase<IShapeECSViewConfig> {
    private hotAreaDebugNode: cc.Node = null;
    private static SHOW_HOT_AREA_DEBUG = false; // 调试开关

    protected initView() {
        for (const k in BoardLayer) {
            if (!isNaN(+k)) continue;
            const node = new cc.Node(k);
            node.zIndex = +BoardLayer[k] * 10;
            node.setParent(this.node);
        }
        this.setNodeUItransform();
        const dragComp = this.logicWorld.getComponent(this.logicEntityId, DragComponent);
        if (dragComp) {
            // 创建热区调试可视化
            if (ShapeECSView.SHOW_HOT_AREA_DEBUG) {
                this.createHotAreaDebugNode();
            }
        }
    }
    protected addEvents() {
        this.logicWorld.eventBus.onEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.PUT_BLOCK_TWEEN, this.tweenNode, this);
        this.logicWorld.eventBus.onEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.PUT_BLOCK_TWEEN, this.onDragStart, this);
    }
    protected removeEvents() {
        this.logicWorld.eventBus.offEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.PUT_BLOCK_TWEEN, this.tweenNode, this);
        this.logicWorld.eventBus.offEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.PUT_BLOCK_TWEEN, this.onDragStart, this);
    }
    protected disposeView() {
        if (this.hotAreaDebugNode) {
            this.hotAreaDebugNode.removeFromParent();
            this.hotAreaDebugNode = null;
        }
    }
    protected update(dt: number): void {
        this.node.scale = this.logicWorld.getComponent(this.logicEntityId, NodeComponent).scaleX;

        // 更新热区调试可视化
        if (this.hotAreaDebugNode && ShapeECSView.SHOW_HOT_AREA_DEBUG) {
            this.updateHotAreaDebugNode();
        }
    }

    onDragStart(entityId: number, event: ECSEvent.EntityEvent, targetPos: IPoint) {
        this.renderWorld.playAudio(this.config.putAudioId);
    }

    tweenNode(entityId: number, event: ECSEvent.EntityEvent, targetPos: IPoint) {
        // 平滑移动并隐藏
        this.node.stopAllActions();
        cc.tween(this.node)
            .to(0.05, { position: cc.v3(targetPos.x, targetPos.y) }, { easing: 'cubicOut' })
            .call(() => {
                this.node.removeFromParent();
                this.node.destroy();
            })
            .start();
    }

    setNodeUItransform() {
        let shapeEntity = this.logicWorld.getComponent(this.logicEntityId, ShapeComponent);
        this.node.width = shapeEntity.shape.width * 106;
        this.node.height = shapeEntity.shape.height * 106;
    }

    /** 创建热区调试节点 */
    private createHotAreaDebugNode(): void {
        this.hotAreaDebugNode = new cc.Node('HotAreaDebug');
        this.hotAreaDebugNode.zIndex = 1000; // 确保在最上层显示

        // 添加Graphics组件来绘制边框
        const graphics = this.hotAreaDebugNode.addComponent(cc.Graphics);
        graphics.lineWidth = 4;
        graphics.strokeColor = cc.Color.RED;
        graphics.fillColor = new cc.Color(255, 0, 0, 50); // 半透明红色填充

        this.hotAreaDebugNode.setParent(this.node);
        this.updateHotAreaDebugNode();
    }

    /** 更新热区调试节点 */
    private updateHotAreaDebugNode(): void {
        if (!this.hotAreaDebugNode) return;

        const dragComp = this.logicWorld.getComponent(this.logicEntityId, DragComponent);
        if (!dragComp) return;

        const hotArea = dragComp.hotArea;
        const graphics = this.hotAreaDebugNode.getComponent(cc.Graphics);

        graphics.clear();

        // 热区以方块中心为基准，hotArea.x/y 是相对于中心的偏移
        // 但通常hotArea是{x:0, y:0, width:600, height:1200}，表示以中心为基准的矩形
        const rectX = -hotArea.width / 2; // 左边界
        const rectY = -hotArea.height / 2; // 下边界

        // 绘制热区边框和填充（以方块中心为原点）
        graphics.rect(rectX, rectY, hotArea.width, hotArea.height);
        graphics.fill();
        graphics.stroke();

        // 添加文字标签显示热区尺寸
        this.updateHotAreaLabel({ x: rectX, y: rectY, width: hotArea.width, height: hotArea.height });
    }

    /** 更新热区标签 */
    private updateHotAreaLabel(hotArea: { x: number; y: number; width: number; height: number }): void {
        let labelNode = this.hotAreaDebugNode.getChildByName('SizeLabel');
        if (!labelNode) {
            labelNode = new cc.Node('SizeLabel');
            const label = labelNode.addComponent(cc.Label);
            label.fontSize = 24;
            label.node.color = cc.Color.WHITE;
            labelNode.setParent(this.hotAreaDebugNode);
        }

        const label = labelNode.getComponent(cc.Label);
        label.string = `${Math.round(hotArea.width)}x${Math.round(hotArea.height)}`;

        // 将标签放在热区中心
        labelNode.setPosition(hotArea.x + hotArea.width / 2, hotArea.y + hotArea.height / 2);
    }
}
