import { ECSEvent } from '../../GameEvent';
import { ECSViewBase } from '../ECSViewBase';

/**
 * 资源缓存接口
 */
interface ResourceCache {
    blockPrefab: cc.Prefab;
    blocksDarkAtlas: cc.SpriteAtlas;
    gemsAtlas: cc.SpriteAtlas;
}

/**
 * 复活块特效视图
 * 播放行列特效
 */
export class ReviveBlockEffectECSView extends ECSViewBase {

    // 资源缓存
    private resourceCache: ResourceCache = {
        blockPrefab: null,
        blocksDarkAtlas: null,
        gemsAtlas: null
    };

    private createdBlockNodes: cc.Node[] = [];

    private static readonly BLOCKS_DARK_URL = "textures/blocks/blocks-dark";
    private static readonly GEMS_URL = "textures/blocks/Gems";
    private static readonly BLOCK_PREFAB_URL = "prefabs/level/block/Block";

    protected async initView(): Promise<void> {
        const { rowEffect, colEffect, blockDisplayData } = this.data;

        // 预加载资源
        await this.preloadResources();

        // 1. 创建块显示节点并播放动画
        if (blockDisplayData && blockDisplayData.length > 0) {
            const blockNodes = await this.createBlockDisplayNodes(blockDisplayData);
            this.createdBlockNodes.push(...blockNodes);
        }

        // 2. 创建行列特效
        if (rowEffect) {
            await this.createEffect(rowEffect);
        }

        if (colEffect) {
            await this.createEffect(colEffect);
        }

    }
    
    protected addEvents(): void {
        this.renderWorld.eventBus.onRenderEvent(ECSEvent.RenderEvent.REVIVE_CLOSE, this.onReviveClose, this);
    }

    protected removeEvents(): void {
        this.renderWorld.eventBus.offRenderEvent(ECSEvent.RenderEvent.REVIVE_CLOSE, this.onReviveClose, this);
    }

    protected disposeView(): void {
        console.log('ReviveBlockEffectECSView: 特效视图已销毁');
    }

    private onReviveClose(): void {
        this.deleteBlockNodes(this.createdBlockNodes);
        this.renderWorld.destroyECSView(this);
    }

    /**
     * 预加载所有需要的资源
     */
    private async preloadResources(): Promise<void> {
        console.log('开始预加载资源...');
        
        // 并行加载所有资源
        const [blockPrefab, blocksDarkAtlas, gemsAtlas] = await Promise.all([
            this.renderWorld.loadRes(this.config.prefabBundleName, ReviveBlockEffectECSView.BLOCK_PREFAB_URL, cc.Prefab),
            this.renderWorld.loadRes(this.config.prefabBundleName, ReviveBlockEffectECSView.BLOCKS_DARK_URL, cc.SpriteAtlas),
            this.renderWorld.loadRes(this.config.prefabBundleName, ReviveBlockEffectECSView.GEMS_URL, cc.SpriteAtlas)
        ]);

        // 验证资源加载结果
        if (!blockPrefab) {
            throw new Error('Block预制体加载失败');
        }
        if (!blocksDarkAtlas) {
            throw new Error('Blocks-dark图集加载失败');
        }
        if (!gemsAtlas) {
            throw new Error('Gems图集加载失败');
        }

        // 缓存资源
        this.resourceCache.blockPrefab = blockPrefab as cc.Prefab;
        this.resourceCache.blocksDarkAtlas = blocksDarkAtlas as cc.SpriteAtlas;
        this.resourceCache.gemsAtlas = gemsAtlas as cc.SpriteAtlas;

        console.log('资源预加载完成');
    }

    /**
     * 创建特效
     */
    private async createEffect(effectData: any): Promise<void> {
        // 加载特效 prefab
        const prefab = await this.renderWorld.loadRes('advres', 'prefabs/level/revive/clear_tips_node', cc.Prefab) as cc.Prefab;
        if (!prefab || !cc.isValid(this.node)) return;

        // 创建特效节点
        const effectNode = cc.instantiate(prefab);

        // 获取棋盘在世界坐标系中的位置
        const boardWorldPos = this.renderWorld.getRenderEntityWorldPosition(effectData.boardEntity);

        // 计算特效在世界坐标系中的位置
        const effectWorldX = boardWorldPos.x + effectData.x;
        const effectWorldY = boardWorldPos.y + effectData.y;

        // 转换为相对于当前视图的坐标
        const localPos = this.node.convertToNodeSpaceAR(cc.v2(effectWorldX, effectWorldY));
        effectNode.x = localPos.x;
        effectNode.y = localPos.y;
        effectNode.angle = effectData.angle;
        effectNode.scaleX = effectData.scaleX;

        // 添加到当前视图节点
        this.node.addChild(effectNode);

        console.log(`ReviveBlockEffectECSView: 创建特效 angle=${effectData.angle}`);
    }

    /**
     * 创建块显示节点
     */
    private async createBlockDisplayNodes(blockDisplayData: any[]): Promise<cc.Node[]> {
        const createdNodes: cc.Node[] = [];
        for (const blockData of blockDisplayData) {
            // 创建块节点
            const blockNode = cc.instantiate(this.resourceCache.blockPrefab);

            // 获取棋盘在世界坐标系中的位置
            const boardWorldPos = this.renderWorld.getRenderEntityWorldPosition(blockData.boardEntity);

            // 计算块在世界坐标系中的位置
            const blockWorldX = boardWorldPos.x + blockData.worldPosition.x;
            const blockWorldY = boardWorldPos.y + blockData.worldPosition.y;

            // 转换为相对于当前视图的坐标
            const localPos = this.node.convertToNodeSpaceAR(cc.v2(blockWorldX, blockWorldY));
            blockNode.x = localPos.x;
            blockNode.y = localPos.y;

            // 设置块的颜色（这里简单设置节点颜色，实际可能需要根据 prefab 结构调整）
            this.setBlockColor(blockNode, blockData.color);

            // 设置块的精灵帧
            this.setBlockSpriteFrame(blockNode, blockData.color);

            // 添加到当前视图节点
            this.node.addChild(blockNode);
            createdNodes.push(blockNode);
            // 播放放大缩小动效
            this.playBlockNodeAnimation(blockNode);
            console.log(`ReviveBlockEffectECSView: 创建块显示节点 位置=${blockData.position.r}_${blockData.position.c}, 颜色=${blockData.color}`);
        }

        return createdNodes;
    }

    /**
     * 设置块的精灵帧
     */
    private setBlockSpriteFrame(blockNode: cc.Node, color: number): void {
        const blockSprite = blockNode.getChildByName("block")?.getComponent(cc.Sprite);
        if (!blockSprite) {
            console.warn('未找到block精灵组件');
            return;
        }

        let spriteFrame: cc.SpriteFrame = null;

        if (color <= 100) {
            // 使用blocks-dark图集
            spriteFrame = this.resourceCache.blocksDarkAtlas.getSpriteFrame(`game_cube_${color}`);
        } else {
            // 使用Gems图集
            spriteFrame = this.resourceCache.gemsAtlas.getSpriteFrame(`Gems${color}`);
        }

        if (spriteFrame) {
            blockSprite.spriteFrame = spriteFrame;
        } else {
            console.warn(`未找到颜色 ${color} 对应的精灵帧`);
        }
    }

    /**
     * 设置块节点的颜色
     */
    private setBlockColor(blockNode: cc.Node, color: number): void {
        // 这里根据颜色值设置节点颜色
        // 实际实现需要根据具体的 prefab 结构和颜色系统调整
        const colorMap = {
            1: cc.Color.RED,
            2: cc.Color.GREEN,
            3: cc.Color.BLUE,
            4: cc.Color.YELLOW,
            5: cc.Color.MAGENTA,
            6: cc.Color.CYAN,
            7: cc.Color.ORANGE,
        };

        const ccColor = colorMap[color] || cc.Color.WHITE;
        blockNode.color = ccColor;
    }

    /**
     * 播放块节点的动画
     */
    private playBlockNodeAnimation(blockNode: cc.Node): void {
        cc.tween(blockNode).
            repeatForever(cc.tween()
                .to(0.3, { scale: 1.12, opacity: 255 })//, 
                .to(0.3, { scale: 1, opacity: 154 })).start();//, opacity: 144

        console.log('ReviveBlockEffectECSView: 播放块节点动效');
    }

    /**
     * 删除块节点
     */
    private deleteBlockNodes(blockNodes: cc.Node[]): void {
        if (!blockNodes || blockNodes.length === 0) return;

        blockNodes.forEach(node => {
            if (cc.isValid(node)) {
                node.destroy();
            }
        });
        console.log('ReviveBlockEffectECSView: 删除了', blockNodes.length, '个块节点');
    }
}
