import { ECSViewBase } from '../ECSViewBase';

/**
 * 复活块特效视图
 * 播放行列特效
 */
export class ReviveBlockEffectECSView extends ECSViewBase {

    protected async initView(): Promise<void> {
        const { rowEffect, colEffect } = this.data;

        if (rowEffect) {
            await this.createEffect(rowEffect);
        }

        if (colEffect) {
            await this.createEffect(colEffect);
        }

        // 特效播放完成后销毁视图
        this.scheduleOnce(() => {
            this.renderWorld.destroyECSView(this);
        }, 2.0); // 2秒后销毁
    }
    
    protected addEvents(): void {
        // 不需要监听事件
    }

    protected removeEvents(): void {
        // 不需要移除事件
    }

    protected disposeView(): void {
        console.log('ReviveBlockEffectECSView: 特效视图已销毁');
    }

    /**
     * 创建特效
     */
    private async createEffect(effectData: any): Promise<void> {
        // 加载特效 prefab
        const prefab = await this.renderWorld.loadRes('advres', 'prefabs/level/revive/clear_tips_node', cc.Prefab) as cc.Prefab;
        if (!prefab || !cc.isValid(this.node)) return;

        // 创建特效节点
        const effectNode = cc.instantiate(prefab);

        // 获取棋盘在世界坐标系中的位置
        const boardWorldPos = this.renderWorld.getRenderEntityWorldPosition(effectData.boardEntity);

        // 计算特效在世界坐标系中的位置
        const effectWorldX = boardWorldPos.x + effectData.x;
        const effectWorldY = boardWorldPos.y + effectData.y;

        // 转换为相对于当前视图的坐标
        const localPos = this.node.convertToNodeSpaceAR(cc.v2(effectWorldX, effectWorldY));
        effectNode.x = localPos.x;
        effectNode.y = localPos.y;
        effectNode.angle = effectData.angle;
        effectNode.scaleX = effectData.scaleX;

        // 添加到当前视图节点
        this.node.addChild(effectNode);

        console.log(`ReviveBlockEffectECSView: 创建特效 angle=${effectData.angle}`);
    }
}
