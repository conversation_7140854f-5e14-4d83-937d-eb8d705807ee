import { ECSViewBase } from '../ECSViewBase';
import NodeComponent from '../../components/NodeComponent';

/**
 * 复活块特效视图
 * 播放行列特效
 */
export class ReviveBlockEffectECSView extends ECSViewBase {

    protected async initView(): Promise<void> {
        const { rowEffect, colEffect, blockEntities } = this.data;

        // 1. 播放块的动画
        if (blockEntities && blockEntities.length > 0) {
            this.playBlockAnimations(blockEntities);
        }

        // 2. 创建行列特效
        if (rowEffect) {
            await this.createEffect(rowEffect);
        }

        if (colEffect) {
            await this.createEffect(colEffect);
        }

        // 3. 2秒后删除块和销毁视图
        this.scheduleOnce(() => {
            this.deleteBlocks(blockEntities);
            this.renderWorld.destroyECSView(this);
        }, 2.0); // 2秒后销毁
    }
    
    protected addEvents(): void {
        // 不需要监听事件
    }

    protected removeEvents(): void {
        // 不需要移除事件
    }

    protected disposeView(): void {
        console.log('ReviveBlockEffectECSView: 特效视图已销毁');
    }

    /**
     * 创建特效
     */
    private async createEffect(effectData: any): Promise<void> {
        // 加载特效 prefab
        const prefab = await this.renderWorld.loadRes('advres', 'prefabs/level/revive/clear_tips_node', cc.Prefab) as cc.Prefab;
        if (!prefab || !cc.isValid(this.node)) return;

        // 创建特效节点
        const effectNode = cc.instantiate(prefab);

        // 获取棋盘在世界坐标系中的位置
        const boardWorldPos = this.renderWorld.getRenderEntityWorldPosition(effectData.boardEntity);

        // 计算特效在世界坐标系中的位置
        const effectWorldX = boardWorldPos.x + effectData.x;
        const effectWorldY = boardWorldPos.y + effectData.y;

        // 转换为相对于当前视图的坐标
        const localPos = this.node.convertToNodeSpaceAR(cc.v2(effectWorldX, effectWorldY));
        effectNode.x = localPos.x;
        effectNode.y = localPos.y;
        effectNode.angle = effectData.angle;
        effectNode.scaleX = effectData.scaleX;

        // 添加到当前视图节点
        this.node.addChild(effectNode);

        console.log(`ReviveBlockEffectECSView: 创建特效 angle=${effectData.angle}`);
    }

    /**
     * 播放块的放大缩小动画
     */
    private playBlockAnimations(blockEntities: number[]): void {
        blockEntities.forEach(entityId => {
            this.playBlockAnimation(entityId);
        });
    }

    /**
     * 播放单个块的动画
     */
    private playBlockAnimation(blockEntity: number): void {
        // 通过 renderWorld 获取实体对应的视图节点
        const blockNode = (this.renderWorld as any)._ecsViewMap?.get(blockEntity)?.node;
        if (!blockNode || !cc.isValid(blockNode)) {
            console.warn('ReviveBlockEffectECSView: 找不到块的渲染节点', blockEntity);
            return;
        }

        // 播放放大缩小动效
        blockNode.scale = 0;
        cc.tween(blockNode)
            .to(0.2, { scale: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scale: 1.0 }, { easing: 'backOut' })
            .start();

        console.log('ReviveBlockEffectECSView: 播放块动效', blockEntity);
    }

    /**
     * 删除块实体
     */
    private deleteBlocks(blockEntities: number[]): void {
        if (!blockEntities || blockEntities.length === 0) return;

        blockEntities.forEach(entityId => {
            // 检查实体是否还存在（通过检查是否有NodeComponent）
            if (this.logicWorld.getComponent(entityId, NodeComponent)) {
                // 通过 renderWorld 删除实体
                this.renderWorld.destroyEntity(entityId);
            }
        });

        console.log('ReviveBlockEffectECSView: 删除了', blockEntities.length, '个块实体');
    }
}
