import { ECSViewBase } from '../ECSViewBase';
import BoardComponent from '../../components/board/BoardComponent';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import NodeComponent from '../../components/NodeComponent';

/**
 * 复活块特效视图
 * 播放行列特效
 */
export class ReviveBlockEffectECSView extends ECSViewBase {
    
    protected async initView(): Promise<void> {
        const { row, col } = this.data;
        
        if (row !== undefined) {
            await this.createRowEffect(row);
        }
        
        if (col !== undefined) {
            await this.createColEffect(col);
        }
        
        // 特效播放完成后销毁视图
        this.scheduleOnce(() => {
            this.renderWorld.destroyECSView(this);
        }, 2.0); // 2秒后销毁
    }
    
    protected addEvents(): void {
        // 不需要监听事件
    }
    
    protected removeEvents(): void {
        // 不需要移除事件
    }
    
    protected disposeView(): void {
        console.log('ReviveBlockEffectECSView: 特效视图已销毁');
    }
    
    /**
     * 创建行特效
     */
    private async createRowEffect(row: number): Promise<void> {
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;
        
        const boardComp = this.logicWorld.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;
        
        // 加载特效 prefab
        const prefab = await this.renderWorld.loadRes('advres', 'prefabs/level/revive/clear_tips_node', cc.Prefab) as cc.Prefab;
        if (!prefab || !cc.isValid(this.node)) return;
        
        // 创建特效节点
        const effectNode = cc.instantiate(prefab);
        
        // 获取行的起始和结束位置
        const startSlotEntity = this.logicWorld.getTargets(boardEntity, RelationName.PARENT_CHILD, `${row}_0`)[0];
        const endSlotEntity = this.logicWorld.getTargets(boardEntity, RelationName.PARENT_CHILD, `${row}_${boardComp.cCount - 1}`)[0];
        
        if (!startSlotEntity || !endSlotEntity) return;
        
        const startNode = this.logicWorld.getComponent(startSlotEntity, NodeComponent);
        const endNode = this.logicWorld.getComponent(endSlotEntity, NodeComponent);
        
        if (!startNode || !endNode) return;
        
        // 获取棋盘在世界坐标系中的位置
        const boardWorldPos = this.renderWorld.getRenderEntityWorldPosition(boardEntity);

        // 计算特效在世界坐标系中的位置（行中心）
        const effectWorldX = boardWorldPos.x + (startNode.x + endNode.x) / 2;
        const effectWorldY = boardWorldPos.y + startNode.y;

        // 转换为相对于当前视图的坐标
        const localPos = this.node.convertToNodeSpaceAR(cc.v2(effectWorldX, effectWorldY));
        effectNode.x = localPos.x;
        effectNode.y = localPos.y;
        effectNode.angle = 0; // 行特效不需要旋转

        // 根据棋盘宽度调整缩放
        const scaleFactor = boardComp.cCount / 8; // 8格为基准
        effectNode.scaleX = scaleFactor;

        // 添加到当前视图节点
        this.node.addChild(effectNode);
        
        console.log(`ReviveBlockEffectECSView: 创建行特效 row=${row}`);
    }
    
    /**
     * 创建列特效
     */
    private async createColEffect(col: number): Promise<void> {
        const boardEntity = this.getBoardEntity();
        if (!boardEntity) return;
        
        const boardComp = this.logicWorld.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;
        
        // 加载特效 prefab
        const prefab = await this.renderWorld.loadRes('advres', 'prefabs/level/revive/clear_tips_node', cc.Prefab) as cc.Prefab;
        if (!prefab || !cc.isValid(this.node)) return;
        
        // 创建特效节点
        const effectNode = cc.instantiate(prefab);
        
        // 获取列的起始和结束位置
        const startSlotEntity = this.logicWorld.getTargets(boardEntity, RelationName.PARENT_CHILD, `0_${col}`)[0];
        const endSlotEntity = this.logicWorld.getTargets(boardEntity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${col}`)[0];
        
        if (!startSlotEntity || !endSlotEntity) return;
        
        const startNode = this.logicWorld.getComponent(startSlotEntity, NodeComponent);
        const endNode = this.logicWorld.getComponent(endSlotEntity, NodeComponent);
        
        if (!startNode || !endNode) return;
        
        // 获取棋盘在世界坐标系中的位置
        const boardWorldPos = this.renderWorld.getRenderEntityWorldPosition(boardEntity);

        // 计算特效在世界坐标系中的位置（列中心）
        const effectWorldX = boardWorldPos.x + startNode.x;
        const effectWorldY = boardWorldPos.y + (startNode.y + endNode.y) / 2;

        // 转换为相对于当前视图的坐标
        const localPos = this.node.convertToNodeSpaceAR(cc.v2(effectWorldX, effectWorldY));
        effectNode.x = localPos.x;
        effectNode.y = localPos.y;
        effectNode.angle = 90; // 列特效需要旋转90度

        // 根据棋盘高度调整缩放
        const scaleFactor = boardComp.rCount / 8; // 8格为基准
        effectNode.scaleX = scaleFactor;

        // 添加到当前视图节点
        this.node.addChild(effectNode);
        
        console.log(`ReviveBlockEffectECSView: 创建列特效 col=${col}`);
    }
    
    /**
     * 获取棋盘实体
     */
    private getBoardEntity(): number | null {
        const boardEntities = this.logicWorld.query([BoardComponent]);
        return boardEntities.length > 0 ? boardEntities[0] : null;
    }
    

}
