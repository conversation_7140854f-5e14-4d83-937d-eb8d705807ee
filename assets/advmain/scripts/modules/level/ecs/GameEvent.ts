import { EcsStorageData } from './cores/center/SnapshotCenter';
import { TargetType } from './define/BoardDefine';
import { IPoint } from './define/EcsDefine';
import {
    IEliminationInfo,
    IPutBlockBackInfo,
    IPutBlockInfo,
    IDragBlockStartInfo,
    ICreateDelayRuleData,
    ICreateInitRuleFlowRuleData,
    ICreateInitWorldRuleData,
    IProduceBlockEnd,
    ISwithBoardData,
    IComboChangedInfo,
    IBoardData,
    TargetParam,
    IAddBuffInfo,
    IPlayAudioInfo,
    IStopAudioInfo,
    IFullScreenShakeInfo,
} from './GameEventData';
export namespace ECSEvent {
    /**基础事件 */
    export enum BaseEvent {
        /**渲染创建事件 */
        RENDER_CREATED = 'BaseEvent.RENDER_CREATED',
        /**快照恢复数据完成事件 */
        SNAPSHOT_RESTORE_DATA_DONE = 'BaseEvent.SNAPSHOT_RESTORE_DATA_DONE',
        /**快照恢复渲染完成事件 */
        SNAPSHOT_RESTORE_RENDER_DONE = 'BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE',
        /**渲染创建失败事件 */
        RENDER_CREATED_FAILED = 'BaseEvent.RENDER_CREATED_FAILED',
        /**初始化世界 */
        INIT_WORLD = 'BaseEvent.INIT_WORLD',
        /**初始化世界完成 */
        INIT_WORLD_COMPLETE = 'BaseEvent.INIT_WORLD_COMPLETE',
        /**初始化规则流 */
        INIT_RULE_FLOW = 'BaseEvent.INIT_RULE_FLOW',
        /**渲染添加事件 */
        RENDER_ADD = 'BaseEvent.RENDER_ADD',
        /**渲染移除事件 */
        RENDER_REMOVE = 'BaseEvent.RENDER_REMOVE',
        /** 创建延时规则 */
        CREATE_DELAY_RULE = 'BaseEvent.CREATE_DELAY_RULE',
        /** 游戏重启 */
        GAME_RESTART = 'BaseEvent.GAME_RESTART',
        /** 开始出块 */
        PRODUCE_BLOCK_START = 'BaseEvent.PRODUCE_BLOCK_START',
        /** 结束出块 */
        PRODUCE_BLOCK_END = 'BaseEvent.PRODUCE_BLOCK_END',
        /** 播放音效 */
        PLAY_AUDIO = 'BaseEvent.PLAY_AUDIO',
        /** 停止音效 */
        STOP_AUDIO = 'BaseEvent.STOP_AUDIO',
        /** 全屏震动动画 */
        FULL_SCREEN_SHAKE = 'BaseEvent.FULL_SCREEN_SHAKE',
        /**保存快照 */
        SAVE_SNAPSHOT = 'BaseEvent.SAVE_SNAPSHOT',
    }
    /**全局游戏事件 */
    export enum GameEvent {
        /** 放置方块 */
        PUT_BLOCK = 'GameEvent.PUT_BLOCK',
        /** 放置方块结束 */
        PUT_BLOCK_BACK = 'GameEvent.PUT_BLOCK_BACK',
        /** 预览方块 */
        PREVIEW_BLOCK = 'GameEvent.PREVIEW_BLOCK',
        /** 清除预览方块 */
        CLEAR_PREVIEW_BLOCK = 'GameEvent.CLEAR_PREVIEW_BLOCK',
        /**目标变化 */
        TARGET_CHANGE = 'GameEvent.TARGET_CHANGE',
        //---------------------------------------------ECSView事件----------------------------------------------
        /**刷新目标 */
        FRESH_TARGET = 'GameEvent.FRESH_TARGET',
        //------------------------------------------------end--------------------------------------------------
        /**消除事件*/
        ELIMINATION = 'GameEvent.ELIMINATION',
        /**生成待放置块 */
        PRODUCE_BLOCK = 'GameEvent.PRODUCE_BLOCK',
        /** 复活出块 */
        PRODUCE_BLOCK_REVIVE = 'GameEvent.PRODUCE_BLOCK_REVIVE',
        /** 结束出块 */
        PRODUCE_BLOCK_END = 'GameEvent.PRODUCE_BLOCK_END',
        /**块被移除 */
        CELL_REMOVED = 'GameEvent.CELL_REMOVED',
        /**发起消除块 */
        CELL_ELIMINATION = 'GameEvent.CELL_ELIMINATION',

        /** 游戏失败 */
        GAME_FAILED = 'GameEvent.GAME_FAILED',
        /** 游戏成功 */
        GAME_WIN = 'GameEvent.GAME_WIN',
        /** 连击变化 */
        COMBO_CHANGED = 'GameEvent.COMBO_CHANGED',
        /**添加buff */
        ADD_BUFF = 'EntityEvent.ADD_BUFF',
        /**移除buff */
        REMOVE_BUFF = 'EntityEvent.REMOVE_BUFF',
        /**单次得分特效展示 */
        SINGLE_SCORE_EFFECT = 'RenderEvent.SINGLE_SCORE_EFFECT',

        /** 游戏重试 */
        GAME_RETRY = 'GameEvent.GAME_RETRY',
        /** 游戏下一关 */
        GAME_NEXT = 'GameEvent.GAME_NEXT',

        /** 复活成功 */
        REVIVE_SUCCESS = 'GameEvent.REVIVE_SUCCESS',
        /** 复活失败 */
        REVIVE_FAILED = 'GameEvent.REVIVE_FAILED',
        /** 复活预览开始 */
        REVIVE_PREVIEW_START = 'GameEvent.REVIVE_PREVIEW_START',
        /** 复活预览完成 */
        REVIVE_PREVIEW_COMPLETE = 'GameEvent.REVIVE_PREVIEW_COMPLETE',
        /** 复活确认 */
        REVIVE_CONFIRMED = 'GameEvent.REVIVE_CONFIRMED',
        /** 复活取消 */
        REVIVE_CANCELLED = 'GameEvent.REVIVE_CANCELLED',

        //------------------------------------------------end--------------------------------------------------
    }
    /**实体事件(该事件只会在实体内部传输) */
    export enum EntityEvent {
        /**被消除，参数为消除组件的消除码 */
        ELIMINATIONED = 'EntityEvent.ELIMINATIONED',
        /**平滑放块 */
        PUT_BLOCK_TWEEN = 'GameEvent.PUT_BLOCK_TWEEN',
        /**高亮变化 */
        HIGHLIGHT_CHANGED = 'EntityEvent.HIGHLIGHT_CHANGED',
    }
    /**渲染事件 */
    export enum RenderEvent {
        /**刷新目标 */
        FRESH_TARGET_COLLECT = 'RenderEvent.FRESH_TARGET_COLLECT',
        /**多行消除特效展示 */
        MULTI_ELIMINATION_SPECIAL_EFFECT = 'RenderEvent.MULTI_ELIMINATION_SPECIAL_EFFECT',
        /**复活弹窗关闭 */
        REVIVE_CLOSE = 'RenderEvent.REVIVE_CLOSE',
    }
}
/**duralBoard玩法模块相关游戏事件 */
export namespace ECSEvent {
    export enum GameEvent {
        DRAG_BLOCK_START = 'GameEvent.DRAG_BLOCK_START',
        DRAG_BLOCK_MOVE = 'GameEvent.DRAG_BLOCK_MOVE',
        DRAG_BLOCK_END = 'GameEvent.DRAG_BLOCK_END',
        DRAG_BLOCK_CANCEL = 'GameEvent.DRAG_BLOCK_CANCEL',
        SWITCH_BOARD = 'GameEvent.SWITCH_BOARD',
    }
}

/**mulityBoard玩法模块相关游戏事件 */
export namespace ECSEvent {
    export enum GameEvent {
        /** 多层棋盘块部署完成 */
        MULTIBOARD_BLOCKS_DEPLOYED = 'GameEvent.MULTIBOARD_BLOCKS_DEPLOYED',
        /** 多层棋盘块创建完成 */
        MULTIBOARD_BLOCKS_CREATED = 'GameEvent.MULTIBOARD_BLOCKS_CREATED',
    }
}

/**基础事件数据映射 */
export interface GameEventMap {
    [ECSEvent.BaseEvent.RENDER_CREATED]: number;
    [ECSEvent.BaseEvent.SNAPSHOT_RESTORE_DATA_DONE]: void;
    [ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE]: void;
    [ECSEvent.BaseEvent.RENDER_CREATED_FAILED]: number;
    [ECSEvent.BaseEvent.INIT_WORLD]: ICreateInitWorldRuleData;
    [ECSEvent.BaseEvent.CREATE_DELAY_RULE]: ICreateDelayRuleData;
    [ECSEvent.BaseEvent.GAME_RESTART]: void;
    [ECSEvent.BaseEvent.RENDER_ADD]: number;
    [ECSEvent.BaseEvent.RENDER_REMOVE]: number;
    [ECSEvent.BaseEvent.INIT_RULE_FLOW]: ICreateInitRuleFlowRuleData;
    [ECSEvent.BaseEvent.INIT_WORLD_COMPLETE]: void;
    [ECSEvent.BaseEvent.PRODUCE_BLOCK_START]: IBoardData;
    [ECSEvent.BaseEvent.PRODUCE_BLOCK_END]: IProduceBlockEnd;
    [ECSEvent.BaseEvent.PLAY_AUDIO]: IPlayAudioInfo;
    [ECSEvent.BaseEvent.STOP_AUDIO]: IStopAudioInfo;
    [ECSEvent.BaseEvent.FULL_SCREEN_SHAKE]: IFullScreenShakeInfo;
    [ECSEvent.BaseEvent.SAVE_SNAPSHOT]: EcsStorageData;
}
/**游戏事件数据映射 */
export interface GameEventMap {
    [ECSEvent.GameEvent.PUT_BLOCK]: IPutBlockInfo;
    [ECSEvent.GameEvent.PUT_BLOCK_BACK]: IPutBlockBackInfo;
    [ECSEvent.GameEvent.PREVIEW_BLOCK]: IPutBlockInfo;
    [ECSEvent.GameEvent.CLEAR_PREVIEW_BLOCK]: { boardId: string };
    [ECSEvent.GameEvent.ELIMINATION]: IEliminationInfo;
    [ECSEvent.GameEvent.PRODUCE_BLOCK]: void;
    [ECSEvent.GameEvent.PRODUCE_BLOCK_REVIVE]: void;
    [ECSEvent.GameEvent.PRODUCE_BLOCK_END]: IProduceBlockEnd;
    [ECSEvent.GameEvent.GAME_FAILED]: void;
    [ECSEvent.GameEvent.GAME_WIN]: void;
    [ECSEvent.GameEvent.DRAG_BLOCK_START]: IDragBlockStartInfo;
    [ECSEvent.GameEvent.DRAG_BLOCK_MOVE]: IPoint;
    [ECSEvent.GameEvent.DRAG_BLOCK_END]: void;
    [ECSEvent.GameEvent.DRAG_BLOCK_CANCEL]: void;
    [ECSEvent.GameEvent.SWITCH_BOARD]: ISwithBoardData;
    [ECSEvent.GameEvent.TARGET_CHANGE]: { key: TargetType; change: number };
    [ECSEvent.GameEvent.FRESH_TARGET]: TargetParam;
    [ECSEvent.GameEvent.COMBO_CHANGED]: IComboChangedInfo;
    [ECSEvent.GameEvent.CELL_REMOVED]: number;
    [ECSEvent.GameEvent.CELL_ELIMINATION]: { entity: number; clearCode: number };
    [ECSEvent.GameEvent.ADD_BUFF]: IAddBuffInfo;
    [ECSEvent.GameEvent.REMOVE_BUFF]: IAddBuffInfo;
    /** 多层棋盘块部署完成事件，不需要额外数据 */
    [ECSEvent.GameEvent.MULTIBOARD_BLOCKS_DEPLOYED]: void;
    /** 多层棋盘块创建完成事件，不需要额外数据 */
    [ECSEvent.GameEvent.MULTIBOARD_BLOCKS_CREATED]: void;
    [ECSEvent.EntityEvent.PUT_BLOCK_TWEEN]: IPoint;
    [ECSEvent.GameEvent.SINGLE_SCORE_EFFECT]: { score: number; eliminateCount: number; boardEntity: number; position?: IPoint };
    [ECSEvent.GameEvent.REVIVE_SUCCESS]: void;
    [ECSEvent.GameEvent.REVIVE_FAILED]: void;
    [ECSEvent.GameEvent.REVIVE_PREVIEW_START]: void;
    [ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE]: IProduceBlockEnd;
    [ECSEvent.GameEvent.REVIVE_CONFIRMED]: void;
    [ECSEvent.GameEvent.REVIVE_CANCELLED]: void;
    [ECSEvent.GameEvent.GAME_NEXT]: void;
    [ECSEvent.GameEvent.GAME_RETRY]: void;
}
/**实体事件数据映射 */
export interface GameEventMap {
    [ECSEvent.EntityEvent.PUT_BLOCK_TWEEN]: IPoint;
    [ECSEvent.EntityEvent.ELIMINATIONED]: number;
    [ECSEvent.EntityEvent.HIGHLIGHT_CHANGED]: boolean;
}
/**渲染事件数据映射 */
export interface GameEventMap {
    [ECSEvent.RenderEvent.FRESH_TARGET_COLLECT]: TargetParam;
    [ECSEvent.RenderEvent.MULTI_ELIMINATION_SPECIAL_EFFECT]: { eliminateCount: number };
    [ECSEvent.RenderEvent.REVIVE_CLOSE]: void;
}
