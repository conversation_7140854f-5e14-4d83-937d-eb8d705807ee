import BoardComponent from '../components/board/BoardComponent';
import { BoardScene } from '../components/board/BoardScene';
import { CellComponent } from '../components/board/CellComponent';
import ShapeComponent from '../components/board/ShapeComponent';
import SlotComponent from '../components/board/SlotComponent';
import { DestroyTag } from '../components/tag/DestroyTag';
import NodeComponent from '../components/NodeComponent';
import { IBoardConfig } from '../config/conf/BoardConfig';
import { UtilBase } from '../cores/UtilBase';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { Component } from '../cores/Component';
import { IPoint, IRCShape } from '../define/EcsDefine';

export default class BoardUtil extends UtilBase {
    /**按排序依据添加关系 */
    addRelationBy(source: number, target: number, name: RelationName, sort: (aEntity: number, bEntity: number) => number, key?: string) {
        const newList = [...this.world.getTargets(source, name), target];
        newList.sort(sort);
        this.world.addRelationAt(source, target, name, newList.indexOf(target), key);
    }

    /**获取唯一组件 */
    getUniqueComponent<T extends Component>(componentType: clzz<T>) {
        return this.world.getComponent(this.world.query([componentType])[0], componentType);
    }

    /**获取实体相对根实体的坐标 */
    getEntityRootPoint(entity: number, path?: string): IPoint {
        //临时写法
        return window['gameIns']._renderWorld.getRenderEntityRootPosition(entity, path);
    }

    /**获取盘面占用数据 */
    getBoardOccupy(boardEntity: number) {
        const board = this.world.getComponent(boardEntity, BoardComponent);
        const occupyData: number[][] = [];
        for (let i = 0; i < board.rCount; i++) {
            const row: number[] = [];
            for (let j = 0; j < board.cCount; j++) {
                const slot = this.world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${i}_${j}`)[0];
                if (slot) row[j] = this.getSlotOccupy(slot) ? 1 : -1;
                else row[j] = 0;
            }
            occupyData[i] = row;
        }
        return occupyData;
    }

    /**遍历盘面格子，返回false中断 */
    boardSlotEach(board: BoardComponent, cb: (slot: SlotComponent) => void | boolean) {
        for (let r = 0; r < board.rCount; r++) {
            for (let c = 0; c < board.cCount; c++) {
                const slotEntity = this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
                if (!slotEntity) continue;
                if (cb(this.world.getComponent(slotEntity, SlotComponent)) === false) return;
            }
        }
    }

    /**取格子最上层的有效连通块 */
    getSlotThrough(slot: SlotComponent | number) {
        if (typeof slot === 'number') slot = this.world.getComponent(slot, SlotComponent);
        return this.slotLayerEach(slot, (cell) => cell.through);
    }

    /**取格子最上层的有效占用块 */
    getSlotOccupy(slot: SlotComponent | number) {
        if (typeof slot === 'number') {
            slot = this.world.getComponent(slot, SlotComponent);
        }
        return this.slotLayerEach(slot, (cell) => cell.occupy);
    }

    /**格子从上层向下遍历有效实体，满足条件中断并返回块实体id */
    private slotLayerEach(slot: SlotComponent, condition: (cell: CellComponent) => boolean) {
        if (!slot) return;
        const layerEntitys = this.world.getTargets(slot.entity, RelationName.SLOT_CELL);
        let nowI = layerEntitys.length - 1;
        let cellEntity: number;

        while ((cellEntity = layerEntitys[nowI--])) {
            if (this.world.getComponent(cellEntity, DestroyTag)) continue;
            const cell = this.world.getComponent(cellEntity, CellComponent);
            if (!cell) continue;
            if (condition(cell)) return cellEntity;
        }
    }

    /**
     * 组合多个boardOccupy数组
     * @param boardOccupies 盘面数据
     * @returns 组合后的盘面数据
     */
    combineBoardOccupy(boardOccupies: number[][][]): number[][] {
        if (boardOccupies.length === 0) return [];
        // 动态获取最大行列数
        let maxRows = 0;
        let maxCols = 0;
        for (const occupy of boardOccupies) {
            if (occupy.length > maxRows) maxRows = occupy.length;
            for (const row of occupy) {
                if (row.length > maxCols) maxCols = row.length;
            }
        }
        const result: number[][] = [];
        for (let r = 0; r < maxRows; r++) {
            result[r] = [];
            for (let c = 0; c < maxCols; c++) {
                let hasNonNegative = false;
                let nonNegativeValue = -1;
                for (let i = 0; i < boardOccupies.length; i++) {
                    const occupy = boardOccupies[i];
                    if (occupy[r] && occupy[r][c] !== undefined) {
                        const value = occupy[r][c];
                        if (value !== -1) {
                            hasNonNegative = true;
                            nonNegativeValue = value;
                            break;
                        }
                    }
                }
                result[r][c] = hasNonNegative ? nonNegativeValue : -1;
            }
        }
        return result;
    }

    /** 检查形状是否可以放置在棋盘上 */
    canShapePlace(world, boardE: number, shapeComp: ShapeComponent): boolean {
        const board = world.getComponent(boardE, BoardComponent);
        for (let y = 0; y <= board.rCount - shapeComp.shape.height; y++) {
            for (let x = 0; x <= board.cCount - shapeComp.shape.width; x++) {
                if (this.shapeFit(board, x, y, shapeComp)) {
                    return true;
                }
            }
        }
        return false;
    }

    /** 检查形状是否可以放置在棋盘上 */
    private shapeFit(board: BoardComponent, x: number, y: number, shapeComp: ShapeComponent): boolean {
        for (const rc of shapeComp.shape.shape) {
            const r = y + rc.r;
            const c = x + rc.c;
            const slotE = this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            if (this.world.utilCenter.boardUtil.getSlotOccupy(slotE)) {
                return false;
            }
        }
        return true;
    }

    /** 检查形状是否可以放置在组合棋盘上 */
    canPutShape(shapeComp: ShapeComponent, combinedBoardOccupy: number[][]): boolean {
        const rows = combinedBoardOccupy.length;
        const cols = combinedBoardOccupy[0].length;
        const { width, height, shape } = shapeComp.shape;
        for (let y = 0; y <= rows - height; y++) {
            for (let x = 0; x <= cols - width; x++) {
                let ok = true;
                for (const rc of shape) {
                    const r = y + rc.r;
                    const c = x + rc.c;
                    if (combinedBoardOccupy[r][c] !== -1) {
                        ok = false;
                        break;
                    }
                }
                if (ok) return true;
            }
        }
        return false;
    }
    /**
     * 检查形状在给定位置的消除信息
     * @param shape 形状
     * @param boardOccupy 棋盘占用数据
     * @param pos 形状位置
     * @returns 可以消除的行和列
     */
    canClearRowCol(shape: IRCShape, boardOccupy: number[][], pos: { r?: number, c?: number, row?: number, col?: number }): { rows: number[], cols: number[] } {
        const boardHeight = boardOccupy.length;
        const boardWidth = boardOccupy[0]?.length || 0;

        console.log("canClearRowCol 输入参数:");
        console.log("  shape:", shape);
        console.log("  pos:", pos);
        console.log("  boardSize:", boardHeight, "x", boardWidth);
        console.log("  原始棋盘详细内容:");
        for (let r = 0; r < boardHeight; r++) {
            console.log(`    行${r}:`, boardOccupy[r]);
        }

        // 创建临时棋盘，模拟放入形状后的状态
        const tempBoard: number[][] = [];
        for (let r = 0; r < boardHeight; r++) {
            tempBoard[r] = [...boardOccupy[r]]; // 复制原棋盘
        }

        // 统一位置格式
        const baseR = pos.r !== undefined ? pos.r : pos.row;
        const baseC = pos.c !== undefined ? pos.c : pos.col;

        console.log("  统一后的基准位置:", baseR, baseC);

        // 在临时棋盘上放入形状
        for (const cell of shape.shape) {
            const targetR = baseR + cell.r;
            const targetC = baseC + cell.c;

            console.log(`  放置形状块: (${cell.r}, ${cell.c}) -> (${targetR}, ${targetC})`);

            // 检查位置是否在棋盘范围内
            if (targetR >= 0 && targetR < boardHeight && targetC >= 0 && targetC < boardWidth) {
                console.log(`    原值: ${tempBoard[targetR][targetC]} -> 新值: 1`);
                tempBoard[targetR][targetC] = 1; // 标记为占用
            } else {
                console.log(`    位置超出边界`);
            }
        }

        console.log("放置后的棋盘详细内容:");
        for (let r = 0; r < boardHeight; r++) {
            console.log(`    行${r}:`, tempBoard[r]);
        }

        // 检查能消除的行
        const clearableRows: number[] = [];
        console.log("检查行消除:");
        for (let r = 0; r < boardHeight; r++) {
            let isFullRow = true;
            let emptyCount = 0;
            let occupiedCount = 0;
            for (let c = 0; c < boardWidth; c++) {
                const cellValue = tempBoard[r][c];
                if (cellValue === -1 || cellValue === 0) {
                    isFullRow = false;
                    emptyCount++;
                } else if (cellValue === 1) {
                    occupiedCount++;
                }
            }
            console.log(`  行${r}: 占用=${occupiedCount}, 空=${emptyCount}, 满行=${isFullRow}`);
            if (isFullRow) {
                clearableRows.push(r);
            }
        }

        // 检查能消除的列
        const clearableCols: number[] = [];
        console.log("检查列消除:");
        for (let c = 0; c < boardWidth; c++) {
            let isFullCol = true;
            let emptyCount = 0;
            let occupiedCount = 0;
            for (let r = 0; r < boardHeight; r++) {
                const cellValue = tempBoard[r][c];
                if (cellValue === -1 || cellValue === 0) {
                    isFullCol = false;
                    emptyCount++;
                } else if (cellValue === 1) {
                    occupiedCount++;
                }
            }
            console.log(`  列${c}: 占用=${occupiedCount}, 空=${emptyCount}, 满列=${isFullCol}`);
            if (isFullCol) {
                clearableCols.push(c);
            }
        }

        console.log("消除检查结果:");
        console.log("  可消除行:", clearableRows);
        console.log("  可消除列:", clearableCols);

        return {
            rows: clearableRows,
            cols: clearableCols
        };
    }

    /** 将屏幕坐标转换为棋盘坐标 */
    convertToGridCoord(screenPos: { x: number; y: number }, boards: IBoardConfig[], shpeId: number) {
        const shapeComp = this.world.getComponent(shpeId, ShapeComponent)!;
        for (const b of boards) {
            const w = b.cols * b.cellSize;
            const h = b.rows * b.cellSize;
            // 形状像素尺寸（未考虑 scale，因为我们用真实 cellSize 计算）
            const shapeW = shapeComp.shape.width * b.cellSize;
            const shapeH = shapeComp.shape.height * b.cellSize;
            // 计算左上格中心坐标
            const adjX = screenPos.x - shapeW / 2 + b.cellSize / 2;
            const adjY = screenPos.y + shapeH / 2 - b.cellSize / 2;
            // 将中心点转换为左下角
            const boardLeftBottomX = b.startPos.x - w / 2;
            const boardLeftBottomY = b.startPos.y - h / 2;
            if (adjX >= boardLeftBottomX && adjX <= boardLeftBottomX + w && adjY >= boardLeftBottomY && adjY <= boardLeftBottomY + h) {
                const localX = adjX - boardLeftBottomX;
                const localY = adjY - boardLeftBottomY;
                let gx = Math.floor(localX / b.cellSize);
                let gy = b.rows - 1 - Math.floor(localY / b.cellSize);
                const clamp = (val: number, min: number, max: number) => Math.max(min, Math.min(max, val));
                gx = clamp(gx, 0, b.cols - 1);
                gy = clamp(gy, 0, b.rows - 1);
                return { boardId: b.boardId, gridX: gx, gridY: gy };
            }
        }
        return null;
    }
    /**获取场景实体 */
    public getSceneEntity() {
        return this.world.query([BoardScene])[0];
    }
    /** 获取棋盘配置列表 */
    public getBoardConfigs(): IBoardConfig[] {
        const world = this.world;
        const config = world.configCenter;
        const boardEntityId = world.query([BoardComponent]);
        const boardConfigs: IBoardConfig[] = [];
        for (let i = 0; i < boardEntityId.length; i++) {
            const board = world.getComponent(boardEntityId[i], BoardComponent);
            const boardConfig = config.getBoardConfig(board.boardId);
            if (boardConfig) {
                boardConfigs.push(boardConfig);
            }
        }
        return boardConfigs;
    }

    /**
     * 在多层棋盘模式下获取最上层棋盘的boardId
     * 如果不是多层棋盘模式，返回原始boardId
     * @param originalBoardId 原始棋盘ID
     * @returns 最上层棋盘的boardId
     */
    public getTopLayerBoardId(originalBoardId?: string): string | null {
        // 检查是否是多层棋盘模式
        const allBoardEntities = this.world.query([BoardComponent]);
        const isMultiLayerMode = allBoardEntities.some((entityId) => {
            const boardComp = this.world.getComponent(entityId, BoardComponent);
            return boardComp.boardId.startsWith('multi_layer_');
        });

        if (!isMultiLayerMode) {
            // 不是多层棋盘模式，返回原始boardId
            return originalBoardId || null;
        }

        // 多层棋盘模式：找到最上层棋盘（zIndex最高的可放置棋盘）
        const topBoardEntity = allBoardEntities
            .filter((entityId) => {
                const boardComp = this.world.getComponent(entityId, BoardComponent);
                return boardComp.isCanPut === true; // 只考虑可放置的棋盘
            })
            .sort((a, b) => {
                const nodeA = this.world.getComponent(a, NodeComponent);
                const nodeB = this.world.getComponent(b, NodeComponent);
                return (nodeB.zIndex || 0) - (nodeA.zIndex || 0); // 从高到低排序
            })[0];

        if (topBoardEntity) {
            const topBoardComp = this.world.getComponent(topBoardEntity, BoardComponent);
            return topBoardComp.boardId;
        }

        // 如果没找到合适的棋盘，返回原始boardId
        return originalBoardId || null;
    }
}
