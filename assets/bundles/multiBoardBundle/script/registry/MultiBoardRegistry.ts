// assets/bundles/multiBoardBundle/script/registry/MultiBoardRegistry.ts

import { BaseModuleRegistry, moduleRegistryManager } from '../../../../advmain/scripts/modules/level/ecs/registry/ModuleRegistry';
import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { ECSRuleBase } from '../../../../advmain/scripts/modules/level/ecs/rules/core/ECSRuleBase';
import { ISystemConfig } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { IECSRuleConfig } from '../../../../advmain/scripts/modules/level/ecs/rules/core/ECSRuleBase';

// 多盘面规则
import { InitMultiBoardRule } from '../rule/InitMultiBoardRule';
import MultiBoardSystem from '../system/MultiBoardSystem';
import { MultiBoardTweenSystem } from '../system/MultiBoardTweenSystem';

// 多盘面系统常量
export const MultiBoardSystemConst = {
    MultiBoardSystem: 'MultiBoardSystem',
    MultiBoardTweenSystem: 'MultiBoardTweenSystem',
} as const;

// 多盘面规则常量
export const MultiBoardRuleConst = {
    InitMultiBoardRule: 'InitMultiBoardRule',
} as const;

// 多盘面规则配置接口
interface IInitMultiBoardRuleConfig extends IECSRuleConfig {
    boardIdList: string[];
}

// 多盘面系统注册表
const MultiBoardSystemRegistry: Record<string, clzz<System>> = {
    [MultiBoardSystemConst.MultiBoardSystem]: MultiBoardSystem,
    [MultiBoardSystemConst.MultiBoardTweenSystem]: MultiBoardTweenSystem,
};

// 多盘面规则注册表
const MultiBoardRuleRegistry: Record<string, clzz<ECSRuleBase>> = {
    [MultiBoardRuleConst.InitMultiBoardRule]: InitMultiBoardRule,
};

// 多盘面系统配置
const MultiBoardSystemConfig: Record<string, ISystemConfig> = {
    system_multi_board: {
        systemId: 'system_multi_board',
        systemName: MultiBoardSystemConst.MultiBoardSystem,
        close: false,
    },
    system_multi_board_tween: {
        systemId: 'system_multi_board_tween',
        systemName: MultiBoardSystemConst.MultiBoardTweenSystem,
        close: false,
    },
};

// 多盘面规则配置
const MultiBoardRuleConfig: Record<string, IInitMultiBoardRuleConfig> = {
    rule_init_multi_board: {
        ruleId: 'rule_init_multi_board',
        ruleDesc: '初始化层层叠叠',
        ruleType: MultiBoardRuleConst.InitMultiBoardRule,
        delay: 0,
        boardIdList: ['multi_layer_0', 'multi_layer_1', 'multi_layer_2', 'multi_layer_3', 'multi_layer_4', 'multi_layer_5'],
    },
};

/**
 * 多盘面玩法模块注册器
 */
export class MultiBoardModuleRegistry extends BaseModuleRegistry {
    readonly moduleName = 'MultiBoardModule';
    readonly version = '1.0.0';

    registerSystems(systemRegistry: Record<string, clzz<System>>): void {
        Object.assign(systemRegistry, MultiBoardSystemRegistry);
        console.log('[MultiBoardModule] 系统注册:', Object.keys(MultiBoardSystemRegistry));
    }

    registerRules(ruleRegistry: Record<string, clzz<ECSRuleBase>>): void {
        Object.assign(ruleRegistry, MultiBoardRuleRegistry);
        console.log('[MultiBoardModule] 规则注册:', Object.keys(MultiBoardRuleRegistry));
    }

    registerSystemConfigs(systemConfig: Record<string, ISystemConfig>): void {
        Object.assign(systemConfig, MultiBoardSystemConfig);
        console.log('[MultiBoardModule] 系统配置注册:', Object.keys(MultiBoardSystemConfig));
    }

    registerRuleConfigs(ruleConfig: Record<string, IECSRuleConfig>): void {
        Object.assign(ruleConfig, MultiBoardRuleConfig);
        console.log('[MultiBoardModule] 规则配置注册:', Object.keys(MultiBoardRuleConfig));
    }

    getSystemConstants(): Record<string, string> {
        return {
            MultiBoardSystem: MultiBoardSystemConst.MultiBoardSystem,
            MultiBoardTweenSystem: MultiBoardSystemConst.MultiBoardTweenSystem,
        };
    }

    getRuleConstants(): Record<string, string> {
        return {
            InitMultiBoardRule: MultiBoardRuleConst.InitMultiBoardRule,
        };
    }
}

// 自动注册到模块注册管理器
moduleRegistryManager.registerModule(new MultiBoardModuleRegistry());
