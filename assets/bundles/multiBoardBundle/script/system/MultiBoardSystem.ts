import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import { CellComponent } from '../../../../advmain/scripts/modules/level/ecs/components/board/CellComponent';
import SlotComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/SlotComponent';
import NodeComponent from '../../../../advmain/scripts/modules/level/ecs/components/NodeComponent';
import OpacityComponent from '../../../../advmain/scripts/modules/level/ecs/components/OpacityComponent';
import { RenderComponent } from '../../../../advmain/scripts/modules/level/ecs/components/RenderComponent';
import { IBoardConfig } from '../../../../advmain/scripts/modules/level/ecs/config/conf/BoardConfig';
import { RelationName } from '../../../../advmain/scripts/modules/level/ecs/cores/center/EntityCenter/WorldRelation';
import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { CellColor, CellType } from '../../../../advmain/scripts/modules/level/ecs/define/BoardDefine';
import { IPoint } from '../../../../advmain/scripts/modules/level/ecs/define/EcsDefine';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';
import { IEliminationInfo } from '../../../../advmain/scripts/modules/level/ecs/GameEventData';
import { TempleteType } from '../../../../advmain/scripts/modules/level/ecs/registry/templete/TempleteRegistry';
import { BundleName } from '../../../../advmain/scripts/modules/preload/config/PreloadConfig';

// 接口定义
interface ICellInfo {
    type: CellType;
    color: CellColor;
    layer: number;
}

interface IBoardInfo {
    sourceBoardEntity: number;
    targetBoardEntity: number;
    sourceBoardComp: BoardComponent;
    targetBoardComp: BoardComponent;
    sourceRow: number;
    sourceCol: number;
    targetRow: number;
    targetCol: number;
}

interface IBoardWithZIndex {
    entityId: number;
    zIndex: number;
    boardId: string;
}

interface IMoveOperation {
    sourceBoardEntityId: number;
    sourceRow: number;
    sourceCol: number;
}

interface IRectangle {
    x: number;
    y: number;
    width: number;
    height: number;
}

// 扩展的NodeComponent接口，包含临时属性
interface IExtendedNodeComponent extends NodeComponent {
    _bubbleEffectEntity?: number;
}

/**
 * 多层棋盘系统
 * 负责处理多层棋盘的遮挡判断和交互逻辑
 * 监听消除事件，在每次消除后执行冒泡
 */
export default class MultiBoardSystem extends System {
    // 延迟任务队列
    private delayedTasks: Array<{
        executeTime: number;
        callback: () => void;
        description: string;
    }> = [];

    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.ELIMINATION, this.onElimination, this);
        this.world.eventBus.on(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_DEPLOYED, this.onMultiBoardBlocksDeployed, this);
    }

    /**
     * 系统销毁方法 - 清理资源
     */
    dispose(): void {
        // 清理延迟任务队列
        this.delayedTasks.length = 0;
        // 移除事件监听器
        this.world.eventBus.off(ECSEvent.GameEvent.ELIMINATION, this.onElimination, this);
        this.world.eventBus.off(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_DEPLOYED, this.onMultiBoardBlocksDeployed, this);
    }

    /**
     * 系统更新方法 - 处理延迟任务
     */
    update(dt: number): void {
        this.processDelayedTasks();
    }

    /**
     * 处理延迟任务队列
     */
    private processDelayedTasks(): void {
        const currentTime = Date.now();
        for (let i = this.delayedTasks.length - 1; i >= 0; i--) {
            const task = this.delayedTasks[i];
            if (currentTime >= task.executeTime) {
                task.callback();
                this.delayedTasks.splice(i, 1);
            }
        }
    }

    /**
     * 添加延迟任务
     */
    private addDelayedTask(delayMs: number, callback: () => void, description: string): void {
        this.delayedTasks.push({
            executeTime: Date.now() + delayMs,
            callback,
            description,
        });
    }

    /**
     * 消除事件处理器
     * 在每次消除完成后自动执行冒泡操作
     * @param eliminationInfo 消除信息
     */
    private onElimination(eliminationInfo: IEliminationInfo): void {
        if (!eliminationInfo) {
            return;
        }
        this.addDelayedTask(
            100,
            () => {
                this.bubbleUnoccludedBlocksToTop();
            },
            '消除后冒泡操作',
        );
    }

    /**
     * 多层棋盘块部署完成事件处理器
     * 在所有块发完并移动到位后自动执行冒泡操作，然后保存快照
     */
    private onMultiBoardBlocksDeployed(): void {
        this.addDelayedTask(
            200,
            () => {
                this.bubbleUnoccludedBlocksToTop();
                this.addDelayedTask(
                    1200,
                    () => {
                        // game.saveSnapshot();
                    },
                    '保存快照',
                );
            },
            '块部署完成后冒泡',
        );
    }

    /**
     * 判断指定棋盘实体上某个位置的块是否被上层棋盘的块遮挡
     * @param targetBoardEntityId 目标棋盘实体ID
     * @param row 行坐标
     * @param col 列坐标
     * @returns true表示被遮挡，false表示未被遮挡
     */
    isBlockOccludedByUpperLayers(targetBoardEntityId: number, row: number, col: number): boolean {
        const boards = this.world.utilCenter.boardUtil.getBoardConfigs();
        if (!boards) return false;
        const targetBoardComp = this.world.getComponent(targetBoardEntityId, BoardComponent);
        if (!targetBoardComp) return false;
        const targetBoardNode = this.world.getComponent(targetBoardEntityId, NodeComponent);
        const targetZIndex = targetBoardNode.zIndex || 0;
        // 找到目标棋盘的配置
        const targetBoardConfig = boards.find((b) => b.boardId === targetBoardComp.boardId);
        if (!targetBoardConfig) return false;
        // 首先检查目标位置是否真的有块
        const targetSlotEntity = this.world.getTargets(targetBoardEntityId, RelationName.PARENT_CHILD, `${row}_${col}`)[0];
        if (!targetSlotEntity || !this.hasBlocksInSlot(targetSlotEntity)) {
            return false;
        }
        const allBoardEntities = this.world.query([BoardComponent]);
        for (const boardEntityId of allBoardEntities) {
            if (boardEntityId === targetBoardEntityId) continue;
            const boardComp = this.world.getComponent(boardEntityId, BoardComponent);
            const boardNode = this.world.getComponent(boardEntityId, NodeComponent);
            const boardZIndex = boardNode.zIndex || 0;
            // 只检查zIndex更高的棋盘（更靠前的棋盘）
            if (boardZIndex <= targetZIndex) continue;
            // 找到该棋盘的配置
            const boardConfig = boards.find((b) => b.boardId === boardComp.boardId);
            if (!boardConfig) continue;
            // 使用新的方法检查该棋盘是否在对应位置有块
            if (this.hasVisibleBlockAtCorrespondingPosition(boardConfig, boardEntityId, targetBoardConfig, row, col)) {
                return true; // 被遮挡
            }
        }
        return false; // 未被遮挡
    }

    /**
     * 冒泡操作：将下层盘面没有遮挡的块移动到最上层盘面
     * @returns 移动的块数量
     */
    bubbleUnoccludedBlocksToTop(): number {
        const allBoardEntities = this.world.query([BoardComponent]);
        if (allBoardEntities.length <= 1) {
            return 0;
        }
        const boardsWithZIndex = allBoardEntities
            .map((entityId) => {
                const node = this.world.getComponent(entityId, NodeComponent);
                const boardComp = this.world.getComponent(entityId, BoardComponent);
                const zIndex = node.zIndex || 0;
                return {
                    entityId,
                    zIndex,
                    boardId: boardComp.boardId,
                };
            })
            .sort((a, b) => b.zIndex - a.zIndex);

        const topBoardEntityId = boardsWithZIndex[0].entityId;
        this.startBatchBubbleProcess(boardsWithZIndex, topBoardEntityId);
        return 0;
    }

    /**
     * 开始分批次冒泡处理
     * @param boardsWithZIndex 按zIndex排序的棋盘列表
     * @param topBoardEntityId 最上层棋盘实体ID
     */
    private startBatchBubbleProcess(boardsWithZIndex: IBoardWithZIndex[], topBoardEntityId: number): void {
        let totalMoved = 0;

        const processBatch = (batchIndex: number) => {
            const batchMoves = this.findCurrentBatchMoves(boardsWithZIndex, topBoardEntityId);
            // 没有更多可移动的块，冒泡完成
            if (batchMoves.length === 0) {
                return;
            }
            let completedAnimations = 0;
            const batchSize = batchMoves.length;
            for (const move of batchMoves) {
                this.executeBlockMove(move, () => {
                    completedAnimations++;
                    totalMoved++;
                    if (completedAnimations === batchSize) {
                        this.addDelayedTask(
                            100,
                            () => {
                                processBatch(batchIndex + 1);
                            },
                            `处理第${batchIndex + 1}批次冒泡`,
                        );
                    }
                });
            }
        };
        processBatch(0);
    }

    /**
     * 找到当前批次可以移动的块
     * @param boardsWithZIndex 按zIndex排序的棋盘列表
     * @param topBoardEntityId 最上层棋盘实体ID
     * @returns 当前批次的移动操作数组
     */
    private findCurrentBatchMoves(boardsWithZIndex: IBoardWithZIndex[], topBoardEntityId: number): IMoveOperation[] {
        const moves: IMoveOperation[] = [];
        const boards = this.world.utilCenter.boardUtil.getBoardConfigs();
        for (let i = 1; i < boardsWithZIndex.length; i++) {
            const sourceBoardEntityId = boardsWithZIndex[i].entityId;
            const sourceBoardComp = this.world.getComponent(sourceBoardEntityId, BoardComponent);
            if (!sourceBoardComp) continue;
            const sourceBoardConfig = boards.find((b) => b.boardId === sourceBoardComp.boardId);
            if (!sourceBoardConfig) continue;
            // 检查该棋盘的每个位置
            for (let row = 0; row < sourceBoardConfig.rows; row++) {
                for (let col = 0; col < sourceBoardConfig.cols; col++) {
                    // 检查是否被遮挡
                    if (this.isBlockOccludedByUpperLayers(sourceBoardEntityId, row, col)) {
                        continue; // 被遮挡的块跳过
                    }
                    // 检查是否有块
                    const sourceSlotEntity = this.world.getTargets(sourceBoardEntityId, RelationName.PARENT_CHILD, `${row}_${col}`)[0];
                    if (!sourceSlotEntity || !this.hasBlocksInSlot(sourceSlotEntity)) {
                        continue;
                    }
                    // 检查目标位置是否可用
                    if (this.canMoveToTarget(sourceBoardEntityId, topBoardEntityId, row, col)) {
                        moves.push({
                            sourceBoardEntityId,
                            sourceRow: row,
                            sourceCol: col,
                        });
                    }
                }
            }
        }
        return moves;
    }

    /**
     * 检查是否可以移动到目标位置
     * @param sourceBoardEntityId 源棋盘实体ID
     * @param targetBoardEntityId 目标棋盘实体ID
     * @param sourceRow 源行坐标
     * @param sourceCol 源列坐标
     * @returns 是否可以移动
     */
    private canMoveToTarget(sourceBoardEntityId: number, targetBoardEntityId: number, sourceRow: number, sourceCol: number): boolean {
        const boards = this.world.utilCenter.boardUtil.getBoardConfigs();
        const sourceBoardComp = this.world.getComponent(sourceBoardEntityId, BoardComponent);
        const targetBoardComp = this.world.getComponent(targetBoardEntityId, BoardComponent);
        const sourceBoardConfig = boards.find((b) => b.boardId === sourceBoardComp.boardId);
        const targetBoardConfig = boards.find((b) => b.boardId === targetBoardComp.boardId);
        if (!sourceBoardConfig || !targetBoardConfig) return false;
        // 计算目标位置
        const targetPos = this.calculateTargetPosition(sourceBoardConfig, targetBoardConfig, sourceRow, sourceCol);
        if (!targetPos) return false;
        const { row: targetRow, col: targetCol } = targetPos;
        // 检查目标位置是否在范围内
        if (targetRow < 0 || targetRow >= targetBoardConfig.rows || targetCol < 0 || targetCol >= targetBoardConfig.cols) {
            return false;
        }
        // 检查目标位置是否为空
        const targetSlotEntity = this.world.getTargets(targetBoardEntityId, RelationName.PARENT_CHILD, `${targetRow}_${targetCol}`)[0];
        return targetSlotEntity && !this.hasBlocksInSlot(targetSlotEntity);
    }

    /**
     * 执行单个块的移动操作
     * @param move 移动操作信息
     * @param onComplete 完成回调
     */
    private executeBlockMove(move: IMoveOperation, onComplete: () => void): void {
        const sourceBoardComp = this.world.getComponent(move.sourceBoardEntityId, BoardComponent);
        const sourceSlotEntity = this.world.getTargets(move.sourceBoardEntityId, RelationName.PARENT_CHILD, `${move.sourceRow}_${move.sourceCol}`)[0];
        if (!sourceSlotEntity) {
            onComplete();
            return;
        }
        const allBoardEntities = this.world.query([BoardComponent]);
        const boardsWithZIndex = allBoardEntities
            .map((entityId) => {
                const node = this.world.getComponent(entityId, NodeComponent);
                const zIndex = node.zIndex || 0;
                return { entityId, zIndex };
            })
            .sort((a, b) => b.zIndex - a.zIndex);

        const topBoardEntityId = boardsWithZIndex[0].entityId;
        const topBoardComp = this.world.getComponent(topBoardEntityId, BoardComponent);
        const boards = this.world.utilCenter.boardUtil.getBoardConfigs();
        const sourceBoardConfig = boards.find((b) => b.boardId === sourceBoardComp.boardId);
        const targetBoardConfig = boards.find((b) => b.boardId === topBoardComp.boardId);
        const targetPos = this.calculateTargetPosition(sourceBoardConfig, targetBoardConfig, move.sourceRow, move.sourceCol);
        if (!targetPos) {
            onComplete();
            return;
        }
        const targetSlotEntity = this.world.getTargets(topBoardEntityId, RelationName.PARENT_CHILD, `${targetPos.row}_${targetPos.col}`)[0];
        if (!targetSlotEntity) {
            onComplete();
            return;
        }
        this.moveBlocksFromSlotToSlotWithCallback(sourceSlotEntity, targetSlotEntity, onComplete);
    }

    /**
     * 带回调的块移动函数
     * @param sourceSlotEntityId 源slot实体ID
     * @param targetSlotEntityId 目标slot实体ID
     * @param onComplete 完成回调
     */
    private moveBlocksFromSlotToSlotWithCallback(sourceSlotEntityId: number, targetSlotEntityId: number, onComplete: () => void): void {
        const result = this.moveBlocksFromSlotToSlot(sourceSlotEntityId, targetSlotEntityId);
        if (!result) {
            onComplete();
            return;
        }
        this.addDelayedTask(
            350,
            () => {
                onComplete();
            },
            '块移动动画完成回调',
        );
    }

    /**
     * 计算块在目标棋盘上的对应位置
     * 使用坐标映射来处理7x7和8x8棋盘的特殊关系
     * @param sourceBoardConfig 源棋盘配置
     * @param targetBoardConfig 目标棋盘配置
     * @param sourceRow 源行坐标
     * @param sourceCol 源列坐标
     * @returns 目标位置坐标，如果无法映射返回null
     */
    private calculateTargetPosition(
        sourceBoardConfig: IBoardConfig,
        targetBoardConfig: IBoardConfig,
        sourceRow: number,
        sourceCol: number,
    ): { row: number; col: number } | null {
        const targetPos = this.convertBoardCoordinates(sourceBoardConfig, targetBoardConfig, sourceRow, sourceCol);
        if (!targetPos) {
            return null;
        }
        return targetPos;
    }

    /**
     * 检查slot中是否有块
     * @param slotEntityId slot实体ID
     * @returns true表示有块，false表示没有
     */
    private hasBlocksInSlot(slotEntityId: number): boolean {
        return this.world.getTargets(slotEntityId, RelationName.SLOT_CELL).length > 0;
    }

    /**
     * 将块从源slot移动到目标slot
     * 块会在原位置生成，然后通过缓动动画移动到目标位置
     * @param sourceSlotEntityId 源slot实体ID
     * @param targetSlotEntityId 目标slot实体ID
     * @returns true表示移动成功，false表示失败
     */
    private moveBlocksFromSlotToSlot(sourceSlotEntityId: number, targetSlotEntityId: number): boolean {
        const { sourceSlotComp, targetSlotComp } = this.validateSlotComponents(sourceSlotEntityId, targetSlotEntityId);
        if (!sourceSlotComp || !targetSlotComp) return false;
        // 获取棋盘信息和位置
        const boardInfo = this.getBoardInfoAndPositions(sourceSlotEntityId, targetSlotEntityId);
        if (!boardInfo) return false;
        // 收集要移动的cell信息
        const cellsToMove = this.collectCellsToMove(sourceSlotComp, sourceSlotEntityId);
        if (cellsToMove.length === 0) return false;
        // 在目标位置创建新的cells并添加动画
        return this.createCellsInTargetPosition(cellsToMove, targetSlotComp, boardInfo);
    }

    /**
     * 验证源和目标slot组件
     */
    private validateSlotComponents(sourceSlotEntityId: number, targetSlotEntityId: number) {
        const sourceSlotComp = this.world.getComponent(sourceSlotEntityId, SlotComponent);
        const targetSlotComp = this.world.getComponent(targetSlotEntityId, SlotComponent);
        return { sourceSlotComp, targetSlotComp };
    }

    /**
     * 获取棋盘信息和位置坐标
     */
    private getBoardInfoAndPositions(sourceSlotEntityId: number, targetSlotEntityId: number): IBoardInfo | null {
        const sourceBoardEntity = this.world.getSource(sourceSlotEntityId, RelationName.PARENT_CHILD);
        const targetBoardEntity = this.world.getSource(targetSlotEntityId, RelationName.PARENT_CHILD);

        const sourceBoardComp = this.world.getComponent(sourceBoardEntity, BoardComponent);
        const targetBoardComp = this.world.getComponent(targetBoardEntity, BoardComponent);

        if (!sourceBoardComp || !targetBoardComp) return null;

        const sourcePosition = this.findSlotPosition(sourceSlotEntityId);
        const targetPosition = this.findSlotPosition(targetSlotEntityId);

        if (!sourcePosition || !targetPosition) return null;

        return {
            sourceBoardEntity,
            targetBoardEntity,
            sourceBoardComp,
            targetBoardComp,
            sourceRow: sourcePosition.row,
            sourceCol: sourcePosition.col,
            targetRow: targetPosition.row,
            targetCol: targetPosition.col,
        };
    }

    /**
     * 查找slot在棋盘中的位置
     */
    private findSlotPosition(slotEntityId: number): { row: number; col: number } | null {
        const slot = this.world.getComponent(slotEntityId, SlotComponent);
        return { row: slot.r, col: slot.c };
    }

    /**
     * 收集要移动的cell信息
     */
    private collectCellsToMove(sourceSlotComp: SlotComponent, sourceSlotEntityId: number): ICellInfo[] {
        const layerEntitys = this.world.getTargets(sourceSlotComp.entity, RelationName.SLOT_CELL);
        if (layerEntitys.length === 0) {
            return [];
        }
        const actualCells = layerEntitys.filter((id) => id !== null && id !== undefined);
        if (actualCells.length === 0) {
            return [];
        }
        const cellsToMove: ICellInfo[] = [];
        // 收集cell信息并销毁源cell
        for (const cellEntityId of actualCells) {
            const cellComp = this.world.getComponent(cellEntityId, CellComponent);
            if (!cellComp) continue;
            cellsToMove.push({
                type: cellComp.type,
                color: cellComp.oriColor,
                layer: cellComp.layer,
            });

            this.world.destroyEntity(cellEntityId);
        }
        // 清理源slot上的遮罩
        this.clearSlotMasks(sourceSlotEntityId);
        return cellsToMove;
    }

    /**
     * 在目标位置创建新的cells
     */
    private createCellsInTargetPosition(cellsToMove: ICellInfo[], targetSlotComp: SlotComponent, boardInfo: IBoardInfo): boolean {
        let moved = false;
        for (const cellInfo of cellsToMove) {
            const newCellEntity = this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                rc: { r: boardInfo.targetRow, c: boardInfo.targetCol },
                parentSize: { width: boardInfo.targetBoardComp.cCount, height: boardInfo.targetBoardComp.rCount },
                parentEntity: boardInfo.targetBoardEntity,
                cellOption: {
                    type: cellInfo.type,
                    color: cellInfo.color,
                    occupy: true,
                    through: true,
                },
                slotEntity: targetSlotComp.entity,
            });
            moved = true;
            const opacityComp = this.world.getComponent(newCellEntity, OpacityComponent);
            if (opacityComp) {
                opacityComp.opacity = 255;
            } else {
                // 如果没有透明度组件，添加一个
                const opacity = this.world.addComponent(newCellEntity, OpacityComponent);
                opacity.opacity = 0;
            }
            // 为新创建的块添加冒泡特效
            this.createBubbleEffect(newCellEntity);
            // 添加动画效果
            this.animateBlockFromSourceToTarget(
                newCellEntity,
                boardInfo.sourceBoardEntity,
                boardInfo.targetBoardEntity,
                boardInfo.sourceRow,
                boardInfo.sourceCol,
                boardInfo.targetRow,
                boardInfo.targetCol,
            );
        }
        return moved;
    }

    /**
     * 为块添加从源位置到目标位置的动画效果
     * @param cellEntityId 要移动的cell实体ID
     * @param sourceBoardEntity 源棋盘实体
     * @param targetBoardEntity 目标棋盘实体
     * @param sourceRow 源行坐标
     * @param sourceCol 源列坐标
     * @param targetRow 目标行坐标
     * @param targetCol 目标列坐标
     */
    private animateBlockFromSourceToTarget(
        cellEntityId: number,
        sourceBoardEntity: number,
        targetBoardEntity: number,
        sourceRow: number,
        sourceCol: number,
        targetRow: number,
        targetCol: number,
    ): void {
        // 获取棋盘配置信息
        const boards = this.world.utilCenter.boardUtil.getBoardConfigs();
        const sourceBoardComp = this.world.getComponent(sourceBoardEntity, BoardComponent);
        const targetBoardComp = this.world.getComponent(targetBoardEntity, BoardComponent);
        const sourceBoardConfig = boards.find((b) => b.boardId === sourceBoardComp.boardId);
        const targetBoardConfig = boards.find((b) => b.boardId === targetBoardComp.boardId);
        if (!sourceBoardConfig || !targetBoardConfig) return;
        // 计算源位置和目标位置的世界坐标
        const sourceWorldPos = this.getBoardCellWorldPosition(sourceBoardConfig, sourceRow, sourceCol);
        const targetWorldPos = this.getBoardCellWorldPosition(targetBoardConfig, targetRow, targetCol);
        const nodeComp = this.world.getComponent(cellEntityId, NodeComponent);
        if (nodeComp) {
            nodeComp.x = sourceWorldPos.x;
            nodeComp.y = sourceWorldPos.y;
            nodeComp.scaleX = nodeComp.scaleY = 1; // 设置初始缩放
            // 同步设置特效的初始位置
            const effectEntityId = (nodeComp as IExtendedNodeComponent)._bubbleEffectEntity;
            if (effectEntityId) {
                const effectNodeComp = this.world.getComponent(effectEntityId, NodeComponent);
                if (effectNodeComp) {
                    effectNodeComp.x = sourceWorldPos.x;
                    effectNodeComp.y = sourceWorldPos.y;
                }
            }
            // 延迟一帧后开始缓动动画到目标位置
            this.addDelayedTask(
                50,
                () => {
                    this.startTweenAnimationWithScale(cellEntityId, targetWorldPos, () => {});
                },
                '开始块移动缓动动画',
            );
        }
    }

    /**
     * 开始带缩放效果的缓动动画
     * @param cellEntityId cell实体ID
     * @param targetPos 目标位置
     * @param onComplete 完成回调
     */
    private startTweenAnimationWithScale(cellEntityId: number, targetPos: IPoint, onComplete: () => void): void {
        const nodeComp = this.world.getComponent(cellEntityId, NodeComponent);
        if (!nodeComp) return;
        const startPos = { x: nodeComp.x, y: nodeComp.y };
        const duration = 296; // 总动画时间
        const scaleDownDuration = 132; // 缩小阶段：182ms
        const pauseDuration = 132; // 停顿阶段：182ms
        const scaleUpDuration = 132; // 放大阶段：132ms
        // const fadeInDuration = 300; // 透明度渐变时间：200ms
        const startTime = Date.now();
        const updatePosition = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            // 缓出效果 (easeOut) - 用于位置动画
            const easeProgress = 1 - Math.pow(1 - progress, 2);
            // 位置动画 - 贯穿整个动画过程
            const currentX = startPos.x + (targetPos.x - startPos.x) * easeProgress;
            const currentY = startPos.y + (targetPos.y - startPos.y) * easeProgress;
            nodeComp.x = currentX;
            nodeComp.y = currentY;
            // 同步更新特效位置
            const effectEntityId = (nodeComp as IExtendedNodeComponent)._bubbleEffectEntity;
            if (effectEntityId) {
                const effectNodeComp = this.world.getComponent(effectEntityId, NodeComponent);
                if (effectNodeComp) {
                    effectNodeComp.x = currentX;
                    effectNodeComp.y = currentY;
                }
            }
            // // 透明度动画 - 在前200ms内从0渐变到255
            const opacityComp = this.world.getComponent(cellEntityId, OpacityComponent);
            // if (opacityComp) {
            //     if (elapsed <= fadeInDuration) {
            //         const fadeProgress = elapsed / fadeInDuration;
            //         opacityComp.opacity = Math.floor(fadeProgress * 255);
            //     } else {
            //         opacityComp.opacity = 255; // 完全不透明
            //     }
            //     opacityComp.opacity = 0;
            // }
            // 缩放动画 - 分三个阶段
            let scale: number;
            if (elapsed <= scaleDownDuration) {
                const scaleProgress = elapsed / scaleDownDuration;
                scale = 1 - scaleProgress * 0.3; // 1 -> 0.7
            } else if (elapsed <= scaleDownDuration + pauseDuration) {
                scale = 0;
            } else if (elapsed <= scaleDownDuration + pauseDuration + scaleUpDuration) {
                const scaleUpElapsed = elapsed - scaleDownDuration - pauseDuration;
                const scaleProgress = scaleUpElapsed / scaleUpDuration;
                scale = 1 + scaleProgress * 0.3; // 0.7 -> 1
            } else {
                scale = 1;
            }
            nodeComp.scaleX = nodeComp.scaleY = scale;
            if (progress < 1) {
                requestAnimationFrame(updatePosition);
            } else {
                // 动画完成，确保最终状态正确
                nodeComp.x = targetPos.x;
                nodeComp.y = targetPos.y;
                nodeComp.scaleX = nodeComp.scaleY = 1;
                if (opacityComp) {
                    opacityComp.opacity = 255; // 确保最终完全不透明
                }
                onComplete();
            }
        };
        updatePosition();
    }

    /**
     * 获取棋盘上指定格子在世界坐标系中的位置
     * @param boardConfig 棋盘配置
     * @param row 行坐标
     * @param col 列坐标
     * @returns 世界坐标位置
     */
    private getBoardCellWorldPosition(boardConfig: IBoardConfig, row: number, col: number): IPoint {
        const cellSize = boardConfig.cellSize;
        const boardCenterX = boardConfig.startPos.x;
        const boardCenterY = boardConfig.startPos.y;
        // 计算格子相对于棋盘中心的偏移
        const offsetX = (col - (boardConfig.cols - 1) / 2) * cellSize;
        const offsetY = ((boardConfig.rows - 1) / 2 - row) * cellSize;
        return {
            x: boardCenterX + offsetX,
            y: boardCenterY + offsetY,
        };
    }

    /**
     * 在不同尺寸棋盘间转换坐标
     * 处理7x7和8x8棋盘的特殊对应关系：7x7是8x8去掉第3行和第3列
     * @param sourceConfig 源棋盘配置
     * @param targetConfig 目标棋盘配置
     * @param sourceRow 源行坐标
     * @param sourceCol 源列坐标
     * @returns 目标棋盘的坐标，如果无法映射返回null
     */
    private convertBoardCoordinates(
        sourceConfig: IBoardConfig,
        targetConfig: IBoardConfig,
        sourceRow: number,
        sourceCol: number,
    ): { row: number; col: number } | null {
        if (sourceConfig.rows === targetConfig.rows && sourceConfig.cols === targetConfig.cols) {
            return { row: sourceRow, col: sourceCol };
        }
        // 处理8x8 -> 7x7的转换
        if (sourceConfig.rows === 8 && sourceConfig.cols === 8 && targetConfig.rows === 7 && targetConfig.cols === 7) {
            // 8x8去掉第3行第3列变成7x7
            // 8x8: (0,1,2,3,4,5,6,7) -> 7x7: (0,1,2,×,3,4,5,6)
            let targetRow = sourceRow;
            let targetCol = sourceCol;
            // 处理行：第3行被移除
            if (sourceRow === 3) {
                return null;
            }
            if (sourceRow > 3) {
                targetRow = sourceRow - 1; // 第4-7行变成第3-6行
            }
            // 处理列：第3列被移除
            if (sourceCol === 3) {
                return null;
            }
            if (sourceCol > 3) {
                targetCol = sourceCol - 1; // 第4-7列变成第3-6列
            }
            return { row: targetRow, col: targetCol };
        }

        // 处理7x7 -> 8x8的转换
        if (sourceConfig.rows === 7 && sourceConfig.cols === 7 && targetConfig.rows === 8 && targetConfig.cols === 8) {
            let targetRow = sourceRow;
            let targetCol = sourceCol;

            // 恢复行：7x7的(0,1,2)保持不变，(3,4,5,6)变成(4,5,6,7)
            if (sourceRow >= 3) {
                targetRow = sourceRow + 1; // 跳过第3行
            }

            // 恢复列：7x7的(0,1,2)保持不变，(3,4,5,6)变成(4,5,6,7)
            if (sourceCol >= 3) {
                targetCol = sourceCol + 1; // 跳过第3列
            }
            return { row: targetRow, col: targetCol };
        }
        return null;
    }

    /**
     * 检查指定棋盘的指定位置是否被其他棋盘在对应位置的块遮挡
     * 使用世界坐标矩形重叠来判断真正的视觉遮挡
     */
    private hasVisibleBlockAtCorrespondingPosition(
        checkingBoardConfig: IBoardConfig,
        checkingBoardEntityId: number,
        sourceBoardConfig: IBoardConfig,
        sourceRow: number,
        sourceCol: number,
    ): boolean {
        // 获取源格子的世界坐标范围（矩形）
        const sourceRect = this.getBoardCellWorldRect(sourceBoardConfig, sourceRow, sourceCol);
        // 检查上层棋盘的所有格子，看是否有任何格子与源格子的矩形重叠
        for (let row = 0; row < checkingBoardConfig.rows; row++) {
            for (let col = 0; col < checkingBoardConfig.cols; col++) {
                // 获取检查格子的世界坐标矩形
                const checkRect = this.getBoardCellWorldRect(checkingBoardConfig, row, col);
                // 检查两个矩形是否重叠
                if (this.isRectOverlap(sourceRect, checkRect)) {
                    // 检查这个位置是否有块
                    const slotEntity = this.world.getTargets(checkingBoardEntityId, RelationName.PARENT_CHILD, `${row}_${col}`)[0];
                    if (slotEntity && this.hasBlocksInSlot(slotEntity)) {
                        return true; // 找到遮挡
                    }
                }
            }
        }

        return false;
    }

    /**
     * 获取棋盘格子在世界坐标系中的矩形范围
     * @param boardConfig 棋盘配置
     * @param row 行坐标
     * @param col 列坐标
     * @returns 世界坐标矩形 {x, y, width, height}
     */
    private getBoardCellWorldRect(boardConfig: IBoardConfig, row: number, col: number): IRectangle {
        const cellSize = boardConfig.cellSize;
        const centerPos = this.getBoardCellWorldPosition(boardConfig, row, col);
        // 格子是以中心点为基准的正方形
        const halfSize = cellSize / 2;
        return {
            x: centerPos.x - halfSize,
            y: centerPos.y - halfSize,
            width: cellSize,
            height: cellSize,
        };
    }

    /**
     * 检查两个矩形是否重叠
     * @param rect1 第一个矩形 {x, y, width, height}
     * @param rect2 第二个矩形 {x, y, width, height}
     * @returns true表示重叠，false表示不重叠
     */
    private isRectOverlap(rect1: IRectangle, rect2: IRectangle): boolean {
        const noOverlap =
            rect1.x + rect1.width <= rect2.x || // rect1在rect2左边
            rect1.x >= rect2.x + rect2.width || // rect1在rect2右边
            rect1.y + rect1.height <= rect2.y || // rect1在rect2上边
            rect1.y >= rect2.y + rect2.height; // rect1在rect2下边

        return !noOverlap;
    }

    /**
     * 获取指定棋盘上所有被遮挡的格子位置
     * @param boardEntityId 棋盘实体ID
     * @returns 被遮挡的格子位置数组
     */
    getOccludedCells(boardEntityId: number): Array<{ row: number; col: number }> {
        const boardComp = this.world.getComponent(boardEntityId, BoardComponent);
        if (!boardComp) return [];
        const boards = this.world.utilCenter.boardUtil.getBoardConfigs();
        const boardConfig = boards?.find((b) => b.boardId === boardComp.boardId);
        if (!boardConfig) return [];
        const occludedCells: Array<{ row: number; col: number }> = [];
        for (let row = 0; row < boardConfig.rows; row++) {
            for (let col = 0; col < boardConfig.cols; col++) {
                if (this.isBlockOccludedByUpperLayers(boardEntityId, row, col)) {
                    occludedCells.push({ row, col });
                }
            }
        }
        return occludedCells;
    }

    /**
     * 为块创建冒泡特效
     * @param cellEntityId 块实体ID
     */
    private createBubbleEffect(cellEntityId: number): void {
        // 创建特效实体
        const effectEntity = this.world.createEntity();
        // 获取块的父节点（通常是slot或board）
        const parentEntity = this.world.getSource(cellEntityId, RelationName.PARENT_CHILD);
        if (!parentEntity) {
            this.world.destroyEntity(effectEntity);
            return;
        }
        // 设置父子关系：特效与块共享同一个父节点
        this.world.addRelation(parentEntity, effectEntity, RelationName.PARENT_CHILD);
        // 获取块的位置和层级信息
        const cellNodeComp = this.world.getComponent(cellEntityId, NodeComponent);
        if (!cellNodeComp) {
            this.world.destroyEntity(effectEntity);
            return;
        }
        // 设置特效位置：与块相同的位置
        const effectNodeComp = this.world.addComponent(effectEntity, NodeComponent, {
            x: cellNodeComp.x,
            y: cellNodeComp.y,
        });

        // 设置特效层级：比块高0.1，确保在块上方显示
        effectNodeComp.zIndex = (cellNodeComp.zIndex || 0) + 0.1;
        // 添加渲染组件：使用冒泡特效预制体
        this.world.addComponent(effectEntity, RenderComponent, 'ecsview_simple_effect', {
            data: {
                bundleName: 'multiBoardBundle',
                aniName: 'blue_move',
                src: 'spine/gameplay_effect_block_light_ske',
                dragonNames: ['gameplay_effect_block_light_tex', 'armatureName'],
                enableBatch: true,
            },
        });

        // 将特效实体ID保存到块上，用于动画同步
        // 使用临时属性保存特效实体ID
        (cellNodeComp as IExtendedNodeComponent)._bubbleEffectEntity = effectEntity;
        // 🔧 使用延迟任务替代setTimeout
        this.addDelayedTask(
            1000,
            () => {
                this.world.destroyEntity(effectEntity);
            },
            '销毁冒泡特效',
        );
    }

    /**
     * 清理指定slot上的遮罩
     * @param slotEntityId slot实体ID
     */
    private clearSlotMasks(slotEntityId: number): void {
        const maskEntities = this.world.getTargets(slotEntityId, RelationName.SLOT_MASK);
        if (maskEntities && maskEntities.length > 0) {
            // 销毁所有绑定的遮罩实体
            for (const maskEntityId of maskEntities) {
                this.world.destroyEntity(maskEntityId);
            }
        }
    }
}
