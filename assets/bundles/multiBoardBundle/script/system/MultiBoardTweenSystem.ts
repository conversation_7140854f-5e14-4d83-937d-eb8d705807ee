import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import { CellComponent } from '../../../../advmain/scripts/modules/level/ecs/components/board/CellComponent';
import SlotComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/SlotComponent';
import NodeComponent from '../../../../advmain/scripts/modules/level/ecs/components/NodeComponent';
import OpacityComponent from '../../../../advmain/scripts/modules/level/ecs/components/OpacityComponent';
import { RenderComponent } from '../../../../advmain/scripts/modules/level/ecs/components/RenderComponent';
import { IBoardConfig } from '../../../../advmain/scripts/modules/level/ecs/config/conf/BoardConfig';
import { RelationName } from '../../../../advmain/scripts/modules/level/ecs/cores/center/EntityCenter/WorldRelation';
import { Component } from '../../../../advmain/scripts/modules/level/ecs/cores/Component';
import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { BoardLayer, CellColor, CellType } from '../../../../advmain/scripts/modules/level/ecs/define/BoardDefine';
import { IPoint } from '../../../../advmain/scripts/modules/level/ecs/define/EcsDefine';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';

// 接口定义
interface IBlockInfo {
    slotEntity: number;
    cellConfigs: ICellConfig[];
    boardCenter: IPoint;
    targetPosition: IPoint;
    boardEntity: number;
    rc: IRowCol;
}

interface ICellConfig {
    color: CellColor;
    type: CellType;
}

interface IRowCol {
    r: number;
    c: number;
}

interface ILayerBlocksData {
    layerIndex: number;
    blocks: IBlockInfo[];
}

interface IAllBlocksData extends IBlockInfo {
    globalIndex: number;
    layerIndex: number;
}

interface IRectangle {
    x: number;
    y: number;
    width: number;
    height: number;
}

/**
 * 多层棋盘动画系统 - 负责块的移动动画和遮挡检测
 */
export class MultiBoardTweenSystem extends System {
    private occlusionStats?: { count: number; totalTime: number; maxTime: number };
    // 空间分区缓存：用于优化遮挡检测
    private spatialHashMap: Map<string, Array<{ entity: number; rect: IRectangle; layerIndex: number }>> = new Map();
    private readonly SPATIAL_GRID_SIZE = 200; // 空间网格大小
    // 组件缓存：实体ID -> 组件类型 -> 组件实例
    private componentCache: Map<number, Map<string, any>> = new Map();

    // 动画控制状态
    private animationState?: {
        allBlocks: IAllBlocksData[];
        currentIndex: number;
        completedBlocks: number;
        frameCount: number;
        frameInterval: number;
        animationStartTime: number;
        isRunning: boolean;
    };

    // 延迟任务队列
    private delayedTasks: Array<{
        executeTime: number;
        callback: () => void;
        description: string;
    }> = [];

    init(): void {
        // 监听多层棋盘块创建完成事件
        this.world.eventBus.on(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_CREATED, this.onMultiBoardBlocksCreated, this);
    }

    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_CREATED, this.onMultiBoardBlocksCreated, this);
        // 清理状态
        this.animationState = undefined;
        this.delayedTasks = [];
        this.activeAnimations = [];
        this.componentCache.clear();
        this.spatialHashMap.clear();
    }

    update(dt: number): void {
        // 处理延迟任务
        this.processDelayedTasks();

        // 处理动画状态
        if (this.animationState && this.animationState.isRunning) {
            this.updateAnimation();
        }
    }

    /**
     * 处理延迟任务队列
     */
    private processDelayedTasks(): void {
        const currentTime = Date.now();

        // 从后往前遍历，便于删除已执行的任务
        for (let i = this.delayedTasks.length - 1; i >= 0; i--) {
            const task = this.delayedTasks[i];
            if (currentTime >= task.executeTime) {
                task.callback();
                this.delayedTasks.splice(i, 1);
            }
        }
    }

    /**
     * 添加延迟任务
     */
    private addDelayedTask(delayMs: number, callback: () => void, description: string): void {
        this.delayedTasks.push({
            executeTime: Date.now() + delayMs,
            callback,
            description,
        });
    }

    /**
     * 更新动画状态，包含cell动画处理
     */
    private updateAnimation(): void {
        if (!this.animationState) return;
        // 更新cell动画
        this.updateCellAnimations();
        const state = this.animationState;
        if (!state) return;

        state.frameCount++;

        // 检查是否到了发牌的时机
        if (state.frameCount >= state.frameInterval && state.currentIndex < state.allBlocks.length) {
            const blockInfo = state.allBlocks[state.currentIndex];
            const spiralIndex = state.currentIndex;
            this.startBlockMovementWithCardEffect(blockInfo, spiralIndex, state.allBlocks.length, () => {
                if (!this.animationState) return;
                this.animationState.completedBlocks++;
                // 当每个块移动完成后，立即进行实时遮挡检测
                const occlusionStart = performance.now();
                this.checkOcclusionForPosition(blockInfo);
                const occlusionEnd = performance.now();

                // 记录遮挡检测时间
                if (!this.occlusionStats) {
                    this.occlusionStats = { count: 0, totalTime: 0, maxTime: 0 };
                }
                const occlusionTime = occlusionEnd - occlusionStart;
                this.occlusionStats.count++;
                this.occlusionStats.totalTime += occlusionTime;
                this.occlusionStats.maxTime = Math.max(this.occlusionStats.maxTime, occlusionTime);

                // 当所有块移动完成后，发送事件通知MultiBoardSystem执行冒泡
                if (this.animationState && this.animationState.completedBlocks === this.animationState.allBlocks.length) {
                    this.animationState = undefined;
                    // 延迟发送完成事件
                    this.addDelayedTask(
                        50,
                        () => {
                            this.world.eventBus.emit(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_DEPLOYED);
                        },
                        '发送多层棋盘块部署完成事件',
                    );
                }
            });
            state.currentIndex++;
            state.frameCount = 0; // 重置帧计数
        }
    }

    /**
     * 多层棋盘块创建完成事件处理器
     */
    private onMultiBoardBlocksCreated(): void {
        const boards: IBoardConfig[] = this.world.utilCenter.boardUtil.getBoardConfigs();
        const allLayersBlocks = this.collectAllLayersBlocksInfo(boards);
        this.startMovingBlocksByLayers(boards, allLayersBlocks);
    }

    /**
     * 收集所有层级的块信息
     */
    private collectAllLayersBlocksInfo(boards: IBoardConfig[]): ILayerBlocksData[] {
        const allLayersBlocks: ILayerBlocksData[] = [];
        const boardEntities = this.world.query([BoardComponent]);

        // 按zIndex排序，确保层级顺序正确
        boardEntities.sort((a, b) => {
            const nodeA = this.world.getComponent(a, NodeComponent);
            const nodeB = this.world.getComponent(b, NodeComponent);
            return (nodeA.zIndex || 0) - (nodeB.zIndex || 0); // 从低到高排序（最下层最先）
        });

        // 为每个棋盘实体找到对应的配置并收集块信息
        for (let i = 0; i < boardEntities.length; i++) {
            const boardEntity = boardEntities[i];
            const boardComp = this.world.getComponent(boardEntity, BoardComponent);
            if (!boardComp) continue;

            // 根据boardId找到对应的配置
            const cfg = boards.find((config) => config.boardId === boardComp.boardId);
            if (!cfg) {
                console.warn(`未找到boardId为${boardComp.boardId}的配置`);
                continue;
            }

            const layerBlocks = this.collectBlocksForBoard(cfg, boardEntity);
            if (layerBlocks.length > 0) {
                allLayersBlocks.push({
                    layerIndex: boards.length - 1 - i, // 保持正确的layerIndex
                    blocks: layerBlocks,
                });
            }
        }

        return allLayersBlocks;
    }

    /**
     * 收集单个棋盘的块信息
     */
    private collectBlocksForBoard(cfg: IBoardConfig, boardEntity: number): IBlockInfo[] {
        const blocksToGenerate: IBlockInfo[] = [];
        const boardCenter = { x: cfg.startPos.x, y: cfg.startPos.y };
        const positions = this.getPositionsInClockwiseOrder(cfg.rows, cfg.cols);
        for (const { r, c } of positions) {
            const slotEntity = this.world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            if (!slotEntity) continue;
            const slotComp = this.world.getComponent(slotEntity, SlotComponent);
            if (!slotComp || this.world.getTargets(slotComp.entity, RelationName.SLOT_CELL).length === 0) continue;
            // 计算目标位置
            const targetPosition = this.calculateCellWorldPosition(cfg, r, c);
            // 创建块信息
            const blockInfo: IBlockInfo = {
                slotEntity,
                cellConfigs: [],
                boardCenter,
                targetPosition,
                boardEntity,
                rc: { r, c },
            };
            blocksToGenerate.push(blockInfo);
        }
        return blocksToGenerate;
    }

    /**
     * 获取顺时针方向的位置序列
     */
    private getPositionsInClockwiseOrder(rows: number, cols: number): Array<IRowCol> {
        const positions: Array<IRowCol> = [];
        if (rows === 0 || cols === 0) return positions;
        let top = 0,
            bottom = rows - 1;
        let left = 0,
            right = cols - 1;
        while (top <= bottom && left <= right) {
            // 第一步：从左到右（上边）
            for (let c = left; c <= right; c++) {
                positions.push({ r: top, c });
            }
            top++;
            // 第二步：从上到下（右边）
            for (let r = top; r <= bottom; r++) {
                positions.push({ r, c: right });
            }
            right--;
            // 第三步：从右到左（下边）- 只有当还有行时
            if (top <= bottom) {
                for (let c = right; c >= left; c--) {
                    positions.push({ r: bottom, c });
                }
                bottom--;
            }
            // 第四步：从下到上（左边）- 只有当还有列时
            if (left <= right) {
                for (let r = bottom; r >= top; r--) {
                    positions.push({ r, c: left });
                }
                left++;
            }
        }
        return positions;
    }

    /**
     * 计算cell的世界坐标位置
     */
    private calculateCellWorldPosition(cfg: IBoardConfig, row: number, col: number): IPoint {
        const cellSize = cfg.cellSize;
        const boardCenterX = cfg.startPos.x;
        const boardCenterY = cfg.startPos.y;

        // 计算格子相对于棋盘中心的偏移
        const offsetX = (col - (cfg.cols - 1) / 2) * cellSize;
        const offsetY = ((cfg.rows - 1) / 2 - row) * cellSize;

        return {
            x: boardCenterX + offsetX,
            y: boardCenterY + offsetY,
        };
    }

    /**
     * 按层级顺序逐个移动块到位置
     */
    private startMovingBlocksByLayers(boards: IBoardConfig[], allLayersBlocks: ILayerBlocksData[]): void {
        const allBlocks: IAllBlocksData[] = [];
        let globalIndex = 0;
        for (const layerData of allLayersBlocks) {
            for (const block of layerData.blocks) {
                allBlocks.push({
                    ...block,
                    globalIndex,
                    layerIndex: layerData.layerIndex,
                });
                globalIndex++;
            }
        }
        this.startContinuousBlockMovement(allBlocks);
    }

    /**
     * 开始连续发牌所有块（跨层级无停顿）
     */
    private startContinuousBlockMovement(allBlocks: IAllBlocksData[]): void {
        this.animationState = {
            allBlocks,
            currentIndex: 0,
            completedBlocks: 0,
            frameCount: 0,
            frameInterval: 1, // 每帧发一张牌
            animationStartTime: performance.now(),
            isRunning: true,
        };
    }

    /**
     * 开始带发牌效果的块移动
     */
    private startBlockMovementWithCardEffect(blockInfo: IBlockInfo, spiralIndex: number, totalBlocks: number, onComplete: () => void): void {
        const { slotEntity, targetPosition } = blockInfo;
        const slotComp = this.world.getComponent(slotEntity, SlotComponent);

        if (!slotComp || this.world.getTargets(slotEntity, RelationName.SLOT_CELL).length === 0) {
            if (onComplete) {
                onComplete();
            }
            return;
        }

        // 移动该slot中的所有cell
        let completedCells = 0;
        const layerEntitys = this.world.getTargets(slotEntity, RelationName.SLOT_CELL);
        const totalCells = layerEntitys.length;

        for (const cellEntity of layerEntitys) {
            this.animateCellToTargetWithCardEffect(cellEntity, targetPosition, spiralIndex, totalBlocks, () => {
                completedCells++;
                if (completedCells === totalCells) {
                    if (onComplete) {
                        onComplete();
                    }
                }
            });
        }
    }

    /**
     * 动画cell移动到目标位置
     */
    private animateCellToTargetWithCardEffect(
        cellEntity: number,
        targetPosition: IPoint,
        spiralIndex: number,
        totalBlocks: number,
        onComplete: () => void,
    ): void {
        const nodeComp = this.getCachedComponent(cellEntity, NodeComponent);
        if (!nodeComp) {
            if (onComplete) {
                onComplete();
            }
            return;
        }

        const startPos = { x: nodeComp.x, y: nodeComp.y };
        const duration = 160;
        const startTime = Date.now();

        // 计算弧度路径参数
        const deltaX = targetPosition.x - startPos.x;
        const deltaY = targetPosition.y - startPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // 根据螺旋位置和距离计算弧度强度
        const spiralRatio = spiralIndex / Math.max(totalBlocks - 1, 1);
        const baseArcStrength = Math.min(distance * 0.4, 120);
        const arcStrength = baseArcStrength * (0.8 + spiralRatio * 0.4);

        // 计算顺时针方向的控制点
        const midX = startPos.x + deltaX * 0.4;
        const midY = startPos.y + deltaY * 0.4;

        // 垂直向量（顺时针旋转90度）
        const perpX = -deltaY / distance;
        const perpY = deltaX / distance;

        // 控制点（向右偏移，形成顺时针弧度）
        const controlX = midX + perpX * arcStrength;
        const controlY = midY + perpY * arcStrength;

        // 创建动画任务数据
        const animationData = {
            cellEntity,
            startPos,
            targetPosition,
            controlX,
            controlY,
            startTime,
            duration,
            onComplete,
            isActive: true,
        };

        this.addActiveAnimation(animationData);
    }

    // 活跃动画列表
    private activeAnimations: Array<{
        cellEntity: number;
        startPos: IPoint;
        targetPosition: IPoint;
        controlX: number;
        controlY: number;
        startTime: number;
        duration: number;
        onComplete: () => void;
        isActive: boolean;
    }> = [];

    /**
     * 添加活跃动画
     */
    private addActiveAnimation(animationData: any): void {
        this.activeAnimations.push(animationData);
    }

    /**
     * 更新所有cell动画
     */
    private updateCellAnimations(): void {
        const currentTime = Date.now();
        // 从后往前遍历，便于删除完成的动画
        for (let i = this.activeAnimations.length - 1; i >= 0; i--) {
            const anim = this.activeAnimations[i];
            if (!anim || !anim.isActive) {
                // 清理无效的动画
                this.activeAnimations.splice(i, 1);
                continue;
            }
            const elapsed = currentTime - anim.startTime;
            const progress = Math.min(elapsed / anim.duration, 1);
            // 使用平滑的缓出效果
            const easeProgress = this.easeOutCubic(progress);
            // 二次贝塞尔曲线计算
            const t = easeProgress;
            const oneMinusT = 1 - t;
            const currentX = oneMinusT * oneMinusT * anim.startPos.x + 2 * oneMinusT * t * anim.controlX + t * t * anim.targetPosition.x;
            const currentY = oneMinusT * oneMinusT * anim.startPos.y + 2 * oneMinusT * t * anim.controlY + t * t * anim.targetPosition.y;
            const nodeComp = this.getCachedComponent(anim.cellEntity, NodeComponent);
            if (nodeComp) {
                nodeComp.x = currentX;
                nodeComp.y = currentY;
            }
            if (progress >= 1) {
                if (nodeComp) {
                    nodeComp.x = anim.targetPosition.x;
                    nodeComp.y = anim.targetPosition.y;
                }
                if (anim.onComplete) {
                    anim.onComplete();
                }
                anim.isActive = false;
                this.activeAnimations.splice(i, 1);
            }
        }
    }

    /**
     * 缓出三次方缓动函数
     */
    private easeOutCubic(t: number): number {
        return 1 - Math.pow(1 - t, 3);
    }

    /**
     * 检查指定位置的实时遮挡情况
     */
    private checkOcclusionForPosition(blockInfo: IAllBlocksData): void {
        const targetPos = blockInfo.targetPosition;
        const currentLayerIndex = blockInfo.layerIndex;
        const boards: IBoardConfig[] = this.world.utilCenter.boardUtil.getBoardConfigs();
        // 根据blockInfo中的boardEntity获取对应的配置
        const boardComp = this.world.getComponent(blockInfo.boardEntity, BoardComponent);
        if (!boardComp) return;

        const currentBoardConfig = boards.find((config) => config.boardId === boardComp.boardId);
        if (!currentBoardConfig) {
            console.warn(`未找到boardId为${boardComp.boardId}的配置`);
            return;
        }
        const upperBlockRect: IRectangle = {
            x: targetPos.x - currentBoardConfig.cellSize / 2,
            y: targetPos.y - currentBoardConfig.cellSize / 2,
            width: currentBoardConfig.cellSize,
            height: currentBoardConfig.cellSize,
        };
        // 添加到空间分区
        this.addToSpatialHash(blockInfo.slotEntity, upperBlockRect, currentLayerIndex);
        // 使用空间分区快速查找可能重叠的块
        const potentialOverlaps = this.getSpatialHashCandidates(upperBlockRect);
        const occludedCells: number[] = [];
        for (const candidate of potentialOverlaps) {
            // 只检查下层（layerIndex更大的）
            if (candidate.layerIndex <= currentLayerIndex) continue;
            // 检查矩形重叠
            if (this.isRectOverlap(upperBlockRect, candidate.rect)) {
                // 获取被遮挡的cell实体
                const cellEntities = this.getCellEntitiesFromCandidate(candidate);
                occludedCells.push(...cellEntities);
            }
        }

        if (occludedCells.length > 0) {
            this.addOcclusionMask(occludedCells);
        }
    }

    /**
     * 添加实体到空间分区哈希表
     */
    private addToSpatialHash(entity: number, rect: IRectangle, layerIndex: number): void {
        const minGridX = Math.floor(rect.x / this.SPATIAL_GRID_SIZE);
        const maxGridX = Math.floor((rect.x + rect.width) / this.SPATIAL_GRID_SIZE);
        const minGridY = Math.floor(rect.y / this.SPATIAL_GRID_SIZE);
        const maxGridY = Math.floor((rect.y + rect.height) / this.SPATIAL_GRID_SIZE);
        const entityData = { entity, rect, layerIndex };
        for (let gridX = minGridX; gridX <= maxGridX; gridX++) {
            for (let gridY = minGridY; gridY <= maxGridY; gridY++) {
                const gridKey = `${gridX},${gridY}`;
                let gridEntities = this.spatialHashMap.get(gridKey);
                if (!gridEntities) {
                    gridEntities = [];
                    this.spatialHashMap.set(gridKey, gridEntities);
                }
                gridEntities.push(entityData);
            }
        }
    }

    /**
     * 从空间分区获取潜在重叠的候选者
     */
    private getSpatialHashCandidates(rect: IRectangle): Array<{ entity: number; rect: IRectangle; layerIndex: number }> {
        const candidates: Array<{ entity: number; rect: IRectangle; layerIndex: number }> = [];
        const seenEntities = new Set<number>();
        // 计算查询矩形覆盖的网格范围
        const minGridX = Math.floor(rect.x / this.SPATIAL_GRID_SIZE);
        const maxGridX = Math.floor((rect.x + rect.width) / this.SPATIAL_GRID_SIZE);
        const minGridY = Math.floor(rect.y / this.SPATIAL_GRID_SIZE);
        const maxGridY = Math.floor((rect.y + rect.height) / this.SPATIAL_GRID_SIZE);
        // 从相关网格收集候选者
        for (let gridX = minGridX; gridX <= maxGridX; gridX++) {
            for (let gridY = minGridY; gridY <= maxGridY; gridY++) {
                const gridKey = `${gridX},${gridY}`;
                const gridEntities = this.spatialHashMap.get(gridKey);
                if (gridEntities) {
                    for (const entityData of gridEntities) {
                        if (!seenEntities.has(entityData.entity)) {
                            seenEntities.add(entityData.entity);
                            candidates.push(entityData);
                        }
                    }
                }
            }
        }
        return candidates;
    }

    /**
     * 从候选者获取cell实体列表
     */
    private getCellEntitiesFromCandidate(candidate: { entity: number; rect: IRectangle; layerIndex: number }): number[] {
        const slotComp = this.getCachedComponent(candidate.entity, SlotComponent);
        return [...this.world.getTargets(slotComp.entity, RelationName.SLOT_CELL)];
    }

    /**
     * 检查两个矩形是否重叠
     */
    private isRectOverlap(rect1: IRectangle, rect2: IRectangle): boolean {
        const noOverlap =
            rect1.x + rect1.width <= rect2.x || // rect1在rect2左边
            rect1.x >= rect2.x + rect2.width || // rect1在rect2右边
            rect1.y + rect1.height <= rect2.y || // rect1在rect2上边
            rect1.y >= rect2.y + rect2.height; // rect1在rect2下边

        return !noOverlap;
    }

    /**
     * 为被遮挡的cell添加遮罩
     */
    private addOcclusionMask(cellEntities: number[]): void {
        for (let i = 0; i < cellEntities.length; i++) {
            const cellEntity = cellEntities[i];
            // 获取cell的位置信息
            const cellNodeComp = this.world.getComponent(cellEntity, NodeComponent);
            if (!cellNodeComp) {
                continue;
            }
            const boardEntity = this.world.getSource(cellEntity, RelationName.PARENT_CHILD);
            if (!boardEntity) {
                continue;
            }
            const slotEntity = this.findSlotForCell(cellEntity);
            if (!slotEntity) {
                continue;
            }
            // 检查该slot是否已经有遮罩
            if (this.hasOcclusionMask(slotEntity)) {
                continue;
            }
            const maskEntity = this.world.createEntity();
            this.world.addRelation(boardEntity, maskEntity, RelationName.PARENT_CHILD);
            // 设置遮罩位置：与cell相同的位置
            const maskNodeComp = this.world.addComponent(maskEntity, NodeComponent, {
                x: cellNodeComp.x,
                y: cellNodeComp.y,
            });
            maskNodeComp.zIndex = (cellNodeComp.zIndex || 0) + 0.1;
            // 添加渲染组件 - 使用遮罩预制体
            this.world.addComponent(maskEntity, RenderComponent, 'ecsview_cell_mask', { childrenPaths: [BoardLayer[BoardLayer.Element]] });
            // 设置遮罩透明度
            const opacityComp = this.world.addComponent(maskEntity, OpacityComponent);
            opacityComp.opacity = 180;
            this.world.addRelation(slotEntity, maskEntity, RelationName.SLOT_MASK);
        }
    }

    /**
     * 找到cell所属的slot
     */
    private findSlotForCell(cellEntity: number): number | null {
        const cell = this.world.getComponent(cellEntity, CellComponent);
        const boardEntity = this.world.getSource(cellEntity, RelationName.PARENT_CHILD);
        return this.world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${cell.r}_${cell.c}`)[0];
    }

    /**
     * 检查slot是否已经有遮罩
     */
    private hasOcclusionMask(slotEntity: number): boolean {
        const maskEntities = this.world.getTargets(slotEntity, RelationName.SLOT_MASK);
        return maskEntities && maskEntities.length > 0;
    }

    /**
     * 缓存组件查询，避免重复查询
     */
    private getCachedComponent<T extends Component>(entity: number, componentType: new (...args: any[]) => T): T | null {
        // 使用组件类型名称作为缓存键
        const componentName = componentType.name;
        // 检查缓存
        let entityCache = this.componentCache.get(entity);
        if (!entityCache) {
            entityCache = new Map();
            this.componentCache.set(entity, entityCache);
        }
        let cachedComponent = entityCache.get(componentName);
        if (cachedComponent) {
            return cachedComponent as T;
        }
        // 缓存未命中，查询并缓存结果
        const component = this.world.getComponent(entity, componentType);
        if (component) {
            entityCache.set(componentName, component);
            return component;
        }
        return null;
    }
}
